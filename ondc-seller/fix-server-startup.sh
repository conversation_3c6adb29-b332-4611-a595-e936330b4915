#!/bin/bash

echo "🔧 ONDC Seller Frontend Server Startup Fix"
echo "=========================================="

# Function to check if a port is in use
check_port() {
    local port=$1
    if ss -tlnp | grep ":$port" > /dev/null; then
        echo "✅ Port $port is in use"
        return 0
    else
        echo "❌ Port $port is not in use"
        return 1
    fi
}

# Function to kill processes on a port
kill_port() {
    local port=$1
    echo "🔪 Killing processes on port $port..."
    pkill -f ":$port" 2>/dev/null || true
    sleep 2
}

echo ""
echo "📋 **STEP 1: Environment Cleanup**"
echo "=================================="

# Kill any existing Next.js or Strapi processes
kill_port 3000
kill_port 3001
kill_port 1339

# Clean npm cache
echo "🧹 Cleaning npm cache..."
npm cache clean --force

echo ""
echo "📋 **STEP 2: Strapi CMS Startup**"
echo "================================="

cd packages/cms-strapi

# Check if Strapi dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/@strapi" ]; then
    echo "📦 Installing Strapi dependencies..."
    npm install --force
fi

# Try different methods to start Strapi
echo "🚀 Starting Strapi CMS..."

# Method 1: Try local binary
if [ -f "node_modules/.bin/strapi" ]; then
    echo "Using local Strapi binary..."
    node_modules/.bin/strapi develop &
    STRAPI_PID=$!
elif [ -f "../../node_modules/.bin/strapi" ]; then
    echo "Using root Strapi binary..."
    ../../node_modules/.bin/strapi develop &
    STRAPI_PID=$!
else
    echo "Using npx with specific version..."
    npx @strapi/strapi@5.13.0 develop &
    STRAPI_PID=$!
fi

# Wait for Strapi to start
echo "⏳ Waiting for Strapi CMS to start..."
for i in {1..30}; do
    if check_port 1339; then
        echo "✅ Strapi CMS started successfully on port 1339"
        break
    fi
    echo "   Waiting... ($i/30)"
    sleep 2
done

if ! check_port 1339; then
    echo "❌ Strapi CMS failed to start"
    echo "💡 Manual start command: cd packages/cms-strapi && npx @strapi/strapi@5.13.0 develop"
fi

echo ""
echo "📋 **STEP 3: Frontend Server Startup**"
echo "======================================"

cd ../frontend

# Check if frontend dependencies are installed
if [ ! -d "node_modules" ] || [ ! -f "node_modules/next/package.json" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install --force
fi

# Try different methods to start Next.js
echo "🚀 Starting Next.js frontend..."

# Method 1: Try using root node_modules Next.js
if [ -f "../../node_modules/next/dist/bin/next" ]; then
    echo "Using root Next.js binary..."
    node ../../node_modules/next/dist/bin/next dev &
    NEXTJS_PID=$!
elif [ -f "node_modules/next/dist/bin/next" ]; then
    echo "Using local Next.js binary..."
    node node_modules/next/dist/bin/next dev &
    NEXTJS_PID=$!
else
    echo "Using npx with specific version..."
    npx next@14.2.29 dev &
    NEXTJS_PID=$!
fi

# Wait for Next.js to start
echo "⏳ Waiting for Next.js to start..."
for i in {1..20}; do
    if check_port 3000 || check_port 3001; then
        if check_port 3000; then
            echo "✅ Next.js started successfully on port 3000"
            FRONTEND_URL="http://localhost:3000"
        else
            echo "✅ Next.js started successfully on port 3001"
            FRONTEND_URL="http://localhost:3001"
        fi
        break
    fi
    echo "   Waiting... ($i/20)"
    sleep 2
done

echo ""
echo "📋 **STEP 4: System Status Check**"
echo "=================================="

echo "🔍 Checking system status..."

# Check Strapi CMS
if check_port 1339; then
    echo "✅ Strapi CMS: Running on http://localhost:1339"
    # Test Strapi API
    if curl -s "http://localhost:1339/api/product-categories" > /dev/null; then
        echo "✅ Strapi API: Responding"
    else
        echo "⚠️ Strapi API: Not responding yet (may still be starting)"
    fi
else
    echo "❌ Strapi CMS: Not running"
fi

# Check Frontend
if check_port 3000 || check_port 3001; then
    echo "✅ Frontend: Running on $FRONTEND_URL"
    # Test Frontend API
    if curl -s "$FRONTEND_URL/api/categories" > /dev/null; then
        echo "✅ Frontend API: Responding"
    else
        echo "⚠️ Frontend API: Not responding yet (may still be starting)"
    fi
else
    echo "❌ Frontend: Not running"
fi

echo ""
echo "📋 **STEP 5: Testing Category Hierarchy**"
echo "========================================"

if [ -n "$FRONTEND_URL" ]; then
    echo "🧪 Testing category hierarchy endpoints..."
    
    # Test parent categories
    if curl -s "$FRONTEND_URL/api/categories?parentOnly=true&pageSize=5" | grep -q "success"; then
        echo "✅ Parent categories API: Working"
    else
        echo "❌ Parent categories API: Failed"
    fi
    
    # Test subcategories
    if curl -s "$FRONTEND_URL/api/subcategories/1" | grep -q "success"; then
        echo "✅ Subcategories API: Working"
    else
        echo "❌ Subcategories API: Failed"
    fi
    
    # Test category detail
    if curl -s "$FRONTEND_URL/api/categories/electronics" | grep -q "success"; then
        echo "✅ Category detail API: Working"
    else
        echo "❌ Category detail API: Failed"
    fi
fi

echo ""
echo "🎉 **STARTUP COMPLETE**"
echo "======================"

if check_port 1339 && (check_port 3000 || check_port 3001); then
    echo "✅ Both Strapi CMS and Frontend are running!"
    echo ""
    echo "🌐 **Access URLs:**"
    echo "   Frontend: $FRONTEND_URL"
    echo "   Strapi Admin: http://localhost:1339/admin"
    echo ""
    echo "🧪 **Test Category Hierarchy:**"
    echo "   Homepage: $FRONTEND_URL"
    echo "   Categories: $FRONTEND_URL/categories"
    echo "   Category Detail: $FRONTEND_URL/categories/electronics"
    echo ""
    echo "📊 **API Endpoints:**"
    echo "   Categories: $FRONTEND_URL/api/categories?parentOnly=true"
    echo "   Subcategories: $FRONTEND_URL/api/subcategories/1"
    echo "   Category Detail: $FRONTEND_URL/api/categories/electronics"
else
    echo "❌ Startup incomplete. Check the logs above for issues."
    echo ""
    echo "🔧 **Manual Commands:**"
    echo "   Strapi: cd packages/cms-strapi && npx @strapi/strapi@5.13.0 develop"
    echo "   Frontend: cd packages/frontend && node ../../node_modules/next/dist/bin/next dev"
fi

echo ""
echo "📝 **Process IDs (for stopping later):**"
echo "   Strapi PID: $STRAPI_PID"
echo "   Next.js PID: $NEXTJS_PID"
echo ""
echo "🛑 **To stop servers:**"
echo "   kill $STRAPI_PID $NEXTJS_PID"
echo "   or use: pkill -f 'strapi\|next'"
