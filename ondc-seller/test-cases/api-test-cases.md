# API Test Cases for E-Commerce Platform

## API Testing Overview
Comprehensive API test cases for ONDC Seller Platform backend services.

---

## 1. Authentication API Tests

### 1.1 User Registration API
**Test Case ID**: TC_API_AUTH_001  
**Endpoint**: `POST /auth/register`  
**Priority**: High  

**Test Scenario**: Register user with valid data  

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "Test@123456",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Expected Response**:
- Status Code: 201
- Response Body:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

---

**Test Case ID**: TC_API_AUTH_002  
**Endpoint**: `POST /auth/register`  
**Priority**: High  

**Test Scenario**: Register user with duplicate email  

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "Test@123456",
  "firstName": "<PERSON>",
  "lastName": "Doe"
}
```

**Expected Response**:
- Status Code: 409
- Response Body:
```json
{
  "success": false,
  "error": "Email already exists",
  "code": "DUPLICATE_EMAIL"
}
```

---

### 1.2 User Login API
**Test Case ID**: TC_API_AUTH_003  
**Endpoint**: `POST /auth/login`  
**Priority**: High  

**Test Scenario**: Login with valid credentials  

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "Test@123456"
}
```

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    }
  }
}
```

---

**Test Case ID**: TC_API_AUTH_004  
**Endpoint**: `POST /auth/login`  
**Priority**: High  

**Test Scenario**: Login with invalid credentials  

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}
```

**Expected Response**:
- Status Code: 401
- Response Body:
```json
{
  "success": false,
  "error": "Invalid credentials",
  "code": "INVALID_CREDENTIALS"
}
```

---

## 2. Product API Tests

### 2.1 Get Products API
**Test Case ID**: TC_API_PROD_001  
**Endpoint**: `GET /store/products`  
**Priority**: High  

**Test Scenario**: Get products list with pagination  

**Query Parameters**:
- limit: 10
- offset: 0

**Headers**:
- x-publishable-api-key: pk_test_123

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "products": [
    {
      "id": "prod_123",
      "title": "Sample Product",
      "description": "Product description",
      "handle": "sample-product",
      "status": "published",
      "images": [
        {
          "url": "https://example.com/image.jpg"
        }
      ],
      "variants": [
        {
          "id": "variant_123",
          "title": "Default Variant",
          "prices": [
            {
              "amount": 1999,
              "currency_code": "INR"
            }
          ]
        }
      ]
    }
  ],
  "count": 1,
  "offset": 0,
  "limit": 10
}
```

---

**Test Case ID**: TC_API_PROD_002  
**Endpoint**: `GET /store/products/{id}`  
**Priority**: High  

**Test Scenario**: Get single product by ID  

**Path Parameters**:
- id: prod_123

**Headers**:
- x-publishable-api-key: pk_test_123

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "product": {
    "id": "prod_123",
    "title": "Sample Product",
    "description": "Detailed product description",
    "handle": "sample-product",
    "status": "published",
    "images": [...],
    "variants": [...],
    "options": [...],
    "tags": [...]
  }
}
```

---

**Test Case ID**: TC_API_PROD_003  
**Endpoint**: `GET /store/products/{id}`  
**Priority**: Medium  

**Test Scenario**: Get product with invalid ID  

**Path Parameters**:
- id: invalid_id

**Expected Response**:
- Status Code: 404
- Response Body:
```json
{
  "type": "not_found",
  "message": "Product not found"
}
```

---

### 2.2 Admin Product API
**Test Case ID**: TC_API_ADMIN_001  
**Endpoint**: `POST /admin/products`  
**Priority**: High  

**Test Scenario**: Create new product (Admin)  

**Headers**:
- Authorization: Bearer admin_token_123

**Request Body**:
```json
{
  "title": "New Product",
  "description": "Product description",
  "handle": "new-product",
  "status": "draft",
  "variants": [
    {
      "title": "Default Variant",
      "prices": [
        {
          "amount": 2999,
          "currency_code": "INR"
        }
      ],
      "inventory_quantity": 100
    }
  ]
}
```

**Expected Response**:
- Status Code: 201
- Response Body:
```json
{
  "product": {
    "id": "prod_new_123",
    "title": "New Product",
    "description": "Product description",
    "handle": "new-product",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

---

## 3. Cart API Tests

### 3.1 Create Cart API
**Test Case ID**: TC_API_CART_001  
**Endpoint**: `POST /store/carts`  
**Priority**: High  

**Test Scenario**: Create new cart  

**Headers**:
- x-publishable-api-key: pk_test_123

**Request Body**:
```json
{
  "region_id": "reg_123"
}
```

**Expected Response**:
- Status Code: 201
- Response Body:
```json
{
  "cart": {
    "id": "cart_123",
    "region_id": "reg_123",
    "items": [],
    "total": 0,
    "subtotal": 0,
    "tax_total": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

---

### 3.2 Add Item to Cart API
**Test Case ID**: TC_API_CART_002  
**Endpoint**: `POST /store/carts/{id}/line-items`  
**Priority**: High  

**Test Scenario**: Add product to cart  

**Path Parameters**:
- id: cart_123

**Headers**:
- x-publishable-api-key: pk_test_123

**Request Body**:
```json
{
  "variant_id": "variant_123",
  "quantity": 2
}
```

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "cart": {
    "id": "cart_123",
    "items": [
      {
        "id": "item_123",
        "variant_id": "variant_123",
        "quantity": 2,
        "unit_price": 1999,
        "total": 3998
      }
    ],
    "total": 3998,
    "subtotal": 3998
  }
}
```

---

## 4. Order API Tests

### 4.1 Create Order API
**Test Case ID**: TC_API_ORDER_001  
**Endpoint**: `POST /store/carts/{id}/complete`  
**Priority**: High  

**Test Scenario**: Complete cart to create order  

**Path Parameters**:
- id: cart_123

**Headers**:
- x-publishable-api-key: pk_test_123

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "type": "order",
  "data": {
    "id": "order_123",
    "status": "pending",
    "cart_id": "cart_123",
    "total": 3998,
    "items": [...],
    "shipping_address": {...},
    "billing_address": {...},
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

---

### 4.2 Get Orders API
**Test Case ID**: TC_API_ORDER_002  
**Endpoint**: `GET /admin/orders`  
**Priority**: High  

**Test Scenario**: Get orders list (Admin)  

**Headers**:
- Authorization: Bearer admin_token_123

**Query Parameters**:
- limit: 20
- offset: 0

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "orders": [
    {
      "id": "order_123",
      "status": "pending",
      "total": 3998,
      "customer": {
        "email": "<EMAIL>"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 1,
  "offset": 0,
  "limit": 20
}
```

---

## 5. Custom ONDC API Tests

### 5.1 ONDC Catalog API
**Test Case ID**: TC_API_ONDC_001  
**Endpoint**: `GET /ondc/catalog`  
**Priority**: High  

**Test Scenario**: Get ONDC formatted catalog  

**Query Parameters**:
- provider_id: provider_123
- location_id: location_123

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "catalog": {
    "descriptor": {
      "name": "ONDC Seller Catalog",
      "code": "ONDC-CAT-001"
    },
    "providers": [
      {
        "id": "provider_123",
        "descriptor": {
          "name": "Default Provider"
        },
        "items": [
          {
            "id": "prod_123",
            "descriptor": {
              "name": "Sample Product",
              "code": "sample-product"
            },
            "price": {
              "currency": "INR",
              "value": "1999"
            }
          }
        ]
      }
    ]
  }
}
```

---

### 5.2 Featured Products API
**Test Case ID**: TC_API_CUSTOM_001  
**Endpoint**: `GET /products/featured`  
**Priority**: Medium  

**Test Scenario**: Get featured products  

**Query Parameters**:
- limit: 6

**Expected Response**:
- Status Code: 200
- Response Body:
```json
{
  "products": [
    {
      "id": "featured-1",
      "title": "Featured Product 1",
      "description": "This is a featured product",
      "status": "published",
      "featured": true
    }
  ],
  "count": 1,
  "endpoint": "featured",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

## 6. Error Handling Tests

### 6.1 Authentication Errors
**Test Case ID**: TC_API_ERROR_001  
**Endpoint**: `GET /admin/products`  
**Priority**: High  

**Test Scenario**: Access admin endpoint without token  

**Headers**: (No Authorization header)

**Expected Response**:
- Status Code: 401
- Response Body:
```json
{
  "type": "unauthorized",
  "message": "Authentication required"
}
```

---

### 6.2 Validation Errors
**Test Case ID**: TC_API_ERROR_002  
**Endpoint**: `POST /auth/register`  
**Priority**: High  

**Test Scenario**: Register with invalid email format  

**Request Body**:
```json
{
  "email": "invalid-email",
  "password": "Test@123456"
}
```

**Expected Response**:
- Status Code: 400
- Response Body:
```json
{
  "type": "invalid_data",
  "message": "Invalid email format",
  "errors": [
    {
      "field": "email",
      "message": "Please provide a valid email address"
    }
  ]
}
```

---

## 7. Performance API Tests

### 7.1 Response Time Tests
**Test Case ID**: TC_API_PERF_001  
**Endpoint**: `GET /store/products`  
**Priority**: Medium  

**Test Scenario**: Products API response time  

**Performance Criteria**:
- Response time < 500ms
- Concurrent users: 100
- Duration: 5 minutes

**Expected Results**:
- Average response time < 500ms
- 95th percentile < 1000ms
- Error rate < 1%

---

### 7.2 Load Testing
**Test Case ID**: TC_API_PERF_002  
**Endpoint**: Multiple endpoints  
**Priority**: Medium  

**Test Scenario**: API load testing  

**Test Configuration**:
- Concurrent users: 500
- Ramp-up time: 2 minutes
- Test duration: 10 minutes

**API Endpoints to Test**:
- GET /store/products
- GET /store/products/{id}
- POST /store/carts
- POST /store/carts/{id}/line-items

**Expected Results**:
- System handles 500 concurrent users
- Response times remain acceptable
- No critical errors

---

## Test Automation Framework

### Tools and Technologies
- **API Testing**: Postman, Newman, Jest
- **Load Testing**: Artillery, JMeter
- **CI/CD Integration**: GitHub Actions
- **Reporting**: Allure, HTML reports

### Test Data Management
```javascript
// Test data configuration
const testData = {
  users: {
    validUser: {
      email: "<EMAIL>",
      password: "Test@123456"
    },
    adminUser: {
      email: "<EMAIL>",
      password: "Admin@123456"
    }
  },
  products: {
    sampleProduct: {
      id: "prod_123",
      title: "Sample Product"
    }
  },
  tokens: {
    publishableKey: "pk_test_123",
    adminToken: "admin_token_123"
  }
};
```

### Environment Configuration
```javascript
// Environment settings
const environments = {
  development: {
    baseUrl: "http://localhost:9000",
    publishableKey: "pk_dev_123"
  },
  staging: {
    baseUrl: "https://api-staging.ondc-seller.com",
    publishableKey: "pk_staging_123"
  },
  production: {
    baseUrl: "https://api.ondc-seller.com",
    publishableKey: "pk_live_123"
  }
};
```

---

## Execution Guidelines

### Pre-Test Setup
1. Ensure test environment is running
2. Reset test database
3. Create test user accounts
4. Verify API endpoints are accessible

### Test Execution Order
1. Authentication tests
2. Product management tests
3. Cart operations tests
4. Order processing tests
5. Custom endpoint tests
6. Error handling tests
7. Performance tests

### Post-Test Cleanup
1. Clean up test data
2. Reset user accounts
3. Clear test carts and orders
4. Generate test reports

---

*These API test cases should be automated and integrated into the CI/CD pipeline for continuous testing.*
