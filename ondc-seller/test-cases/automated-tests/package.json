{"name": "ondc-seller-api-tests", "version": "1.0.0", "description": "Automated API tests for ONDC Seller Platform", "main": "index.js", "scripts": {"test": "mocha --recursive --timeout 30000 --reporter spec", "test:auth": "mocha auth.test.js --timeout 30000 --reporter spec", "test:products": "mocha products.test.js --timeout 30000 --reporter spec", "test:cart": "mocha cart.test.js --timeout 30000 --reporter spec", "test:orders": "mocha orders.test.js --timeout 30000 --reporter spec", "test:admin": "mocha admin.test.js --timeout 30000 --reporter spec", "test:ondc": "mocha ondc.test.js --timeout 30000 --reporter spec", "test:performance": "mocha performance.test.js --timeout 60000 --reporter spec", "test:security": "mocha security.test.js --timeout 30000 --reporter spec", "test:ci": "mocha --recursive --timeout 30000 --reporter json > test-results.json", "test:html": "mocha --recursive --timeout 30000 --reporter mochaw<PERSON><PERSON>", "test:watch": "mocha --recursive --timeout 30000 --reporter spec --watch", "test:coverage": "nyc mocha --recursive --timeout 30000", "lint": "eslint *.js", "lint:fix": "eslint *.js --fix", "setup": "node setup.js", "cleanup": "node cleanup.js", "load-test": "artillery run load-test-config.yml", "smoke-test": "mocha smoke.test.js --timeout 10000 --reporter spec"}, "keywords": ["api", "testing", "e-commerce", "ondc", "automation"], "author": "ONDC Seller Platform Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chai": "^4.3.10", "mocha": "^10.2.0", "supertest": "^6.3.3", "faker": "^5.5.3", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.54.0", "nyc": "^15.1.0", "mochawesome": "^7.1.3", "artillery": "^2.0.0", "newman": "^6.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "nyc": {"reporter": ["text", "html", "lcov"], "exclude": ["node_modules/**", "coverage/**", "test/**"]}, "mocha": {"timeout": 30000, "recursive": true, "reporter": "spec"}}