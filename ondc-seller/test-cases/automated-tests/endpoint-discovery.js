#!/usr/bin/env node

/**
 * Endpoint Discovery and Testing Script
 * Tests all available endpoints and identifies failures
 */

const axios = require('axios');

const baseUrl = 'http://localhost:9000';
const publishableKey = 'pk_01HQD0GMQXVK5XJ0XS0ZNPJ8T5';

// Test endpoints to check
const endpoints = [
  // Basic endpoints
  { method: 'GET', url: '/health', name: 'Health Check', headers: {} },
  { method: 'GET', url: '/version', name: 'Version Info', headers: {} },
  
  // Store API endpoints
  { method: 'GET', url: '/store/products', name: 'Store Products', headers: { 'x-publishable-api-key': publishableKey } },
  { method: 'GET', url: '/store/regions', name: 'Store Regions', headers: { 'x-publishable-api-key': publishableKey } },
  { method: 'POST', url: '/store/carts', name: 'Create Cart', headers: { 'x-publishable-api-key': publishableKey }, data: {} },
  
  // Admin API endpoints (will fail without auth)
  { method: 'GET', url: '/admin/products', name: 'Admin Products', headers: {} },
  { method: 'GET', url: '/admin/orders', name: 'Admin Orders', headers: {} },
  { method: 'GET', url: '/admin/customers', name: 'Admin Customers', headers: {} },
  
  // Custom endpoints
  { method: 'GET', url: '/products/featured', name: 'Featured Products', headers: {} },
  { method: 'GET', url: '/ondc/catalog', name: 'ONDC Catalog', headers: {} },
  { method: 'GET', url: '/analytics/dashboard', name: 'Analytics Dashboard', headers: {} },
  
  // Authentication endpoints
  { method: 'POST', url: '/auth/register', name: 'User Registration', headers: { 'x-publishable-api-key': publishableKey }, data: { email: '<EMAIL>', password: 'Test@123' } },
  { method: 'POST', url: '/auth/login', name: 'User Login', headers: { 'x-publishable-api-key': publishableKey }, data: { email: '<EMAIL>', password: 'Test@123' } },
  { method: 'GET', url: '/auth/me', name: 'Get Current User', headers: { 'x-publishable-api-key': publishableKey } },
];

async function testEndpoint(endpoint) {
  try {
    const config = {
      method: endpoint.method,
      url: `${baseUrl}${endpoint.url}`,
      headers: {
        'Content-Type': 'application/json',
        ...endpoint.headers
      },
      timeout: 5000,
      validateStatus: () => true // Don't throw on any status code
    };

    if (endpoint.data) {
      config.data = endpoint.data;
    }

    const response = await axios(config);
    
    return {
      name: endpoint.name,
      method: endpoint.method,
      url: endpoint.url,
      status: response.status,
      statusText: response.statusText,
      success: response.status < 400,
      data: typeof response.data === 'string' ? response.data.substring(0, 200) : JSON.stringify(response.data).substring(0, 200),
      headers: Object.keys(response.headers)
    };
  } catch (error) {
    return {
      name: endpoint.name,
      method: endpoint.method,
      url: endpoint.url,
      status: 'ERROR',
      statusText: error.message,
      success: false,
      data: error.message,
      headers: []
    };
  }
}

async function runEndpointDiscovery() {
  console.log('🔍 ONDC Seller Platform - Endpoint Discovery & Testing');
  console.log('=' .repeat(60));
  console.log(`Testing against: ${baseUrl}`);
  console.log(`Publishable Key: ${publishableKey}`);
  console.log('=' .repeat(60));

  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`\n🧪 Testing: ${endpoint.name} (${endpoint.method} ${endpoint.url})`);
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    const statusColor = result.success ? '✅' : '❌';
    console.log(`   ${statusColor} Status: ${result.status} ${result.statusText}`);
    
    if (result.data && result.data.length > 0) {
      console.log(`   📄 Response: ${result.data}${result.data.length >= 200 ? '...' : ''}`);
    }
  }

  // Generate summary report
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY REPORT');
  console.log('=' .repeat(60));

  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const total = results.length;

  console.log(`\n📈 Overall Results:`);
  console.log(`   Total Tests: ${total}`);
  console.log(`   ✅ Passed: ${passed} (${((passed/total)*100).toFixed(1)}%)`);
  console.log(`   ❌ Failed: ${failed} (${((failed/total)*100).toFixed(1)}%)`);

  // Categorize failures
  const failures = results.filter(r => !r.success);
  const failuresByStatus = {};
  
  failures.forEach(failure => {
    const status = failure.status;
    if (!failuresByStatus[status]) {
      failuresByStatus[status] = [];
    }
    failuresByStatus[status].push(failure);
  });

  console.log(`\n🔍 Failure Analysis:`);
  Object.keys(failuresByStatus).forEach(status => {
    const count = failuresByStatus[status].length;
    console.log(`\n   ${status} Errors (${count} tests):`);
    failuresByStatus[status].forEach(failure => {
      console.log(`     • ${failure.name}: ${failure.method} ${failure.url}`);
    });
  });

  // Working endpoints
  const working = results.filter(r => r.success);
  if (working.length > 0) {
    console.log(`\n✅ Working Endpoints (${working.length}):`);
    working.forEach(endpoint => {
      console.log(`   • ${endpoint.name}: ${endpoint.method} ${endpoint.url} (${endpoint.status})`);
    });
  }

  // Recommendations
  console.log(`\n💡 Recommendations:`);
  
  const notFoundCount = failuresByStatus['404'] ? failuresByStatus['404'].length : 0;
  const unauthorizedCount = failuresByStatus['401'] ? failuresByStatus['401'].length : 0;
  const errorCount = failuresByStatus['ERROR'] ? failuresByStatus['ERROR'].length : 0;

  if (notFoundCount > 0) {
    console.log(`   🔧 ${notFoundCount} endpoints return 404 - Check if routes are properly configured`);
  }
  
  if (unauthorizedCount > 0) {
    console.log(`   🔐 ${unauthorizedCount} endpoints return 401 - Authentication/authorization needed`);
  }
  
  if (errorCount > 0) {
    console.log(`   ⚠️  ${errorCount} endpoints have connection errors - Check if services are running`);
  }

  // Save results to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = `endpoint-discovery-report-${timestamp}.json`;
  
  const report = {
    timestamp: new Date().toISOString(),
    baseUrl,
    summary: {
      total,
      passed,
      failed,
      passRate: ((passed/total)*100).toFixed(1)
    },
    results,
    failuresByStatus
  };

  require('fs').writeFileSync(reportFile, JSON.stringify(report, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportFile}`);

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Endpoint Discovery Complete');
  console.log('=' .repeat(60));

  return report;
}

// Run the discovery
runEndpointDiscovery().catch(console.error);
