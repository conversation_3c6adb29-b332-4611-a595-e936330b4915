{"timestamp": "2025-06-06T10:08:21.474Z", "baseUrl": "http://localhost:9000", "summary": {"total": 14, "passed": 10, "failed": 4, "passRate": "71.4"}, "results": [{"name": "Health Check", "method": "GET", "url": "/health", "status": 200, "statusText": "OK", "success": true, "data": "OK", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Version Info", "method": "GET", "url": "/version", "status": 200, "statusText": "OK", "success": true, "data": "{\"version\":\"1.0.0\",\"name\":\"ONDC Seller Platform\",\"timestamp\":\"2025-06-06T10:08:21.411Z\",\"environment\":\"development\",\"endpoints\":8}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Store Products", "method": "GET", "url": "/store/products", "status": 200, "statusText": "OK", "success": true, "data": "{\"products\":[{\"id\":\"prod_1\",\"title\":\"Sample Laptop\",\"description\":\"High-performance laptop for development\",\"status\":\"published\",\"handle\":\"sample-laptop\",\"variants\":[{\"id\":\"variant_1\",\"title\":\"Default", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Store Regions", "method": "GET", "url": "/store/regions", "status": 200, "statusText": "OK", "success": true, "data": "{\"regions\":[{\"id\":\"reg_india\",\"name\":\"India\",\"currency_code\":\"INR\",\"countries\":[{\"iso_2\":\"IN\",\"display_name\":\"India\"}]}]}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Create <PERSON><PERSON>", "method": "POST", "url": "/store/carts", "status": 201, "statusText": "Created", "success": true, "data": "{\"cart\":{\"id\":\"id_5kygia0gu\",\"region_id\":\"reg_india\",\"items\":[],\"total\":0,\"subtotal\":0,\"tax_total\":0,\"created_at\":\"2025-06-06T10:08:21.425Z\"}}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Products", "method": "GET", "url": "/admin/products", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Orders", "method": "GET", "url": "/admin/orders", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Customers", "method": "GET", "url": "/admin/customers", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Featured Products", "method": "GET", "url": "/products/featured", "status": 200, "statusText": "OK", "success": true, "data": "{\"products\":[{\"id\":\"prod_1\",\"title\":\"Sample Laptop\",\"description\":\"High-performance laptop for development\",\"status\":\"published\",\"handle\":\"sample-laptop\",\"variants\":[{\"id\":\"variant_1\",\"title\":\"Default", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "ONDC Catalog", "method": "GET", "url": "/ondc/catalog", "status": 200, "statusText": "OK", "success": true, "data": "{\"catalog\":{\"descriptor\":{\"name\":\"ONDC Seller Catalog\",\"code\":\"ONDC-CAT-001\"},\"providers\":[{\"id\":\"provider_123\",\"descriptor\":{\"name\":\"Default Provider\"},\"items\":[{\"id\":\"prod_1\",\"descriptor\":{\"name\":\"S", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Analytics Dashboard", "method": "GET", "url": "/analytics/dashboard", "status": 200, "statusText": "OK", "success": true, "data": "{\"sales\":{\"total\":125000,\"thisMonth\":25000,\"growth\":15.5},\"orders\":{\"total\":450,\"pending\":12,\"completed\":438},\"products\":{\"total\":2,\"active\":2,\"outOfStock\":0},\"timestamp\":\"2025-06-06T10:08:21.463Z\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "User Registration", "method": "POST", "url": "/auth/register", "status": 201, "statusText": "Created", "success": true, "data": "{\"success\":true,\"message\":\"User registered successfully\",\"data\":{\"id\":\"id_rp1mxm195\",\"email\":\"<EMAIL>\",\"firstName\":\"\",\"lastName\":\"\",\"createdAt\":\"2025-06-06T10:08:21.465Z\"}}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "User Login", "method": "POST", "url": "/auth/login", "status": 200, "statusText": "OK", "success": true, "data": "{\"success\":true,\"data\":{\"token\":\"token_445sdal2l3b\",\"user\":{\"id\":\"id_rp1mxm195\",\"email\":\"<EMAIL>\",\"firstName\":\"\",\"lastName\":\"\",\"createdAt\":\"2025-06-06T10:08:21.465Z\"}}}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Get Current User", "method": "GET", "url": "/auth/me", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"success\":false,\"error\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}], "failuresByStatus": {"401": [{"name": "Admin Products", "method": "GET", "url": "/admin/products", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Orders", "method": "GET", "url": "/admin/orders", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Customers", "method": "GET", "url": "/admin/customers", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"type\":\"unauthorized\",\"message\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Get Current User", "method": "GET", "url": "/auth/me", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"success\":false,\"error\":\"Authentication required\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "x-content-type-options", "x-frame-options", "x-xss-protection", "referrer-policy", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}]}}