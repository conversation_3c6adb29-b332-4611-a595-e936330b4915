#!/usr/bin/env node

/**
 * Test Runner for ONDC Seller Platform
 * Comprehensive test execution with reporting and monitoring
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('./config');

// Test suites configuration
const testSuites = {
  smoke: {
    name: 'Smoke Tests',
    files: ['smoke.test.js'],
    timeout: 10000,
    description: 'Quick validation of core functionality'
  },
  auth: {
    name: 'Authentication Tests',
    files: ['auth.test.js'],
    timeout: 30000,
    description: 'User authentication and authorization'
  },
  products: {
    name: 'Product Management Tests',
    files: ['products.test.js'],
    timeout: 30000,
    description: 'Product CRUD operations and catalog'
  },
  cart: {
    name: 'Shopping Cart Tests',
    files: ['cart.test.js'],
    timeout: 30000,
    description: 'Cart operations and management'
  },
  orders: {
    name: 'Order Management Tests',
    files: ['orders.test.js'],
    timeout: 30000,
    description: 'Order processing and tracking'
  },
  admin: {
    name: 'Admin Panel Tests',
    files: ['admin.test.js'],
    timeout: 30000,
    description: 'Administrative functions'
  },
  ondc: {
    name: 'ONDC Integration Tests',
    files: ['ondc.test.js'],
    timeout: 30000,
    description: 'ONDC protocol compliance'
  },
  security: {
    name: 'Security Tests',
    files: ['security.test.js'],
    timeout: 30000,
    description: 'Security vulnerabilities and protection'
  },
  performance: {
    name: 'Performance Tests',
    files: ['performance.test.js'],
    timeout: 60000,
    description: 'Load testing and performance validation'
  },
  integration: {
    name: 'Integration Tests',
    files: ['integration.test.js'],
    timeout: 45000,
    description: 'End-to-end workflow testing'
  }
};

// Command line arguments
const args = process.argv.slice(2);
const options = {
  suite: args.find(arg => arg.startsWith('--suite='))?.split('=')[1] || 'all',
  environment: args.find(arg => arg.startsWith('--env='))?.split('=')[1] || 'development',
  reporter: args.find(arg => arg.startsWith('--reporter='))?.split('=')[1] || 'spec',
  parallel: args.includes('--parallel'),
  watch: args.includes('--watch'),
  coverage: args.includes('--coverage'),
  verbose: args.includes('--verbose'),
  bail: args.includes('--bail'),
  grep: args.find(arg => arg.startsWith('--grep='))?.split('=')[1],
  timeout: args.find(arg => arg.startsWith('--timeout='))?.split('=')[1] || '30000'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Environment validation
function validateEnvironment() {
  logHeader('Environment Validation');
  
  const requiredEnvVars = {
    development: ['API_BASE_URL'],
    staging: ['STAGING_PUBLISHABLE_KEY', 'STAGING_ADMIN_TOKEN'],
    production: ['PROD_PUBLISHABLE_KEY', 'PROD_ADMIN_TOKEN']
  };

  const required = requiredEnvVars[options.environment] || [];
  const missing = required.filter(envVar => !process.env[envVar]);

  if (missing.length > 0) {
    logWarning(`Missing environment variables: ${missing.join(', ')}`);
    logInfo('Using default values for development environment');
  }

  logInfo(`Environment: ${options.environment}`);
  logInfo(`Base URL: ${config.config.baseUrl}`);
  logInfo(`Frontend URL: ${config.config.frontendUrl}`);
  
  return true;
}

// Pre-test setup
async function setupTests() {
  logHeader('Test Setup');
  
  try {
    // Create reports directory
    const reportsDir = path.join(__dirname, 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
      logSuccess('Created reports directory');
    }

    // Create screenshots directory
    const screenshotsDir = path.join(reportsDir, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
      logSuccess('Created screenshots directory');
    }

    // Validate API connectivity
    logInfo('Validating API connectivity...');
    const axios = require('axios');
    
    try {
      const response = await axios.get(`${config.config.baseUrl}/health`, {
        timeout: 5000
      });
      logSuccess('API is accessible');
    } catch (error) {
      logWarning('API connectivity check failed - tests may fail');
      if (options.verbose) {
        console.log(error.message);
      }
    }

    return true;
  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    return false;
  }
}

// Test execution
function runTestSuite(suiteName, suiteConfig) {
  return new Promise((resolve, reject) => {
    logHeader(`Running ${suiteConfig.name}`);
    logInfo(suiteConfig.description);

    const mochaArgs = [
      '--timeout', options.timeout,
      '--reporter', options.reporter
    ];

    if (options.grep) {
      mochaArgs.push('--grep', options.grep);
    }

    if (options.bail) {
      mochaArgs.push('--bail');
    }

    if (options.coverage) {
      mochaArgs.unshift('nyc');
    }

    // Add test files
    suiteConfig.files.forEach(file => {
      mochaArgs.push(file);
    });

    const testProcess = spawn('npx', ['mocha', ...mochaArgs], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: options.environment,
        TEST_SUITE: suiteName
      }
    });

    testProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess(`${suiteConfig.name} completed successfully`);
        resolve({ suite: suiteName, status: 'passed', code });
      } else {
        logError(`${suiteConfig.name} failed with exit code ${code}`);
        resolve({ suite: suiteName, status: 'failed', code });
      }
    });

    testProcess.on('error', (error) => {
      logError(`Failed to start ${suiteConfig.name}: ${error.message}`);
      reject(error);
    });
  });
}

// Main execution function
async function main() {
  logHeader('ONDC Seller Platform Test Runner');
  logInfo(`Test Suite: ${options.suite}`);
  logInfo(`Environment: ${options.environment}`);
  logInfo(`Reporter: ${options.reporter}`);

  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Setup tests
  if (!await setupTests()) {
    process.exit(1);
  }

  const startTime = Date.now();
  const results = [];

  try {
    if (options.suite === 'all') {
      // Run all test suites
      for (const [suiteName, suiteConfig] of Object.entries(testSuites)) {
        if (options.parallel) {
          // TODO: Implement parallel execution
          logWarning('Parallel execution not yet implemented, running sequentially');
        }
        
        const result = await runTestSuite(suiteName, suiteConfig);
        results.push(result);

        // Stop on first failure if bail option is set
        if (options.bail && result.status === 'failed') {
          logError('Stopping execution due to test failure (--bail option)');
          break;
        }
      }
    } else {
      // Run specific test suite
      const suiteConfig = testSuites[options.suite];
      if (!suiteConfig) {
        logError(`Unknown test suite: ${options.suite}`);
        logInfo(`Available suites: ${Object.keys(testSuites).join(', ')}`);
        process.exit(1);
      }

      const result = await runTestSuite(options.suite, suiteConfig);
      results.push(result);
    }

    // Generate summary report
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logHeader('Test Execution Summary');
    logInfo(`Total execution time: ${duration.toFixed(2)} seconds`);
    
    const passed = results.filter(r => r.status === 'passed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    
    logInfo(`Test suites executed: ${results.length}`);
    logSuccess(`Passed: ${passed}`);
    
    if (failed > 0) {
      logError(`Failed: ${failed}`);
      
      // List failed suites
      const failedSuites = results.filter(r => r.status === 'failed');
      failedSuites.forEach(suite => {
        logError(`  - ${suite.suite}`);
      });
    }

    // Save results to file
    const reportFile = path.join(__dirname, 'reports', `test-results-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      environment: options.environment,
      suite: options.suite,
      duration,
      results,
      summary: { total: results.length, passed, failed }
    }, null, 2));

    logSuccess(`Test results saved to: ${reportFile}`);

    // Exit with appropriate code
    process.exit(failed > 0 ? 1 : 0);

  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    if (options.verbose) {
      console.error(error);
    }
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  logWarning('Test execution interrupted by user');
  process.exit(130);
});

process.on('SIGTERM', () => {
  logWarning('Test execution terminated');
  process.exit(143);
});

// Show help
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
ONDC Seller Platform Test Runner

Usage: node run-tests.js [options]

Options:
  --suite=<name>     Test suite to run (default: all)
                     Available: ${Object.keys(testSuites).join(', ')}
  --env=<env>        Environment (development, staging, production)
  --reporter=<name>  Test reporter (spec, json, html)
  --parallel         Run tests in parallel (experimental)
  --watch            Watch for file changes and re-run tests
  --coverage         Generate code coverage report
  --verbose          Verbose output
  --bail             Stop on first test failure
  --grep=<pattern>   Only run tests matching pattern
  --timeout=<ms>     Test timeout in milliseconds
  --help, -h         Show this help message

Examples:
  node run-tests.js --suite=auth --env=development
  node run-tests.js --suite=all --reporter=html --coverage
  node run-tests.js --grep="login" --verbose
`);
  process.exit(0);
}

// Run the tests
main();
