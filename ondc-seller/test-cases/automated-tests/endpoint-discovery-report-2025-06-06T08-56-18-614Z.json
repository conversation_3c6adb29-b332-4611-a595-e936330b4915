{"timestamp": "2025-06-06T08:56:18.614Z", "baseUrl": "http://localhost:9000", "summary": {"total": 14, "passed": 1, "failed": 13, "passRate": "7.1"}, "results": [{"name": "Health Check", "method": "GET", "url": "/health", "status": 200, "statusText": "OK", "success": true, "data": "OK", "headers": ["x-powered-by", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Version Info", "method": "GET", "url": "/version", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /version</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Store Products", "method": "GET", "url": "/store/products", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Store Regions", "method": "GET", "url": "/store/regions", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Create <PERSON><PERSON>", "method": "POST", "url": "/store/carts", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Products", "method": "GET", "url": "/admin/products", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Orders", "method": "GET", "url": "/admin/orders", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Customers", "method": "GET", "url": "/admin/customers", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Featured Products", "method": "GET", "url": "/products/featured", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /products/featured</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "ONDC Catalog", "method": "GET", "url": "/ondc/catalog", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /ondc/catalog</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Analytics Dashboard", "method": "GET", "url": "/analytics/dashboard", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /analytics/dashboard</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "User Registration", "method": "POST", "url": "/auth/register", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /auth/register</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "User Login", "method": "POST", "url": "/auth/login", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /auth/login</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Get Current User", "method": "GET", "url": "/auth/me", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /auth/me</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}], "failuresByStatus": {"401": [{"name": "Admin Products", "method": "GET", "url": "/admin/products", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Orders", "method": "GET", "url": "/admin/orders", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Admin Customers", "method": "GET", "url": "/admin/customers", "status": 401, "statusText": "Unauthorized", "success": false, "data": "{\"message\":\"Unauthorized\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}], "404": [{"name": "Version Info", "method": "GET", "url": "/version", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /version</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Featured Products", "method": "GET", "url": "/products/featured", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /products/featured</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "ONDC Catalog", "method": "GET", "url": "/ondc/catalog", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /ondc/catalog</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Analytics Dashboard", "method": "GET", "url": "/analytics/dashboard", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /analytics/dashboard</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "User Registration", "method": "POST", "url": "/auth/register", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /auth/register</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "User Login", "method": "POST", "url": "/auth/login", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /auth/login</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}, {"name": "Get Current User", "method": "GET", "url": "/auth/me", "status": 404, "statusText": "Not Found", "success": false, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot GET /auth/me</pre>\n</body>\n</html>\n", "headers": ["x-powered-by", "content-security-policy", "x-content-type-options", "content-type", "content-length", "date", "connection", "keep-alive"]}], "500": [{"name": "Store Products", "method": "GET", "url": "/store/products", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Store Regions", "method": "GET", "url": "/store/regions", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}, {"name": "Create <PERSON><PERSON>", "method": "POST", "url": "/store/carts", "status": 500, "statusText": "Internal Server Error", "success": false, "data": "{\"code\":\"unknown_error\",\"type\":\"unknown_error\",\"message\":\"An unknown error occurred.\"}", "headers": ["x-powered-by", "vary", "access-control-allow-credentials", "content-type", "content-length", "etag", "date", "connection", "keep-alive"]}]}}