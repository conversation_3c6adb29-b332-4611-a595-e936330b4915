/**
 * Authentication API Test Suite
 * Automated tests for user authentication endpoints
 */

const request = require('supertest');
const { expect } = require('chai');

// Test configuration
const config = {
  baseUrl: process.env.API_BASE_URL || 'http://localhost:9000',
  publishableKey: process.env.PUBLISHABLE_KEY || 'pk_test_123',
  timeout: 10000,
};

// Test data
const testData = {
  validUser: {
    email: '<EMAIL>',
    password: 'Test@123456',
    firstName: 'John',
    lastName: 'Doe',
  },
  invalidUser: {
    email: 'invalid-email',
    password: 'weak',
  },
  existingUser: {
    email: '<EMAIL>',
    password: 'Test@123456',
  },
};

describe('Authentication API Tests', () => {
  let app;
  let userToken;

  before(async () => {
    // Setup test environment
    console.log(`Testing against: ${config.baseUrl}`);

    // Clean up any existing test data
    await request(config.baseUrl).post('/test/cleanup').send({});
  });

  after(async () => {
    // Cleanup test data
    console.log('Cleaning up test data...');
  });

  describe('User Registration', () => {
    it('TC_API_AUTH_001: Should register user with valid data', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/register')
        .send(testData.validUser)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(201);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('message');
      expect(response.body.data).to.have.property('id');
      expect(response.body.data).to.have.property('email', testData.validUser.email);
      expect(response.body.data).to.not.have.property('password');
    });

    it('TC_API_AUTH_002: Should reject registration with duplicate email', async () => {
      // First registration
      await request(config.baseUrl)
        .post('/auth/register')
        .send(testData.existingUser)
        .set('x-publishable-api-key', config.publishableKey);

      // Duplicate registration
      const response = await request(config.baseUrl)
        .post('/auth/register')
        .send(testData.existingUser)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(409);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.include('already exists');
    });

    it('TC_API_AUTH_003: Should reject registration with invalid email', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/register')
        .send(testData.invalidUser)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(400);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });

    it('TC_API_AUTH_004: Should reject registration with missing fields', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/register')
        .send({ email: '<EMAIL>' })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(400);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });
  });

  describe('User Login', () => {
    before(async () => {
      // Ensure test user exists
      await request(config.baseUrl)
        .post('/auth/register')
        .send(testData.validUser)
        .set('x-publishable-api-key', config.publishableKey);
    });

    it('TC_API_AUTH_005: Should login with valid credentials', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send({
          email: testData.validUser.email,
          password: testData.validUser.password,
        })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('token');
      expect(response.body.data).to.have.property('user');
      expect(response.body.data.user).to.have.property('email', testData.validUser.email);
      expect(response.body.data.user).to.not.have.property('password');

      // Store token for subsequent tests
      userToken = response.body.data.token;
    });

    it('TC_API_AUTH_006: Should reject login with invalid credentials', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send({
          email: testData.validUser.email,
          password: 'wrongpassword',
        })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(401);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.include('Invalid');
    });

    it('TC_API_AUTH_007: Should reject login with non-existent user', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(401);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });

    it('TC_API_AUTH_008: Should reject login with missing fields', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send({ email: testData.validUser.email })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(400);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });
  });

  describe('Token Validation', () => {
    it('TC_API_AUTH_009: Should validate valid token', async () => {
      if (!userToken) {
        // Login to get token
        const loginResponse = await request(config.baseUrl)
          .post('/auth/login')
          .send({
            email: testData.validUser.email,
            password: testData.validUser.password,
          })
          .set('x-publishable-api-key', config.publishableKey);

        userToken = loginResponse.body.data.token;
      }

      const response = await request(config.baseUrl)
        .get('/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('user');
      expect(response.body.data.user).to.have.property('email', testData.validUser.email);
    });

    it('TC_API_AUTH_010: Should reject invalid token', async () => {
      const response = await request(config.baseUrl)
        .get('/auth/me')
        .set('Authorization', 'Bearer invalid_token')
        .set('x-publishable-api-key', config.publishableKey)
        .expect(401);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });

    it('TC_API_AUTH_011: Should reject missing token', async () => {
      const response = await request(config.baseUrl)
        .get('/auth/me')
        .set('x-publishable-api-key', config.publishableKey)
        .expect(401);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });
  });

  describe('Password Reset', () => {
    it('TC_API_AUTH_012: Should initiate password reset for valid email', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/forgot-password')
        .send({ email: testData.validUser.email })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('message');
      expect(response.body.message).to.include('reset');
    });

    it('TC_API_AUTH_013: Should handle password reset for non-existent email', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(200); // Should return 200 for security reasons

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('message');
    });

    it('TC_API_AUTH_014: Should reject password reset with invalid email format', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/forgot-password')
        .send({ email: 'invalid-email' })
        .set('x-publishable-api-key', config.publishableKey)
        .expect(400);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });
  });

  describe('Logout', () => {
    it('TC_API_AUTH_015: Should logout successfully with valid token', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('message');
    });

    it('TC_API_AUTH_016: Should handle logout with invalid token', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/logout')
        .set('Authorization', 'Bearer invalid_token')
        .set('x-publishable-api-key', config.publishableKey)
        .expect(401);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });
  });

  describe('Rate Limiting', () => {
    it('TC_API_AUTH_017: Should implement rate limiting for login attempts', async () => {
      const promises = [];

      // Make multiple rapid login attempts
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(config.baseUrl)
            .post('/auth/login')
            .send({
              email: testData.validUser.email,
              password: 'wrongpassword',
            })
            .set('x-publishable-api-key', config.publishableKey)
        );
      }

      const responses = await Promise.all(promises);

      // Check if rate limiting is applied
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).to.be.greaterThan(0);
    });
  });

  describe('Security Headers', () => {
    it('TC_API_AUTH_018: Should include security headers in responses', async () => {
      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send({
          email: testData.validUser.email,
          password: testData.validUser.password,
        })
        .set('x-publishable-api-key', config.publishableKey);

      // Check for security headers
      expect(response.headers).to.have.property('x-content-type-options');
      expect(response.headers).to.have.property('x-frame-options');
      expect(response.headers['x-content-type-options']).to.equal('nosniff');
    });
  });

  describe('Input Validation', () => {
    it('TC_API_AUTH_019: Should sanitize input to prevent XSS', async () => {
      const maliciousInput = {
        email: '<script>alert("xss")</script>@example.com',
        password: 'Test@123456',
        firstName: '<script>alert("xss")</script>',
        lastName: 'Doe',
      };

      const response = await request(config.baseUrl)
        .post('/auth/register')
        .send(maliciousInput)
        .set('x-publishable-api-key', config.publishableKey)
        .expect(400);

      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error');
    });

    it('TC_API_AUTH_020: Should prevent SQL injection in login', async () => {
      // Clean up rate limiting before this test
      await request(config.baseUrl).post('/test/cleanup').send({});

      // Small delay to ensure cleanup is processed
      await new Promise(resolve => setTimeout(resolve, 100));

      const sqlInjectionAttempt = {
        email: "<EMAIL>'; DROP TABLE users; --",
        password: 'password',
      };

      const response = await request(config.baseUrl)
        .post('/auth/login')
        .send(sqlInjectionAttempt)
        .set('x-publishable-api-key', config.publishableKey);

      // Should not cause server error
      expect(response.status).to.be.oneOf([400, 401, 429]); // Accept 429 as valid response
      expect(response.body).to.have.property('success', false);
    });
  });
});

// Helper functions
function generateRandomEmail() {
  const timestamp = Date.now();
  return `test${timestamp}@example.com`;
}

function generateRandomPassword() {
  return `Test@${Math.random().toString(36).substring(7)}`;
}

// Export for use in other test files
module.exports = {
  config,
  testData,
  generateRandomEmail,
  generateRandomPassword,
};
