/**
 * Test Configuration for ONDC Seller Platform
 * Environment-specific settings and test data
 */

const path = require('path');

// Environment configuration
const environments = {
  development: {
    name: 'Development',
    baseUrl: 'http://localhost:9000',
    frontendUrl: 'http://localhost:3001',
    publishableKey: 'pk_01HQD0GMQXVK5XJ0XS0ZNPJ8T5',
    adminToken: null, // Will be generated during tests
    database: {
      host: 'localhost',
      port: 5432,
      name: 'medusa_test',
    },
    redis: {
      host: 'localhost',
      port: 6379,
    },
  },
  staging: {
    name: 'Staging',
    baseUrl: 'https://api-staging.ondc-seller.com',
    frontendUrl: 'https://staging.ondc-seller.com',
    publishableKey: process.env.STAGING_PUBLISHABLE_KEY,
    adminToken: process.env.STAGING_ADMIN_TOKEN,
    database: {
      host: process.env.STAGING_DB_HOST,
      port: process.env.STAGING_DB_PORT,
      name: process.env.STAGING_DB_NAME,
    },
  },
  production: {
    name: 'Production',
    baseUrl: 'https://api.ondc-seller.com',
    frontendUrl: 'https://ondc-seller.com',
    publishableKey: process.env.PROD_PUBLISHABLE_KEY,
    adminToken: process.env.PROD_ADMIN_TOKEN,
    database: {
      host: process.env.PROD_DB_HOST,
      port: process.env.PROD_DB_PORT,
      name: process.env.PROD_DB_NAME,
    },
  },
};

// Get current environment
const currentEnv = process.env.NODE_ENV || 'development';
const config = environments[currentEnv];

// Test data templates
const testData = {
  users: {
    customer: {
      email: '<EMAIL>',
      password: 'Customer@123',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+91-**********',
    },
    admin: {
      email: '<EMAIL>',
      password: 'Admin@123',
      firstName: 'Admin',
      lastName: 'User',
    },
    seller: {
      email: '<EMAIL>',
      password: 'Seller@123',
      firstName: 'Seller',
      lastName: 'User',
      businessName: 'Test Business',
    },
  },
  products: {
    electronics: {
      title: 'Test Laptop',
      description: 'High-performance laptop for testing',
      handle: 'test-laptop',
      status: 'published',
      weight: 2000,
      material: 'Aluminum',
      categories: ['electronics', 'computers'],
      variants: [
        {
          title: '16GB RAM / 512GB SSD',
          sku: 'LAPTOP-16-512',
          prices: [
            {
              amount: 89999,
              currency_code: 'INR',
            },
          ],
          inventory_quantity: 50,
        },
      ],
    },
    clothing: {
      title: 'Test T-Shirt',
      description: 'Comfortable cotton t-shirt',
      handle: 'test-tshirt',
      status: 'published',
      weight: 200,
      material: 'Cotton',
      categories: ['clothing', 'casual'],
      variants: [
        {
          title: 'Medium / Blue',
          sku: 'TSHIRT-M-BLUE',
          prices: [
            {
              amount: 1999,
              currency_code: 'INR',
            },
          ],
          inventory_quantity: 100,
        },
      ],
    },
  },
  addresses: {
    shipping: {
      first_name: 'John',
      last_name: 'Doe',
      address_1: '123 Test Street',
      address_2: 'Apt 4B',
      city: 'Mumbai',
      province: 'Maharashtra',
      postal_code: '400001',
      country_code: 'IN',
      phone: '+91-**********',
    },
    billing: {
      first_name: 'John',
      last_name: 'Doe',
      address_1: '123 Test Street',
      address_2: 'Apt 4B',
      city: 'Mumbai',
      province: 'Maharashtra',
      postal_code: '400001',
      country_code: 'IN',
      phone: '+91-**********',
    },
  },
  payment: {
    creditCard: {
      number: '****************',
      exp_month: '12',
      exp_year: '2025',
      cvc: '123',
      name: 'John Doe',
    },
    upi: {
      vpa: 'test@upi',
    },
  },
  ondc: {
    provider: {
      id: 'test-provider-001',
      name: 'Test Provider',
      short_desc: 'Test provider for ONDC',
      long_desc: 'Detailed description of test provider',
      locations: [
        {
          id: 'test-location-001',
          gps: '19.0760,72.8777',
          address: 'Mumbai, Maharashtra, India',
        },
      ],
    },
    catalog: {
      descriptor: {
        name: 'Test Catalog',
        code: 'TEST-CAT-001',
      },
    },
  },
};

// Test timeouts and limits
const timeouts = {
  api: 10000, // 10 seconds for API calls
  database: 5000, // 5 seconds for database operations
  file: 15000, // 15 seconds for file operations
  payment: 30000, // 30 seconds for payment processing
  load: 60000, // 60 seconds for load tests
};

// Performance thresholds
const performance = {
  responseTime: {
    fast: 200, // < 200ms is fast
    acceptable: 1000, // < 1s is acceptable
    slow: 3000, // > 3s is slow
  },
  throughput: {
    minimum: 100, // Minimum requests per second
    target: 500, // Target requests per second
    maximum: 1000, // Maximum requests per second
  },
  errorRate: {
    acceptable: 0.01, // 1% error rate is acceptable
    critical: 0.05, // 5% error rate is critical
  },
};

// Security test configurations
const security = {
  rateLimiting: {
    maxRequests: 100,
    windowMs: 60000, // 1 minute window
  },
  bruteForce: {
    maxAttempts: 5,
    lockoutTime: 300000, // 5 minutes
  },
  injection: {
    sqlPayloads: ["'; DROP TABLE users; --", "' OR '1'='1", "'; SELECT * FROM users; --"],
    xssPayloads: [
      '<script>alert("xss")</script>',
      '<img src=x onerror=alert("xss")>',
      'javascript:alert("xss")',
    ],
  },
};

// Load testing configuration
const loadTest = {
  phases: [
    {
      name: 'Warm up',
      duration: 60,
      arrivalRate: 10,
    },
    {
      name: 'Ramp up',
      duration: 120,
      arrivalRate: 50,
    },
    {
      name: 'Sustained load',
      duration: 300,
      arrivalRate: 100,
    },
    {
      name: 'Peak load',
      duration: 60,
      arrivalRate: 200,
    },
  ],
  scenarios: [
    {
      name: 'Browse products',
      weight: 40,
      flow: [
        { get: { url: '/store/products' } },
        { get: { url: '/store/products/{{ $randomString() }}' } },
      ],
    },
    {
      name: 'Add to cart',
      weight: 30,
      flow: [
        { post: { url: '/store/carts', json: { region_id: 'reg_test' } } },
        { post: { url: '/store/carts/{{ cart_id }}/line-items' } },
      ],
    },
    {
      name: 'Search products',
      weight: 20,
      flow: [{ get: { url: '/store/products?q={{ $randomString() }}' } }],
    },
    {
      name: 'User authentication',
      weight: 10,
      flow: [{ post: { url: '/auth/login' } }, { get: { url: '/auth/me' } }],
    },
  ],
};

// Test reporting configuration
const reporting = {
  formats: ['spec', 'json', 'html'],
  outputDir: path.join(__dirname, 'reports'),
  screenshots: {
    enabled: true,
    onFailure: true,
    path: path.join(__dirname, 'reports', 'screenshots'),
  },
  videos: {
    enabled: false,
    path: path.join(__dirname, 'reports', 'videos'),
  },
};

// Database test configuration
const database = {
  testPrefix: 'test_',
  cleanup: {
    enabled: true,
    tables: ['users', 'products', 'orders', 'carts'],
    preserveAdmin: true,
  },
  fixtures: {
    users: 10,
    products: 50,
    orders: 25,
    categories: 5,
  },
};

// API endpoints for testing
const endpoints = {
  auth: {
    register: '/auth/register',
    login: '/auth/login',
    logout: '/auth/logout',
    me: '/auth/me',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
  },
  store: {
    products: '/store/products',
    product: '/store/products/:id',
    carts: '/store/carts',
    cart: '/store/carts/:id',
    addToCart: '/store/carts/:id/line-items',
    regions: '/store/regions',
    shippingOptions: '/store/shipping-options',
  },
  admin: {
    products: '/admin/products',
    product: '/admin/products/:id',
    orders: '/admin/orders',
    order: '/admin/orders/:id',
    customers: '/admin/customers',
    customer: '/admin/customers/:id',
    analytics: '/admin/analytics',
  },
  custom: {
    health: '/health',
    version: '/version',
    featured: '/products/featured',
    ondcCatalog: '/ondc/catalog',
    analytics: '/analytics/dashboard',
  },
};

// Export configuration
module.exports = {
  environment: currentEnv,
  config,
  testData,
  timeouts,
  performance,
  security,
  loadTest,
  reporting,
  database,
  endpoints,

  // Helper functions
  getEndpoint: (category, name, params = {}) => {
    let endpoint = endpoints[category][name];
    Object.keys(params).forEach(key => {
      endpoint = endpoint.replace(`:${key}`, params[key]);
    });
    return endpoint;
  },

  generateTestEmail: () => {
    const timestamp = Date.now();
    return `test${timestamp}@example.com`;
  },

  generateTestData: (template, overrides = {}) => {
    return { ...template, ...overrides };
  },

  isProduction: () => currentEnv === 'production',
  isDevelopment: () => currentEnv === 'development',
  isStaging: () => currentEnv === 'staging',
};
