# E-Commerce Website Test Cases

## Test Suite Overview
Comprehensive test cases for ONDC Seller Platform covering functional, non-functional, and integration testing.

---

## 1. User Authentication & Authorization

### 1.1 User Registration
**Test Case ID**: TC_AUTH_001  
**Priority**: High  
**Test Scenario**: User registration with valid details  

**Test Steps**:
1. Navigate to registration page
2. Enter valid email, password, confirm password
3. Accept terms and conditions
4. Click "Register" button

**Expected Result**: 
- User account created successfully
- Confirmation email sent
- Redirect to login page

**Test Data**:
- Email: <EMAIL>
- Password: Test@123456
- Confirm Password: Test@123456

---

**Test Case ID**: TC_AUTH_002  
**Priority**: High  
**Test Scenario**: User registration with invalid email format  

**Test Steps**:
1. Navigate to registration page
2. Enter invalid email format
3. Enter valid password details
4. Click "Register" button

**Expected Result**: 
- Error message displayed: "Please enter a valid email address"
- Registration form not submitted

**Test Data**:
- Email: invalid-email
- Password: Test@123456

---

### 1.2 User Login
**Test Case ID**: TC_AUTH_003  
**Priority**: High  
**Test Scenario**: Login with valid credentials  

**Test Steps**:
1. Navigate to login page
2. Enter valid email and password
3. Click "Login" button

**Expected Result**: 
- User logged in successfully
- Redirect to dashboard/homepage
- User session established

---

**Test Case ID**: TC_AUTH_004  
**Priority**: High  
**Test Scenario**: Login with invalid credentials  

**Test Steps**:
1. Navigate to login page
2. Enter invalid email/password
3. Click "Login" button

**Expected Result**: 
- Error message: "Invalid email or password"
- User remains on login page
- No session established

---

### 1.3 Password Management
**Test Case ID**: TC_AUTH_005  
**Priority**: Medium  
**Test Scenario**: Forgot password functionality  

**Test Steps**:
1. Click "Forgot Password" link
2. Enter registered email
3. Click "Send Reset Link"
4. Check email for reset link
5. Click reset link and enter new password

**Expected Result**: 
- Password reset email sent
- Reset link works correctly
- Password updated successfully

---

## 2. Product Management

### 2.1 Product Catalog
**Test Case ID**: TC_PROD_001  
**Priority**: High  
**Test Scenario**: View product catalog  

**Test Steps**:
1. Navigate to products page
2. Verify product grid layout
3. Check product information display

**Expected Result**: 
- Products displayed in grid format
- Product images, titles, prices visible
- Pagination working correctly

---

**Test Case ID**: TC_PROD_002  
**Priority**: High  
**Test Scenario**: Product search functionality  

**Test Steps**:
1. Enter product name in search box
2. Click search button
3. Verify search results

**Expected Result**: 
- Relevant products displayed
- Search filters working
- "No results" message for invalid searches

**Test Data**:
- Search Term: "laptop"
- Expected: Laptop products displayed

---

**Test Case ID**: TC_PROD_003  
**Priority**: High  
**Test Scenario**: Product detail view  

**Test Steps**:
1. Click on a product from catalog
2. Verify product detail page loads
3. Check all product information

**Expected Result**: 
- Product details page loads correctly
- Images, description, price, specifications visible
- Add to cart button functional

---

### 2.2 Product Filtering & Sorting
**Test Case ID**: TC_PROD_004  
**Priority**: Medium  
**Test Scenario**: Filter products by category  

**Test Steps**:
1. Select category filter (e.g., Electronics)
2. Apply filter
3. Verify filtered results

**Expected Result**: 
- Only products from selected category displayed
- Filter count updated
- Clear filter option available

---

**Test Case ID**: TC_PROD_005  
**Priority**: Medium  
**Test Scenario**: Sort products by price  

**Test Steps**:
1. Select "Price: Low to High" sorting
2. Verify product order
3. Select "Price: High to Low" sorting
4. Verify product order

**Expected Result**: 
- Products sorted correctly by price
- Sorting options work as expected

---

## 3. Shopping Cart Management

### 3.1 Add to Cart
**Test Case ID**: TC_CART_001  
**Priority**: High  
**Test Scenario**: Add product to cart  

**Test Steps**:
1. Navigate to product detail page
2. Select quantity (if applicable)
3. Click "Add to Cart" button
4. Verify cart icon updates

**Expected Result**: 
- Product added to cart successfully
- Cart count updated
- Success message displayed

---

**Test Case ID**: TC_CART_002  
**Priority**: High  
**Test Scenario**: View cart contents  

**Test Steps**:
1. Click on cart icon
2. Verify cart page loads
3. Check product details in cart

**Expected Result**: 
- Cart page displays correctly
- Product details, quantities, prices visible
- Total amount calculated correctly

---

### 3.2 Cart Operations
**Test Case ID**: TC_CART_003  
**Priority**: High  
**Test Scenario**: Update product quantity in cart  

**Test Steps**:
1. Navigate to cart page
2. Change quantity of a product
3. Click update button
4. Verify changes

**Expected Result**: 
- Quantity updated successfully
- Total price recalculated
- Cart totals updated

---

**Test Case ID**: TC_CART_004  
**Priority**: High  
**Test Scenario**: Remove product from cart  

**Test Steps**:
1. Navigate to cart page
2. Click "Remove" button for a product
3. Confirm removal

**Expected Result**: 
- Product removed from cart
- Cart totals updated
- Cart count decreased

---

## 4. Checkout Process

### 4.1 Checkout Flow
**Test Case ID**: TC_CHECKOUT_001  
**Priority**: High  
**Test Scenario**: Complete checkout process  

**Test Steps**:
1. Add products to cart
2. Navigate to checkout
3. Enter shipping address
4. Select payment method
5. Review order
6. Place order

**Expected Result**: 
- Checkout process completes successfully
- Order confirmation displayed
- Order confirmation email sent

---

**Test Case ID**: TC_CHECKOUT_002  
**Priority**: High  
**Test Scenario**: Checkout with invalid shipping address  

**Test Steps**:
1. Proceed to checkout
2. Enter incomplete shipping address
3. Attempt to continue

**Expected Result**: 
- Validation errors displayed
- Cannot proceed without valid address
- Required fields highlighted

---

### 4.2 Payment Processing
**Test Case ID**: TC_PAYMENT_001  
**Priority**: High  
**Test Scenario**: Payment with valid credit card  

**Test Steps**:
1. Select credit card payment
2. Enter valid card details
3. Submit payment

**Expected Result**: 
- Payment processed successfully
- Order status updated to "Paid"
- Payment confirmation received

**Test Data**:
- Card Number: ****************
- Expiry: 12/25
- CVV: 123

---

**Test Case ID**: TC_PAYMENT_002  
**Priority**: High  
**Test Scenario**: Payment with invalid credit card  

**Test Steps**:
1. Select credit card payment
2. Enter invalid card details
3. Submit payment

**Expected Result**: 
- Payment declined
- Error message displayed
- Order not processed

---

## 5. Order Management

### 5.1 Order Tracking
**Test Case ID**: TC_ORDER_001  
**Priority**: High  
**Test Scenario**: View order history  

**Test Steps**:
1. Login to user account
2. Navigate to "My Orders"
3. Verify order list

**Expected Result**: 
- Order history displayed correctly
- Order details, status, dates visible
- Order tracking links functional

---

**Test Case ID**: TC_ORDER_002  
**Priority**: Medium  
**Test Scenario**: Track order status  

**Test Steps**:
1. Click on order tracking link
2. Verify tracking information
3. Check status updates

**Expected Result**: 
- Tracking page loads correctly
- Current order status displayed
- Tracking timeline visible

---

### 5.2 Order Modifications
**Test Case ID**: TC_ORDER_003  
**Priority**: Medium  
**Test Scenario**: Cancel order  

**Test Steps**:
1. Navigate to order details
2. Click "Cancel Order" button
3. Confirm cancellation

**Expected Result**: 
- Order cancelled successfully
- Status updated to "Cancelled"
- Refund process initiated (if applicable)

---

## 6. User Profile Management

### 6.1 Profile Updates
**Test Case ID**: TC_PROFILE_001  
**Priority**: Medium  
**Test Scenario**: Update user profile information  

**Test Steps**:
1. Navigate to profile settings
2. Update personal information
3. Save changes

**Expected Result**: 
- Profile updated successfully
- Changes reflected in account
- Confirmation message displayed

---

**Test Case ID**: TC_PROFILE_002  
**Priority**: Medium  
**Test Scenario**: Change password  

**Test Steps**:
1. Navigate to password change section
2. Enter current password
3. Enter new password
4. Confirm new password
5. Save changes

**Expected Result**: 
- Password changed successfully
- User can login with new password
- Confirmation message displayed

---

## 7. Admin Panel Testing

### 7.1 Product Management (Admin)
**Test Case ID**: TC_ADMIN_001  
**Priority**: High  
**Test Scenario**: Add new product  

**Test Steps**:
1. Login as admin
2. Navigate to product management
3. Click "Add Product"
4. Fill product details
5. Upload images
6. Save product

**Expected Result**: 
- Product added successfully
- Product visible in catalog
- All details saved correctly

---

**Test Case ID**: TC_ADMIN_002  
**Priority**: High  
**Test Scenario**: Edit existing product  

**Test Steps**:
1. Navigate to product list
2. Select product to edit
3. Modify product details
4. Save changes

**Expected Result**: 
- Product updated successfully
- Changes reflected on frontend
- Version history maintained

---

### 7.2 Order Management (Admin)
**Test Case ID**: TC_ADMIN_003  
**Priority**: High  
**Test Scenario**: Process order  

**Test Steps**:
1. Navigate to order management
2. Select pending order
3. Update order status
4. Add tracking information

**Expected Result**: 
- Order status updated
- Customer notified of status change
- Tracking information saved

---

## 8. Performance Testing

### 8.1 Load Testing
**Test Case ID**: TC_PERF_001  
**Priority**: Medium  
**Test Scenario**: Website performance under normal load  

**Test Steps**:
1. Simulate 100 concurrent users
2. Perform typical user actions
3. Monitor response times

**Expected Result**: 
- Page load times < 3 seconds
- No errors or timeouts
- System remains stable

---

### 8.2 Stress Testing
**Test Case ID**: TC_PERF_002  
**Priority**: Medium  
**Test Scenario**: Website performance under high load  

**Test Steps**:
1. Simulate 500+ concurrent users
2. Monitor system behavior
3. Check for breaking points

**Expected Result**: 
- System handles load gracefully
- Appropriate error messages for overload
- Recovery after load reduction

---

## 9. Security Testing

### 9.1 Input Validation
**Test Case ID**: TC_SEC_001  
**Priority**: High  
**Test Scenario**: SQL injection prevention  

**Test Steps**:
1. Enter SQL injection code in search box
2. Submit form
3. Verify system response

**Expected Result**: 
- Input sanitized properly
- No database errors
- Security measures active

**Test Data**:
- Input: `'; DROP TABLE users; --`

---

### 9.2 Authentication Security
**Test Case ID**: TC_SEC_002  
**Priority**: High  
**Test Scenario**: Session management  

**Test Steps**:
1. Login to account
2. Close browser
3. Reopen and try to access protected pages

**Expected Result**: 
- Session expired appropriately
- Redirect to login page
- No unauthorized access

---

## 10. Mobile Responsiveness

### 10.1 Mobile Layout
**Test Case ID**: TC_MOBILE_001  
**Priority**: High  
**Test Scenario**: Website display on mobile devices  

**Test Steps**:
1. Open website on mobile device
2. Navigate through different pages
3. Test touch interactions

**Expected Result**: 
- Layout adapts to mobile screen
- All features accessible
- Touch interactions work properly

---

### 10.2 Mobile Performance
**Test Case ID**: TC_MOBILE_002  
**Priority**: Medium  
**Test Scenario**: Mobile page load performance  

**Test Steps**:
1. Test page load times on mobile
2. Check image optimization
3. Verify mobile-specific features

**Expected Result**: 
- Fast loading on mobile networks
- Images optimized for mobile
- Mobile-specific features functional

---

## Test Execution Guidelines

### Test Environment Setup
- **Development**: http://localhost:3000
- **Staging**: https://staging.ondc-seller.com
- **Production**: https://ondc-seller.com

### Test Data Management
- Use dedicated test accounts
- Reset test data before each test cycle
- Maintain test data consistency

### Reporting
- Document all test results
- Include screenshots for failures
- Track defect resolution

### Automation Priorities
1. User authentication flows
2. Product search and filtering
3. Cart operations
4. Checkout process
5. Payment processing

---

*This test suite should be executed in each development cycle and before any production deployment.*
