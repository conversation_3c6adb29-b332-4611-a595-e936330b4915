#!/usr/bin/env node

/**
 * Simple test client for medusa-mcp server
 * Tests the MCP protocol communication
 */

const { spawn } = require('child_process');

async function testMCPServer() {
  console.log('🚀 Testing Medusa-MCP Server...\n');

  // Start the MCP server process
  const mcpServer = spawn('node', ['dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let serverOutput = '';
  let serverError = '';

  mcpServer.stdout.on('data', (data) => {
    serverOutput += data.toString();
    console.log('📤 Server Output:', data.toString().trim());
  });

  mcpServer.stderr.on('data', (data) => {
    serverError += data.toString();
    console.log('📥 Server Started:', data.toString().trim());
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('\n🔧 Testing MCP Protocol...\n');

  // Test 1: List Tools
  console.log('1️⃣ Testing list_tools...');
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };

  mcpServer.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // Test 2: Call list_products tool
  console.log('2️⃣ Testing list_products tool...');
  const listProductsRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'list_products',
      arguments: {
        limit: 5
      }
    }
  };

  setTimeout(() => {
    mcpServer.stdin.write(JSON.stringify(listProductsRequest) + '\n');
  }, 1000);

  // Test 3: Call get_product tool
  console.log('3️⃣ Testing get_product tool...');
  const getProductRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'get_product',
      arguments: {
        id: 'test-product-id'
      }
    }
  };

  setTimeout(() => {
    mcpServer.stdin.write(JSON.stringify(getProductRequest) + '\n');
  }, 2000);

  // Wait for responses
  await new Promise(resolve => setTimeout(resolve, 5000));

  console.log('\n📊 Test Results:');
  console.log('Server Output Length:', serverOutput.length);
  console.log('Server Error Length:', serverError.length);

  if (serverOutput.includes('tools')) {
    console.log('✅ MCP server is responding to requests');
  } else {
    console.log('❌ MCP server may not be responding correctly');
  }

  // Clean up
  mcpServer.kill();
  console.log('\n🏁 Test completed!');
}

// Run the test
testMCPServer().catch(console.error);
