#!/usr/bin/env node

// Simple test script to verify Medusa backend connection
const MEDUSA_API_URL = 'http://localhost:9000';

async function testConnection() {
  console.log('Testing connection to Medusa backend...');

  try {
    // Test basic health check
    const healthResponse = await fetch(`${MEDUSA_API_URL}/health`);
    console.log(`Health check: ${healthResponse.status} ${healthResponse.statusText}`);

    if (healthResponse.ok) {
      const healthText = await healthResponse.text();
      console.log('Health response:', healthText);
    }

    // Test store API (public endpoint)
    console.log('\nTesting store API...');
    const storeResponse = await fetch(`${MEDUSA_API_URL}/store/products`);
    console.log(`Store products: ${storeResponse.status} ${storeResponse.statusText}`);

    if (storeResponse.ok) {
      const storeData = await storeResponse.json();
      console.log(`Found ${storeData.products?.length || 0} products in store`);
    }

    // Test admin API (may require auth)
    console.log('\nTesting admin API...');
    const adminResponse = await fetch(`${MEDUSA_API_URL}/admin/products`);
    console.log(`Admin products: ${adminResponse.status} ${adminResponse.statusText}`);

    if (adminResponse.ok) {
      const adminData = await adminResponse.json();
      console.log(`Found ${adminData.products?.length || 0} products in admin`);
    } else if (adminResponse.status === 401) {
      console.log('Admin API requires authentication (expected)');
    }

    console.log('\n✅ Medusa backend is accessible!');
  } catch (error) {
    console.error('❌ Error connecting to Medusa backend:', error.message);
  }
}

testConnection();
