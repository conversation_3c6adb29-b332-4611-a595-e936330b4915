/**
 * MCP (Model Context Protocol) Routes
 * Provides MCP tool execution and management endpoints
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';
import { MCPService } from '../../services/mcp-service';

export default (rootDirectory: string): Router => {
  const router = Router();
  const mcpService = new MCPService();

  // Middleware
  router.use(validateTenant);

  /**
   * List Available MCP Tools
   * GET /mcp/tools
   */
  router.get('/tools', asyncHandler(async (req: Request, res: Response) => {
    const tools = await mcpService.listTools();
    
    res.json({
      tools,
      total_count: tools.length,
      timestamp: new Date().toISOString(),
    });
  }));

  /**
   * Execute MCP Tool
   * POST /mcp/tools/execute
   */
  router.post('/tools/execute', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const { tool, parameters } = req.body;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tool) {
      return res.status(400).json({
        error: 'Tool name is required',
        code: 'MISSING_TOOL_NAME',
      });
    }

    try {
      const result = await mcpService.executeTool(tool, parameters, {
        tenantId,
        userId: req.user?.id,
        scope: req.scope,
      });

      res.json({
        success: true,
        tool,
        result,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        tool,
        error: error.message,
        code: 'TOOL_EXECUTION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Tool Schema
   * GET /mcp/tools/:toolName/schema
   */
  router.get('/tools/:toolName/schema', asyncHandler(async (req: Request, res: Response) => {
    const { toolName } = req.params;
    
    try {
      const schema = await mcpService.getToolSchema(toolName);
      
      if (!schema) {
        return res.status(404).json({
          error: `Tool '${toolName}' not found`,
          code: 'TOOL_NOT_FOUND',
        });
      }

      res.json({
        tool: toolName,
        schema,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'SCHEMA_RETRIEVAL_ERROR',
      });
    }
  }));

  /**
   * Batch Execute Tools
   * POST /mcp/tools/batch
   */
  router.post('/tools/batch', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const { operations } = req.body;
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!Array.isArray(operations)) {
      return res.status(400).json({
        error: 'Operations must be an array',
        code: 'INVALID_OPERATIONS_FORMAT',
      });
    }

    const results = [];
    const errors = [];

    for (const operation of operations) {
      try {
        const result = await mcpService.executeTool(operation.tool, operation.parameters, {
          tenantId,
          userId: req.user?.id,
          scope: req.scope,
        });
        
        results.push({
          tool: operation.tool,
          success: true,
          result,
        });
      } catch (error: any) {
        errors.push({
          tool: operation.tool,
          success: false,
          error: error.message,
        });
      }
    }

    res.json({
      batch_id: `batch_${Date.now()}`,
      total_operations: operations.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
      timestamp: new Date().toISOString(),
    });
  }));

  /**
   * MCP Health Check
   * GET /mcp/health
   */
  router.get('/health', asyncHandler(async (req: Request, res: Response) => {
    const health = await mcpService.getHealth();
    
    res.json({
      status: 'healthy',
      mcp_version: '1.0.0',
      tools_available: health.toolsCount,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      ...health,
    });
  }));

  /**
   * MCP Metrics
   * GET /mcp/metrics
   */
  router.get('/metrics', asyncHandler(async (req: Request, res: Response) => {
    const { period = '24h' } = req.query;
    const metrics = await mcpService.getMetrics(period as string);
    
    res.json({
      period,
      metrics,
      timestamp: new Date().toISOString(),
    });
  }));

  /**
   * Tool Execution History
   * GET /mcp/history
   */
  router.get('/history', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const { limit = 50, offset = 0, tool } = req.query;
    const tenantId = req.headers['x-tenant-id'] as string;

    const history = await mcpService.getExecutionHistory({
      tenantId,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      tool: tool as string,
    });

    res.json({
      history: history.executions,
      total_count: history.total,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      timestamp: new Date().toISOString(),
    });
  }));

  return router;
};
