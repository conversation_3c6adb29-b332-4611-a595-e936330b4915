/**
 * Cart Management Routes
 * Shopping cart operations and checkout functionality
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);

  /**
   * Create Cart
   * POST /api/carts
   */
  router.post('/', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const {
      region_id,
      sales_channel_id,
      customer_id,
      email,
      currency_code,
      metadata = {},
    } = req.body;

    try {
      const cartData = {
        region_id,
        sales_channel_id,
        customer_id,
        email,
        currency_code,
        metadata: {
          ...metadata,
          tenant_id: req.headers['x-tenant-id'],
          created_via: 'api',
        },
      };

      const cart = await cartService.create(cartData);

      res.status(201).json({
        cart,
        message: 'Cart created successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CART_CREATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Cart
   * GET /api/carts/:id
   */
  router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const { expand = 'items,region,customer,shipping_address,billing_address' } = req.query;

    try {
      const cart = await cartService.retrieve(id, {
        relations: expand.toString().split(','),
      });

      if (!cart) {
        return res.status(404).json({
          error: 'Cart not found',
          code: 'CART_NOT_FOUND',
          cart_id: id,
        });
      }

      // Add computed fields
      const enhancedCart = {
        ...cart,
        computed: {
          total_items: cart.items?.length || 0,
          total_quantity: this.calculateTotalQuantity(cart.items),
          estimated_total: this.calculateEstimatedTotal(cart),
          can_checkout: this.canCheckout(cart),
          shipping_required: this.requiresShipping(cart.items),
        },
      };

      res.json({
        cart: enhancedCart,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CART_RETRIEVAL_ERROR',
        cart_id: id,
      });
    }
  }));

  /**
   * Add Item to Cart
   * POST /api/carts/:id/line-items
   */
  router.post('/:id/line-items', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const { variant_id, quantity, metadata = {} } = req.body;

    if (!variant_id || !quantity) {
      return res.status(400).json({
        error: 'Variant ID and quantity are required',
        code: 'MISSING_REQUIRED_FIELDS',
      });
    }

    if (quantity <= 0) {
      return res.status(400).json({
        error: 'Quantity must be greater than 0',
        code: 'INVALID_QUANTITY',
      });
    }

    try {
      // Check variant availability
      const productVariantService = req.scope.resolve('productVariantService');
      const variant = await productVariantService.retrieve(variant_id);
      
      if (!variant) {
        return res.status(404).json({
          error: 'Product variant not found',
          code: 'VARIANT_NOT_FOUND',
          variant_id,
        });
      }

      if (variant.inventory_quantity < quantity) {
        return res.status(400).json({
          error: 'Insufficient inventory',
          code: 'INSUFFICIENT_INVENTORY',
          available_quantity: variant.inventory_quantity,
          requested_quantity: quantity,
        });
      }

      const lineItem = await cartService.addLineItem(id, {
        variant_id,
        quantity,
        metadata,
      });

      const updatedCart = await cartService.retrieve(id, {
        relations: ['items', 'region'],
      });

      res.json({
        cart: updatedCart,
        line_item: lineItem,
        message: 'Item added to cart successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ADD_TO_CART_ERROR',
        cart_id: id,
      });
    }
  }));

  /**
   * Update Cart Item
   * PUT /api/carts/:id/line-items/:line_id
   */
  router.put('/:id/line-items/:line_id', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id, line_id } = req.params;
    const { quantity, metadata } = req.body;

    if (quantity !== undefined && quantity <= 0) {
      return res.status(400).json({
        error: 'Quantity must be greater than 0',
        code: 'INVALID_QUANTITY',
      });
    }

    try {
      const updateData: any = {};
      if (quantity !== undefined) updateData.quantity = quantity;
      if (metadata !== undefined) updateData.metadata = metadata;

      await cartService.updateLineItem(id, line_id, updateData);

      const updatedCart = await cartService.retrieve(id, {
        relations: ['items', 'region'],
      });

      res.json({
        cart: updatedCart,
        message: 'Cart item updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'UPDATE_CART_ITEM_ERROR',
        cart_id: id,
        line_id,
      });
    }
  }));

  /**
   * Remove Item from Cart
   * DELETE /api/carts/:id/line-items/:line_id
   */
  router.delete('/:id/line-items/:line_id', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id, line_id } = req.params;

    try {
      await cartService.removeLineItem(id, line_id);

      const updatedCart = await cartService.retrieve(id, {
        relations: ['items', 'region'],
      });

      res.json({
        cart: updatedCart,
        message: 'Item removed from cart successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'REMOVE_CART_ITEM_ERROR',
        cart_id: id,
        line_id,
      });
    }
  }));

  /**
   * Update Cart Customer
   * PUT /api/carts/:id/customer
   */
  router.put('/:id/customer', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const { customer_id, email } = req.body;

    try {
      const updateData: any = {};
      if (customer_id) updateData.customer_id = customer_id;
      if (email) updateData.email = email;

      const cart = await cartService.update(id, updateData);

      res.json({
        cart,
        message: 'Cart customer updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'UPDATE_CART_CUSTOMER_ERROR',
        cart_id: id,
      });
    }
  }));

  /**
   * Add Shipping Address
   * POST /api/carts/:id/shipping-address
   */
  router.post('/:id/shipping-address', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const addressData = req.body;

    try {
      const cart = await cartService.update(id, {
        shipping_address: addressData,
      });

      res.json({
        cart,
        message: 'Shipping address added successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ADD_SHIPPING_ADDRESS_ERROR',
        cart_id: id,
      });
    }
  }));

  /**
   * Add Billing Address
   * POST /api/carts/:id/billing-address
   */
  router.post('/:id/billing-address', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const addressData = req.body;

    try {
      const cart = await cartService.update(id, {
        billing_address: addressData,
      });

      res.json({
        cart,
        message: 'Billing address added successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ADD_BILLING_ADDRESS_ERROR',
        cart_id: id,
      });
    }
  }));

  /**
   * Apply Discount/Promotion
   * POST /api/carts/:id/discounts
   */
  router.post('/:id/discounts', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        error: 'Discount code is required',
        code: 'MISSING_DISCOUNT_CODE',
      });
    }

    try {
      const cart = await cartService.update(id, {
        discounts: [{ code }],
      });

      res.json({
        cart,
        message: 'Discount applied successfully',
        discount_code: code,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'APPLY_DISCOUNT_ERROR',
        cart_id: id,
        discount_code: code,
      });
    }
  }));

  /**
   * Complete Cart (Checkout)
   * POST /api/carts/:id/complete
   */
  router.post('/:id/complete', asyncHandler(async (req: Request, res: Response) => {
    const cartService = req.scope.resolve('cartService');
    const { id } = req.params;

    try {
      const cart = await cartService.retrieve(id, {
        relations: ['items', 'customer', 'shipping_address', 'billing_address'],
      });

      if (!cart) {
        return res.status(404).json({
          error: 'Cart not found',
          code: 'CART_NOT_FOUND',
          cart_id: id,
        });
      }

      if (!this.canCheckout(cart)) {
        return res.status(400).json({
          error: 'Cart cannot be completed',
          code: 'CART_NOT_READY_FOR_CHECKOUT',
          missing_requirements: this.getCheckoutRequirements(cart),
        });
      }

      const order = await cartService.complete(id);

      res.json({
        order,
        message: 'Order placed successfully',
        cart_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CART_COMPLETION_ERROR',
        cart_id: id,
      });
    }
  }));

  // Helper methods
  private calculateTotalQuantity(items: any[]): number {
    if (!items || items.length === 0) return 0;
    return items.reduce((total, item) => total + (item.quantity || 0), 0);
  }

  private calculateEstimatedTotal(cart: any): number {
    if (!cart.items || cart.items.length === 0) return 0;
    
    const subtotal = cart.items.reduce((total: number, item: any) => {
      const price = item.unit_price || 0;
      const quantity = item.quantity || 0;
      return total + (price * quantity);
    }, 0);

    // Add estimated tax and shipping (simplified calculation)
    const estimatedTax = subtotal * 0.1; // 10% tax
    const estimatedShipping = this.requiresShipping(cart.items) ? 500 : 0; // ₹5 shipping

    return subtotal + estimatedTax + estimatedShipping;
  }

  private canCheckout(cart: any): boolean {
    return (
      cart.items && 
      cart.items.length > 0 &&
      (cart.email || cart.customer_id) &&
      cart.shipping_address &&
      cart.billing_address
    );
  }

  private requiresShipping(items: any[]): boolean {
    if (!items || items.length === 0) return false;
    // Check if any item requires shipping (not digital)
    return items.some(item => !item.variant?.product?.is_digital);
  }

  private getCheckoutRequirements(cart: any): string[] {
    const missing = [];
    
    if (!cart.items || cart.items.length === 0) {
      missing.push('cart_items');
    }
    
    if (!cart.email && !cart.customer_id) {
      missing.push('customer_email');
    }
    
    if (!cart.shipping_address) {
      missing.push('shipping_address');
    }
    
    if (!cart.billing_address) {
      missing.push('billing_address');
    }

    return missing;
  }

  return router;
};
