/**
 * Inventory Management Routes
 * Stock management and inventory tracking endpoints
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);
  router.use(requireAuth);

  /**
   * List Inventory Items
   * GET /api/inventory
   */
  router.get('/', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const {
      limit = 10,
      offset = 0,
      location_id,
      sku,
      low_stock_threshold,
      q,
      sort_by = 'created_at',
      sort_order = 'DESC',
    } = req.query;

    const filters: any = {};

    if (location_id) {
      filters.location_id = location_id;
    }

    if (sku) {
      filters.sku = { $ilike: `%${sku}%` };
    }

    if (q) {
      filters.$or = [
        { sku: { $ilike: `%${q}%` } },
        { title: { $ilike: `%${q}%` } },
      ];
    }

    try {
      const [items, count] = await Promise.all([
        inventoryService.list(filters, {
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { [sort_by as string]: sort_order },
          relations: ['location', 'variant'],
        }),
        inventoryService.count(filters),
      ]);

      // Add computed fields and filter by low stock if requested
      let enhancedItems = items.map((item: any) => ({
        ...item,
        computed: {
          is_low_stock: this.isLowStock(item),
          stock_status: this.getStockStatus(item),
          reserved_quantity: item.reserved_quantity || 0,
          available_quantity: (item.stocked_quantity || 0) - (item.reserved_quantity || 0),
        },
      }));

      if (low_stock_threshold) {
        const threshold = parseInt(low_stock_threshold as string);
        enhancedItems = enhancedItems.filter(item => 
          item.computed.available_quantity <= threshold
        );
      }

      res.json({
        inventory_items: enhancedItems,
        count: enhancedItems.length,
        total: count,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        has_more: count > parseInt(offset as string) + parseInt(limit as string),
        filters: {
          location_id,
          sku,
          low_stock_threshold,
          q,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'INVENTORY_LIST_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Inventory Item
   * GET /api/inventory/:id
   */
  router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { id } = req.params;

    try {
      const item = await inventoryService.retrieve(id, {
        relations: ['location', 'variant', 'reservations'],
      });

      if (!item) {
        return res.status(404).json({
          error: 'Inventory item not found',
          code: 'INVENTORY_ITEM_NOT_FOUND',
          item_id: id,
        });
      }

      const enhancedItem = {
        ...item,
        computed: {
          is_low_stock: this.isLowStock(item),
          stock_status: this.getStockStatus(item),
          reserved_quantity: item.reserved_quantity || 0,
          available_quantity: (item.stocked_quantity || 0) - (item.reserved_quantity || 0),
          stock_movements: await this.getStockMovements(id),
        },
      };

      res.json({
        inventory_item: enhancedItem,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'INVENTORY_RETRIEVAL_ERROR',
        item_id: id,
      });
    }
  }));

  /**
   * Update Inventory Levels
   * PUT /api/inventory/:id
   */
  router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { id } = req.params;
    const { stocked_quantity, reserved_quantity, metadata } = req.body;

    try {
      const updateData: any = {};
      
      if (stocked_quantity !== undefined) {
        updateData.stocked_quantity = stocked_quantity;
      }
      
      if (reserved_quantity !== undefined) {
        updateData.reserved_quantity = reserved_quantity;
      }

      if (metadata) {
        updateData.metadata = {
          ...metadata,
          updated_by: req.user?.id,
          updated_at: new Date().toISOString(),
        };
      }

      const item = await inventoryService.update(id, updateData);

      // Log stock movement
      await this.logStockMovement(id, {
        type: 'adjustment',
        quantity_change: stocked_quantity,
        reason: 'manual_adjustment',
        user_id: req.user?.id,
      });

      res.json({
        inventory_item: item,
        message: 'Inventory updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'INVENTORY_UPDATE_ERROR',
        item_id: id,
      });
    }
  }));

  /**
   * Bulk Update Inventory
   * POST /api/inventory/bulk-update
   */
  router.post('/bulk-update', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { updates } = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        error: 'Updates array is required',
        code: 'MISSING_UPDATES',
      });
    }

    const results = [];
    const errors = [];

    for (const update of updates) {
      try {
        const { id, stocked_quantity, reserved_quantity } = update;
        
        const item = await inventoryService.update(id, {
          stocked_quantity,
          reserved_quantity,
          metadata: {
            bulk_updated_by: req.user?.id,
            bulk_updated_at: new Date().toISOString(),
          },
        });

        // Log stock movement
        await this.logStockMovement(id, {
          type: 'bulk_adjustment',
          quantity_change: stocked_quantity,
          reason: 'bulk_update',
          user_id: req.user?.id,
        });
        
        results.push({
          item_id: id,
          success: true,
          item,
        });
      } catch (error: any) {
        errors.push({
          item_id: update.id,
          success: false,
          error: error.message,
        });
      }
    }

    res.json({
      bulk_update_id: `bulk_${Date.now()}`,
      total_items: updates.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
      timestamp: new Date().toISOString(),
    });
  }));

  /**
   * Reserve Inventory
   * POST /api/inventory/:id/reserve
   */
  router.post('/:id/reserve', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { id } = req.params;
    const { quantity, reason, order_id, line_item_id } = req.body;

    if (!quantity || quantity <= 0) {
      return res.status(400).json({
        error: 'Valid quantity is required',
        code: 'INVALID_QUANTITY',
      });
    }

    try {
      const reservation = await inventoryService.createReservation({
        inventory_item_id: id,
        quantity,
        reason,
        order_id,
        line_item_id,
        created_by: req.user?.id,
      });

      res.status(201).json({
        reservation,
        message: 'Inventory reserved successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'INVENTORY_RESERVATION_ERROR',
        item_id: id,
      });
    }
  }));

  /**
   * Release Inventory Reservation
   * DELETE /api/inventory/reservations/:reservation_id
   */
  router.delete('/reservations/:reservation_id', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { reservation_id } = req.params;

    try {
      await inventoryService.deleteReservation(reservation_id);

      res.json({
        message: 'Reservation released successfully',
        reservation_id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'RESERVATION_RELEASE_ERROR',
        reservation_id,
      });
    }
  }));

  /**
   * Get Stock Locations
   * GET /api/inventory/locations
   */
  router.get('/locations', asyncHandler(async (req: Request, res: Response) => {
    const stockLocationService = req.scope.resolve('stockLocationService');
    const { limit = 50, offset = 0 } = req.query;

    try {
      const [locations, count] = await Promise.all([
        stockLocationService.list({}, {
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
        }),
        stockLocationService.count({}),
      ]);

      res.json({
        locations,
        count: locations.length,
        total: count,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'LOCATIONS_LIST_ERROR',
      });
    }
  }));

  /**
   * Get Low Stock Report
   * GET /api/inventory/reports/low-stock
   */
  router.get('/reports/low-stock', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { threshold = 10, location_id } = req.query;

    try {
      const filters: any = {};
      if (location_id) {
        filters.location_id = location_id;
      }

      const items = await inventoryService.list(filters, {
        relations: ['variant', 'location'],
      });

      const lowStockItems = items.filter((item: any) => {
        const available = (item.stocked_quantity || 0) - (item.reserved_quantity || 0);
        return available <= parseInt(threshold as string);
      });

      const report = {
        total_items: items.length,
        low_stock_items: lowStockItems.length,
        threshold: parseInt(threshold as string),
        items: lowStockItems.map((item: any) => ({
          ...item,
          available_quantity: (item.stocked_quantity || 0) - (item.reserved_quantity || 0),
        })),
      };

      res.json({
        report,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'LOW_STOCK_REPORT_ERROR',
      });
    }
  }));

  /**
   * Get Inventory Analytics
   * GET /api/inventory/analytics
   */
  router.get('/analytics', asyncHandler(async (req: Request, res: Response) => {
    const inventoryService = req.scope.resolve('inventoryService');
    const { period = '30d', location_id } = req.query;

    try {
      const analytics = await this.getInventoryAnalytics(inventoryService, period as string, location_id as string);

      res.json({
        analytics,
        period,
        location_id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'INVENTORY_ANALYTICS_ERROR',
      });
    }
  }));

  // Helper methods
  private isLowStock(item: any): boolean {
    const available = (item.stocked_quantity || 0) - (item.reserved_quantity || 0);
    const threshold = item.low_stock_threshold || 10;
    return available <= threshold;
  }

  private getStockStatus(item: any): string {
    const available = (item.stocked_quantity || 0) - (item.reserved_quantity || 0);
    
    if (available <= 0) return 'out_of_stock';
    if (this.isLowStock(item)) return 'low_stock';
    return 'in_stock';
  }

  private async getStockMovements(itemId: string): Promise<any[]> {
    // Mock implementation - get stock movement history
    return [
      {
        id: 'mov_1',
        type: 'adjustment',
        quantity_change: 100,
        reason: 'initial_stock',
        created_at: new Date().toISOString(),
      },
      {
        id: 'mov_2',
        type: 'sale',
        quantity_change: -5,
        reason: 'order_fulfillment',
        created_at: new Date().toISOString(),
      },
    ];
  }

  private async logStockMovement(itemId: string, movement: any): Promise<void> {
    // Mock implementation - log stock movement
    console.log(`Stock movement logged for item ${itemId}:`, movement);
  }

  private async getInventoryAnalytics(inventoryService: any, period: string, locationId?: string): Promise<any> {
    // Mock implementation - implement real inventory analytics
    return {
      total_items: 1250,
      total_stock_value: 450000,
      low_stock_items: 45,
      out_of_stock_items: 12,
      reserved_quantity: 150,
      available_quantity: 8500,
      stock_turnover_rate: 2.5,
      top_selling_items: [
        { sku: 'ITEM001', quantity_sold: 150 },
        { sku: 'ITEM002', quantity_sold: 120 },
      ],
      stock_movements: [
        { date: '2024-01-01', in: 100, out: 50 },
        { date: '2024-01-02', in: 80, out: 60 },
      ],
    };
  }

  return router;
};
