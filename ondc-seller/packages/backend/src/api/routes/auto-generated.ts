/**
 * Auto-generated API routes for ONDC Seller Platform
 * Custom endpoints for enhanced functionality
 */

import { Router } from 'express';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Health endpoint
  router.get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    });
  });

  // Version endpoint
  router.get('/version', (req, res) => {
    res.status(200).json({
      version: '1.0.0',
      api_version: 'v1',
      medusa_version: '1.20.0',
      endpoints: 8,
      timestamp: new Date().toISOString(),
    });
  });

  // Featured products endpoint
  router.get('/products/featured', async (req, res) => {
    try {
      const productService = req.scope.resolve('productService');
      const { limit = 6 } = req.query;

      const products = await productService.list(
        { is_giftcard: false, status: 'published' },
        {
          relations: ['variants', 'variants.prices', 'tags', 'images'],
          take: parseInt(limit as string),
          order: { created_at: 'DESC' },
        }
      );

      res.json({
        products,
        count: products.length,
        endpoint: 'featured',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching featured products:', error);
      res.status(500).json({
        error: error.message,
        endpoint: 'featured',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // ONDC specific endpoints
  router.get('/ondc/catalog', async (req, res) => {
    try {
      const productService = req.scope.resolve('productService');
      const { provider_id, location_id } = req.query;

      const products = await productService.list(
        {
          is_giftcard: false,
          status: 'published',
          ...(provider_id && { metadata: { provider_id } }),
        },
        {
          relations: ['variants', 'variants.prices', 'tags', 'images', 'collection'],
          take: 100,
        }
      );

      // Transform to ONDC catalog format
      const catalog = {
        descriptor: {
          name: 'ONDC Seller Catalog',
          code: 'ONDC-CAT-001',
          symbol: 'https://example.com/logo.png',
          short_desc: 'Complete product catalog for ONDC network',
          long_desc: 'Comprehensive catalog of products available through ONDC network',
          images: ['https://example.com/catalog-banner.png'],
        },
        providers: [
          {
            id: provider_id || 'default-provider',
            descriptor: {
              name: 'Default Provider',
              symbol: 'https://example.com/provider-logo.png',
              short_desc: 'Default product provider',
              long_desc: 'Default provider for ONDC products',
            },
            locations: [
              {
                id: location_id || 'default-location',
                gps: '12.9716,77.5946',
                address: {
                  locality: 'Bangalore',
                  street: 'MG Road',
                  city: 'Bangalore',
                  area_code: '560001',
                  state: 'Karnataka',
                },
              },
            ],
            items: products.map(product => ({
              id: product.id,
              descriptor: {
                name: product.title,
                code: product.handle,
                symbol: product.thumbnail,
                short_desc: product.description?.substring(0, 100),
                long_desc: product.description,
                images: product.images?.map(img => img.url) || [],
              },
              price: {
                currency: 'INR',
                value: product.variants?.[0]?.prices?.[0]?.amount?.toString() || '0',
                maximum_value: product.variants?.[0]?.prices?.[0]?.amount?.toString() || '0',
              },
              category_id: product.collection?.id || 'default-category',
              fulfillment_id: 'default-fulfillment',
              location_id: location_id || 'default-location',
              '@ondc/org/returnable': true,
              '@ondc/org/cancellable': true,
              '@ondc/org/return_window': 'P7D',
              '@ondc/org/seller_pickup_return': true,
              '@ondc/org/time_to_ship': 'PT2H',
              '@ondc/org/available_on_cod': true,
              '@ondc/org/contact_details_consumer_care': '<EMAIL>,+91-**********',
              tags:
                product.tags?.map(tag => ({
                  code: 'attribute',
                  list: [
                    {
                      code: 'brand',
                      value: tag.value,
                    },
                  ],
                })) || [],
            })),
          },
        ],
        fulfillments: [
          {
            id: 'default-fulfillment',
            type: 'Delivery',
            contact: {
              phone: '+91-**********',
              email: '<EMAIL>',
            },
          },
        ],
      };

      res.json({
        catalog,
        timestamp: new Date().toISOString(),
        provider_id,
        location_id,
      });
    } catch (error) {
      console.error('Error generating ONDC catalog:', error);
      res.status(500).json({
        error: error.message,
        endpoint: 'ondc/catalog',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // ONDC search endpoint
  router.post('/ondc/search', async (req, res) => {
    try {
      const productService = req.scope.resolve('productService');
      const { context, message } = req.body;

      const searchCriteria = message?.intent?.item?.descriptor?.name || '';
      const categoryId = message?.intent?.category?.id;
      const fulfillmentType = message?.intent?.fulfillment?.type;

      let searchOptions: any = {
        is_giftcard: false,
        status: 'published',
      };

      if (searchCriteria) {
        searchOptions.title = { $ilike: `%${searchCriteria}%` };
      }

      if (categoryId) {
        searchOptions.collection_id = categoryId;
      }

      const products = await productService.list(searchOptions, {
        relations: ['variants', 'variants.prices', 'tags', 'images', 'collection'],
        take: 20,
      });

      // Transform to ONDC search response format
      const searchResponse = {
        context: {
          ...context,
          action: 'on_search',
          timestamp: new Date().toISOString(),
        },
        message: {
          catalog: {
            'bpp/descriptor': {
              name: 'ONDC Seller Platform',
              symbol: 'https://example.com/logo.png',
              short_desc: 'ONDC compliant seller platform',
              long_desc: 'Complete e-commerce solution for ONDC network',
            },
            'bpp/providers': [
              {
                id: 'default-provider',
                descriptor: {
                  name: 'Default Provider',
                  symbol: 'https://example.com/provider-logo.png',
                  short_desc: 'Default product provider',
                },
                items: products.map(product => ({
                  id: product.id,
                  descriptor: {
                    name: product.title,
                    code: product.handle,
                    symbol: product.thumbnail,
                    short_desc: product.description?.substring(0, 100),
                    long_desc: product.description,
                  },
                  price: {
                    currency: 'INR',
                    value: product.variants?.[0]?.prices?.[0]?.amount?.toString() || '0',
                  },
                  category_id: product.collection?.id || 'default-category',
                  fulfillment_id: 'default-fulfillment',
                  location_id: 'default-location',
                })),
              },
            ],
          },
        },
      };

      res.json(searchResponse);
    } catch (error) {
      console.error('Error processing ONDC search:', error);
      res.status(500).json({
        error: error.message,
        endpoint: 'ondc/search',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Analytics endpoints
  router.get('/analytics/dashboard', async (req, res) => {
    try {
      const productService = req.scope.resolve('productService');
      const orderService = req.scope.resolve('orderService');

      // Get basic counts
      const [totalProducts, totalOrders] = await Promise.all([
        productService.count({ is_giftcard: false }),
        orderService.count({}),
      ]);

      // Get recent products
      const recentProducts = await productService.list(
        { is_giftcard: false },
        {
          take: 5,
          order: { created_at: 'DESC' },
          relations: ['variants', 'variants.prices'],
        }
      );

      res.json({
        summary: {
          total_products: totalProducts,
          total_orders: totalOrders,
          published_products: await productService.count({
            is_giftcard: false,
            status: 'published',
          }),
          draft_products: await productService.count({
            is_giftcard: false,
            status: 'draft',
          }),
        },
        recent_products: recentProducts,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      res.status(500).json({
        error: error.message,
        endpoint: 'analytics/dashboard',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Bulk operations endpoint
  router.post('/bulk/products/status', async (req, res) => {
    try {
      const productService = req.scope.resolve('productService');
      const { product_ids, status } = req.body;

      if (!product_ids || !Array.isArray(product_ids) || !status) {
        return res.status(400).json({
          error: 'product_ids (array) and status are required',
          endpoint: 'bulk/products/status',
        });
      }

      const validStatuses = ['draft', 'proposed', 'published', 'rejected'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
          endpoint: 'bulk/products/status',
        });
      }

      const updatePromises = product_ids.map(id => productService.update(id, { status }));

      const results = await Promise.allSettled(updatePromises);

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      res.json({
        message: `Bulk status update completed`,
        successful,
        failed,
        total: product_ids.length,
        new_status: status,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error in bulk status update:', error);
      res.status(500).json({
        error: error.message,
        endpoint: 'bulk/products/status',
        timestamp: new Date().toISOString(),
      });
    }
  });

  return router;
};
