/**
 * Enhanced Product Routes
 * Extended product management endpoints for e-commerce functionality
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);

  /**
   * List Products with Advanced Filtering
   * GET /api/products
   */
  router.get('/', asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const {
      limit = 10,
      offset = 0,
      category_id,
      collection_id,
      tags,
      price_min,
      price_max,
      status,
      q,
      sort_by = 'created_at',
      sort_order = 'DESC',
      expand = 'variants,images,categories,collection',
    } = req.query;

    // Build filter conditions
    const filters: any = {
      is_giftcard: false,
    };

    if (status) {
      filters.status = status;
    }

    if (category_id) {
      filters.categories = { id: category_id };
    }

    if (collection_id) {
      filters.collection_id = collection_id;
    }

    if (q) {
      filters.title = { $ilike: `%${q}%` };
    }

    // Build query options
    const queryOptions: any = {
      relations: expand.toString().split(','),
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
      order: { [sort_by as string]: sort_order },
    };

    try {
      const [products, count] = await Promise.all([
        productService.list(filters, queryOptions),
        productService.count(filters),
      ]);

      // Apply price filtering if specified (post-query filtering)
      let filteredProducts = products;
      if (price_min || price_max) {
        filteredProducts = products.filter((product: any) => {
          const price = product.variants?.[0]?.prices?.[0]?.amount || 0;
          const min = price_min ? parseInt(price_min as string) : 0;
          const max = price_max ? parseInt(price_max as string) : Infinity;
          return price >= min && price <= max;
        });
      }

      res.json({
        products: filteredProducts,
        count: filteredProducts.length,
        total: count,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        has_more: count > parseInt(offset as string) + parseInt(limit as string),
        filters: {
          category_id,
          collection_id,
          tags,
          price_min,
          price_max,
          status,
          q,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'PRODUCT_LIST_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Product with Enhanced Details
   * GET /api/products/:id
   */
  router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const { id } = req.params;
    const { expand = 'variants,images,categories,collection,tags' } = req.query;

    try {
      const product = await productService.retrieve(id, {
        relations: expand.toString().split(','),
      });

      if (!product) {
        return res.status(404).json({
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND',
          product_id: id,
        });
      }

      // Add computed fields
      const enhancedProduct = {
        ...product,
        computed: {
          price_range: this.calculatePriceRange(product.variants),
          availability: this.checkAvailability(product.variants),
          rating: await this.getProductRating(id),
          review_count: await this.getReviewCount(id),
        },
      };

      res.json({
        product: enhancedProduct,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'PRODUCT_RETRIEVAL_ERROR',
        product_id: id,
      });
    }
  }));

  /**
   * Create Product with Validation
   * POST /api/products
   */
  router.post('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const {
      title,
      description,
      handle,
      status = 'draft',
      thumbnail,
      images = [],
      collection_id,
      categories = [],
      tags = [],
      variants = [],
      options = [],
      metadata = {},
    } = req.body;

    // Validation
    if (!title) {
      return res.status(400).json({
        error: 'Product title is required',
        code: 'MISSING_TITLE',
      });
    }

    try {
      // Generate handle if not provided
      const productHandle = handle || title.toLowerCase().replace(/[^a-z0-9]+/g, '-');

      // Check if handle already exists
      const existingProduct = await productService.list({ handle: productHandle });
      if (existingProduct.length > 0) {
        return res.status(409).json({
          error: 'Product handle already exists',
          code: 'DUPLICATE_HANDLE',
          handle: productHandle,
        });
      }

      const productData = {
        title,
        description,
        handle: productHandle,
        status,
        thumbnail,
        images,
        collection_id,
        categories,
        tags,
        variants,
        options,
        metadata: {
          ...metadata,
          created_by: req.user?.id,
          tenant_id: req.headers['x-tenant-id'],
        },
      };

      const product = await productService.create(productData);

      res.status(201).json({
        product,
        message: 'Product created successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'PRODUCT_CREATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Update Product
   * PUT /api/products/:id
   */
  router.put('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const { id } = req.params;
    const updateData = req.body;

    try {
      // Add update metadata
      updateData.metadata = {
        ...updateData.metadata,
        updated_by: req.user?.id,
        updated_at: new Date().toISOString(),
      };

      const product = await productService.update(id, updateData);

      res.json({
        product,
        message: 'Product updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'PRODUCT_UPDATE_ERROR',
        product_id: id,
      });
    }
  }));

  /**
   * Delete Product
   * DELETE /api/products/:id
   */
  router.delete('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const { id } = req.params;

    try {
      await productService.delete(id);

      res.json({
        message: 'Product deleted successfully',
        product_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'PRODUCT_DELETION_ERROR',
        product_id: id,
      });
    }
  }));

  /**
   * Bulk Update Products
   * POST /api/products/bulk-update
   */
  router.post('/bulk-update', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const productService = req.scope.resolve('productService');
    const { product_ids, updates } = req.body;

    if (!Array.isArray(product_ids) || product_ids.length === 0) {
      return res.status(400).json({
        error: 'Product IDs array is required',
        code: 'MISSING_PRODUCT_IDS',
      });
    }

    const results = [];
    const errors = [];

    for (const productId of product_ids) {
      try {
        const product = await productService.update(productId, {
          ...updates,
          metadata: {
            ...updates.metadata,
            bulk_updated_by: req.user?.id,
            bulk_updated_at: new Date().toISOString(),
          },
        });
        
        results.push({
          product_id: productId,
          success: true,
          product,
        });
      } catch (error: any) {
        errors.push({
          product_id: productId,
          success: false,
          error: error.message,
        });
      }
    }

    res.json({
      bulk_update_id: `bulk_${Date.now()}`,
      total_products: product_ids.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
      timestamp: new Date().toISOString(),
    });
  }));

  /**
   * Get Product Categories
   * GET /api/products/categories
   */
  router.get('/categories', asyncHandler(async (req: Request, res: Response) => {
    const productCategoryService = req.scope.resolve('productCategoryService');
    const { limit = 50, offset = 0, parent_category_id, include_descendants_tree } = req.query;

    try {
      const filters: any = {};
      if (parent_category_id) {
        filters.parent_category_id = parent_category_id;
      }

      const categories = await productCategoryService.list(filters, {
        take: parseInt(limit as string),
        skip: parseInt(offset as string),
        relations: include_descendants_tree ? ['children'] : [],
      });

      res.json({
        categories,
        count: categories.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CATEGORY_LIST_ERROR',
      });
    }
  }));

  // Helper methods (these would typically be in a separate service)
  private calculatePriceRange(variants: any[]): { min: number; max: number } {
    if (!variants || variants.length === 0) {
      return { min: 0, max: 0 };
    }

    const prices = variants
      .flatMap(v => v.prices || [])
      .map(p => p.amount)
      .filter(Boolean);

    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  }

  private checkAvailability(variants: any[]): boolean {
    if (!variants || variants.length === 0) {
      return false;
    }

    return variants.some(v => v.inventory_quantity > 0);
  }

  private async getProductRating(productId: string): Promise<number> {
    // Mock implementation - integrate with review service
    return 4.5;
  }

  private async getReviewCount(productId: string): Promise<number> {
    // Mock implementation - integrate with review service
    return 23;
  }

  return router;
};
