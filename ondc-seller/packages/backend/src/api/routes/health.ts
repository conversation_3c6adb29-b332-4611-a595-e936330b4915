/**
 * Health and Monitoring Routes
 * System health checks and monitoring endpoints
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  /**
   * Basic Health Check
   * GET /api/health
   */
  router.get('/', asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      // Basic health indicators
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        response_time: Date.now() - startTime,
      };

      res.json(health);
    } catch (error: any) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Detailed Health Check
   * GET /api/health/detailed
   */
  router.get('/detailed', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        response_time: Date.now() - startTime,
        system: await this.getSystemHealth(),
        services: await this.getServicesHealth(req.scope),
        database: await this.getDatabaseHealth(req.scope),
        cache: await this.getCacheHealth(),
        external_services: await this.getExternalServicesHealth(),
      };

      // Determine overall status
      const allHealthy = [
        health.system.status === 'healthy',
        health.services.status === 'healthy',
        health.database.status === 'healthy',
        health.cache.status === 'healthy',
        health.external_services.status === 'healthy',
      ].every(Boolean);

      health.status = allHealthy ? 'healthy' : 'degraded';

      const statusCode = health.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error: any) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * System Metrics
   * GET /api/health/metrics
   */
  router.get('/metrics', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        system: {
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          uptime: process.uptime(),
          platform: process.platform,
          node_version: process.version,
        },
        application: {
          active_connections: await this.getActiveConnections(),
          request_count: await this.getRequestCount(),
          error_rate: await this.getErrorRate(),
          response_times: await this.getResponseTimes(),
        },
        database: await this.getDatabaseMetrics(req.scope),
        cache: await this.getCacheMetrics(),
      };

      res.json(metrics);
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'METRICS_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Readiness Check
   * GET /api/health/ready
   */
  router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
    try {
      const checks = {
        database: await this.checkDatabaseConnection(req.scope),
        cache: await this.checkCacheConnection(),
        external_apis: await this.checkExternalAPIs(),
      };

      const allReady = Object.values(checks).every(check => check.status === 'ready');

      const readiness = {
        status: allReady ? 'ready' : 'not_ready',
        checks,
        timestamp: new Date().toISOString(),
      };

      const statusCode = allReady ? 200 : 503;
      res.status(statusCode).json(readiness);
    } catch (error: any) {
      res.status(503).json({
        status: 'not_ready',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Liveness Check
   * GET /api/health/live
   */
  router.get('/live', asyncHandler(async (req: Request, res: Response) => {
    // Simple liveness check - if we can respond, we're alive
    res.json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  }));

  /**
   * Service Dependencies Status
   * GET /api/health/dependencies
   */
  router.get('/dependencies', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    try {
      const dependencies = {
        internal_services: {
          medusa_backend: await this.checkServiceHealth('http://localhost:9001/health'),
          strapi_cms: await this.checkServiceHealth('http://localhost:1339/health'),
          auth_service: await this.checkServiceHealth('http://localhost:3002/health'),
          notification_service: await this.checkServiceHealth('http://localhost:3003/health'),
          ondc_adapter: await this.checkServiceHealth('http://localhost:8080/health'),
        },
        external_services: {
          payment_gateway: await this.checkPaymentGatewayHealth(),
          email_service: await this.checkEmailServiceHealth(),
          sms_service: await this.checkSMSServiceHealth(),
          ondc_network: await this.checkONDCNetworkHealth(),
        },
        databases: {
          postgresql: await this.checkDatabaseConnection(req.scope),
          redis: await this.checkCacheConnection(),
        },
      };

      res.json({
        dependencies,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'DEPENDENCIES_CHECK_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  // Helper methods
  private async getSystemHealth(): Promise<any> {
    const memory = process.memoryUsage();
    const memoryUsagePercent = (memory.heapUsed / memory.heapTotal) * 100;

    return {
      status: memoryUsagePercent < 90 ? 'healthy' : 'warning',
      memory: {
        used: memory.heapUsed,
        total: memory.heapTotal,
        usage_percent: memoryUsagePercent,
      },
      uptime: process.uptime(),
      load_average: process.platform === 'linux' ? require('os').loadavg() : null,
    };
  }

  private async getServicesHealth(scope: any): Promise<any> {
    try {
      // Check if core services are available
      const productService = scope.resolve('productService');
      const orderService = scope.resolve('orderService');
      
      // Simple check - try to count records
      await Promise.all([
        productService.count({}),
        orderService.count({}),
      ]);

      return {
        status: 'healthy',
        services: ['productService', 'orderService'],
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async getDatabaseHealth(scope: any): Promise<any> {
    try {
      const manager = scope.resolve('manager');
      await manager.query('SELECT 1');
      
      return {
        status: 'healthy',
        connection: 'active',
        type: 'postgresql',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async getCacheHealth(): Promise<any> {
    try {
      // Mock implementation - check Redis connection
      return {
        status: 'healthy',
        connection: 'active',
        type: 'redis',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async getExternalServicesHealth(): Promise<any> {
    const services = {
      payment_gateway: await this.checkPaymentGatewayHealth(),
      email_service: await this.checkEmailServiceHealth(),
      ondc_network: await this.checkONDCNetworkHealth(),
    };

    const allHealthy = Object.values(services).every(service => service.status === 'healthy');

    return {
      status: allHealthy ? 'healthy' : 'degraded',
      services,
    };
  }

  private async checkServiceHealth(url: string): Promise<any> {
    try {
      // Mock implementation - make HTTP request to service health endpoint
      return {
        status: 'healthy',
        url,
        response_time: Math.floor(Math.random() * 100) + 50,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        url,
        error: error.message,
      };
    }
  }

  private async checkDatabaseConnection(scope: any): Promise<any> {
    try {
      const manager = scope.resolve('manager');
      const start = Date.now();
      await manager.query('SELECT 1');
      
      return {
        status: 'ready',
        response_time: Date.now() - start,
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error.message,
      };
    }
  }

  private async checkCacheConnection(): Promise<any> {
    try {
      // Mock implementation
      return {
        status: 'ready',
        response_time: 5,
      };
    } catch (error) {
      return {
        status: 'not_ready',
        error: error.message,
      };
    }
  }

  private async checkExternalAPIs(): Promise<any> {
    // Mock implementation
    return {
      status: 'ready',
      apis_checked: ['payment_gateway', 'email_service'],
    };
  }

  private async checkPaymentGatewayHealth(): Promise<any> {
    // Mock implementation
    return {
      status: 'healthy',
      provider: 'razorpay',
      response_time: 120,
    };
  }

  private async checkEmailServiceHealth(): Promise<any> {
    // Mock implementation
    return {
      status: 'healthy',
      provider: 'sendgrid',
      response_time: 80,
    };
  }

  private async checkSMSServiceHealth(): Promise<any> {
    // Mock implementation
    return {
      status: 'healthy',
      provider: 'twilio',
      response_time: 95,
    };
  }

  private async checkONDCNetworkHealth(): Promise<any> {
    // Mock implementation
    return {
      status: 'healthy',
      network: 'ondc_staging',
      response_time: 200,
    };
  }

  private async getActiveConnections(): Promise<number> {
    // Mock implementation
    return Math.floor(Math.random() * 100) + 10;
  }

  private async getRequestCount(): Promise<number> {
    // Mock implementation
    return Math.floor(Math.random() * 1000) + 500;
  }

  private async getErrorRate(): Promise<number> {
    // Mock implementation
    return Math.random() * 5; // 0-5% error rate
  }

  private async getResponseTimes(): Promise<any> {
    // Mock implementation
    return {
      avg: 150,
      p50: 120,
      p95: 300,
      p99: 500,
    };
  }

  private async getDatabaseMetrics(scope: any): Promise<any> {
    // Mock implementation
    return {
      connections: {
        active: 5,
        idle: 10,
        total: 15,
      },
      queries: {
        total: 1250,
        slow_queries: 3,
        avg_duration: 45,
      },
    };
  }

  private async getCacheMetrics(): Promise<any> {
    // Mock implementation
    return {
      hit_rate: 0.85,
      memory_usage: 0.65,
      keys: 1500,
      operations: {
        gets: 5000,
        sets: 1200,
        deletes: 300,
      },
    };
  }

  return router;
};
