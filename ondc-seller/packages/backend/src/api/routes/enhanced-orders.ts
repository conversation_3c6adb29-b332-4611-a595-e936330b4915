/**
 * Enhanced Order Routes
 * Extended order management endpoints for e-commerce functionality
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);

  /**
   * List Orders with Advanced Filtering
   * GET /api/orders
   */
  router.get('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const {
      limit = 10,
      offset = 0,
      status,
      fulfillment_status,
      payment_status,
      customer_id,
      date_from,
      date_to,
      q,
      sort_by = 'created_at',
      sort_order = 'DESC',
      expand = 'customer,items,shipping_address,billing_address',
    } = req.query;

    // Build filter conditions
    const filters: any = {};

    if (status) {
      filters.status = Array.isArray(status) ? status : [status];
    }

    if (fulfillment_status) {
      filters.fulfillment_status = Array.isArray(fulfillment_status) 
        ? fulfillment_status 
        : [fulfillment_status];
    }

    if (payment_status) {
      filters.payment_status = Array.isArray(payment_status) 
        ? payment_status 
        : [payment_status];
    }

    if (customer_id) {
      filters.customer_id = customer_id;
    }

    if (date_from || date_to) {
      filters.created_at = {};
      if (date_from) filters.created_at.$gte = new Date(date_from as string);
      if (date_to) filters.created_at.$lte = new Date(date_to as string);
    }

    if (q) {
      filters.$or = [
        { display_id: { $ilike: `%${q}%` } },
        { email: { $ilike: `%${q}%` } },
      ];
    }

    try {
      const [orders, count] = await Promise.all([
        orderService.list(filters, {
          relations: expand.toString().split(','),
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { [sort_by as string]: sort_order },
        }),
        orderService.count(filters),
      ]);

      // Add computed fields
      const enhancedOrders = orders.map((order: any) => ({
        ...order,
        computed: {
          total_items: order.items?.length || 0,
          estimated_delivery: this.calculateEstimatedDelivery(order),
          can_cancel: this.canCancelOrder(order),
          can_return: this.canReturnOrder(order),
        },
      }));

      res.json({
        orders: enhancedOrders,
        count: enhancedOrders.length,
        total: count,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        has_more: count > parseInt(offset as string) + parseInt(limit as string),
        filters: {
          status,
          fulfillment_status,
          payment_status,
          customer_id,
          date_from,
          date_to,
          q,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ORDER_LIST_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Order with Enhanced Details
   * GET /api/orders/:id
   */
  router.get('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { id } = req.params;
    const { expand = 'customer,items,shipping_address,billing_address,fulfillments,payments' } = req.query;

    try {
      const order = await orderService.retrieve(id, {
        relations: expand.toString().split(','),
      });

      if (!order) {
        return res.status(404).json({
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
          order_id: id,
        });
      }

      // Add computed fields and timeline
      const enhancedOrder = {
        ...order,
        computed: {
          total_items: order.items?.length || 0,
          estimated_delivery: this.calculateEstimatedDelivery(order),
          can_cancel: this.canCancelOrder(order),
          can_return: this.canReturnOrder(order),
          timeline: await this.getOrderTimeline(id),
        },
      };

      res.json({
        order: enhancedOrder,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ORDER_RETRIEVAL_ERROR',
        order_id: id,
      });
    }
  }));

  /**
   * Update Order Status
   * PUT /api/orders/:id/status
   */
  router.put('/:id/status', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { id } = req.params;
    const { status, reason, notes } = req.body;

    if (!status) {
      return res.status(400).json({
        error: 'Status is required',
        code: 'MISSING_STATUS',
      });
    }

    try {
      const order = await orderService.retrieve(id);
      
      if (!order) {
        return res.status(404).json({
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
          order_id: id,
        });
      }

      // Validate status transition
      if (!this.isValidStatusTransition(order.status, status)) {
        return res.status(400).json({
          error: `Invalid status transition from ${order.status} to ${status}`,
          code: 'INVALID_STATUS_TRANSITION',
        });
      }

      const updatedOrder = await orderService.update(id, {
        status,
        metadata: {
          ...order.metadata,
          status_history: [
            ...(order.metadata?.status_history || []),
            {
              from: order.status,
              to: status,
              reason,
              notes,
              updated_by: req.user?.id,
              updated_at: new Date().toISOString(),
            },
          ],
        },
      });

      res.json({
        order: updatedOrder,
        message: `Order status updated to ${status}`,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ORDER_STATUS_UPDATE_ERROR',
        order_id: id,
      });
    }
  }));

  /**
   * Cancel Order
   * POST /api/orders/:id/cancel
   */
  router.post('/:id/cancel', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { id } = req.params;
    const { reason, refund_amount } = req.body;

    try {
      const order = await orderService.retrieve(id);
      
      if (!order) {
        return res.status(404).json({
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND',
          order_id: id,
        });
      }

      if (!this.canCancelOrder(order)) {
        return res.status(400).json({
          error: 'Order cannot be cancelled',
          code: 'ORDER_NOT_CANCELLABLE',
          current_status: order.status,
        });
      }

      // Cancel the order
      const cancelledOrder = await orderService.cancel(id);

      // Process refund if specified
      if (refund_amount) {
        // Implement refund logic here
        await this.processRefund(order, refund_amount, reason);
      }

      res.json({
        order: cancelledOrder,
        message: 'Order cancelled successfully',
        refund_processed: !!refund_amount,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ORDER_CANCELLATION_ERROR',
        order_id: id,
      });
    }
  }));

  /**
   * Create Return Request
   * POST /api/orders/:id/returns
   */
  router.post('/:id/returns', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const returnService = req.scope.resolve('returnService');
    const { id } = req.params;
    const { items, reason, notes } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Return items are required',
        code: 'MISSING_RETURN_ITEMS',
      });
    }

    try {
      const returnRequest = await returnService.create({
        order_id: id,
        items,
        reason,
        notes,
        created_by: req.user?.id,
      });

      res.status(201).json({
        return: returnRequest,
        message: 'Return request created successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'RETURN_CREATION_ERROR',
        order_id: id,
      });
    }
  }));

  /**
   * Get Order Analytics
   * GET /api/orders/analytics
   */
  router.get('/analytics', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { period = '30d', group_by = 'day' } = req.query;

    try {
      const analytics = await this.getOrderAnalytics(orderService, period as string, group_by as string);

      res.json({
        analytics,
        period,
        group_by,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ORDER_ANALYTICS_ERROR',
      });
    }
  }));

  /**
   * Bulk Update Orders
   * POST /api/orders/bulk-update
   */
  router.post('/bulk-update', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { order_ids, updates } = req.body;

    if (!Array.isArray(order_ids) || order_ids.length === 0) {
      return res.status(400).json({
        error: 'Order IDs array is required',
        code: 'MISSING_ORDER_IDS',
      });
    }

    const results = [];
    const errors = [];

    for (const orderId of order_ids) {
      try {
        const order = await orderService.update(orderId, {
          ...updates,
          metadata: {
            ...updates.metadata,
            bulk_updated_by: req.user?.id,
            bulk_updated_at: new Date().toISOString(),
          },
        });
        
        results.push({
          order_id: orderId,
          success: true,
          order,
        });
      } catch (error: any) {
        errors.push({
          order_id: orderId,
          success: false,
          error: error.message,
        });
      }
    }

    res.json({
      bulk_update_id: `bulk_${Date.now()}`,
      total_orders: order_ids.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
      timestamp: new Date().toISOString(),
    });
  }));

  // Helper methods
  private calculateEstimatedDelivery(order: any): string | null {
    if (!order.shipping_methods || order.shipping_methods.length === 0) {
      return null;
    }

    // Mock calculation - implement based on shipping method and location
    const deliveryDays = 3; // Default delivery time
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + deliveryDays);
    
    return estimatedDate.toISOString();
  }

  private canCancelOrder(order: any): boolean {
    const cancellableStatuses = ['pending', 'requires_action'];
    return cancellableStatuses.includes(order.status);
  }

  private canReturnOrder(order: any): boolean {
    const returnableStatuses = ['completed'];
    const daysSinceCompletion = order.completed_at 
      ? Math.floor((Date.now() - new Date(order.completed_at).getTime()) / (1000 * 60 * 60 * 24))
      : 0;
    
    return returnableStatuses.includes(order.status) && daysSinceCompletion <= 30;
  }

  private isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions: Record<string, string[]> = {
      pending: ['requires_action', 'completed', 'canceled'],
      requires_action: ['pending', 'completed', 'canceled'],
      completed: ['canceled'], // Only allow cancellation of completed orders in special cases
      canceled: [], // Cannot transition from canceled
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  private async getOrderTimeline(orderId: string): Promise<any[]> {
    // Mock implementation - integrate with order history service
    return [
      {
        event: 'order_created',
        timestamp: new Date().toISOString(),
        description: 'Order was created',
      },
      {
        event: 'payment_authorized',
        timestamp: new Date().toISOString(),
        description: 'Payment was authorized',
      },
    ];
  }

  private async processRefund(order: any, amount: number, reason: string): Promise<void> {
    // Mock implementation - integrate with payment service
    console.log(`Processing refund of ${amount} for order ${order.id}, reason: ${reason}`);
  }

  private async getOrderAnalytics(orderService: any, period: string, groupBy: string): Promise<any> {
    // Mock implementation - implement real analytics
    return {
      total_orders: 150,
      total_revenue: 45000,
      average_order_value: 300,
      orders_by_status: {
        pending: 10,
        completed: 120,
        canceled: 20,
      },
      revenue_trend: [
        { date: '2024-01-01', revenue: 5000, orders: 20 },
        { date: '2024-01-02', revenue: 6000, orders: 25 },
      ],
    };
  }

  return router;
};
