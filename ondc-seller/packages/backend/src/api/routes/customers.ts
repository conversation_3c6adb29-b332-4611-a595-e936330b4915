/**
 * Customer Management Routes
 * Customer operations and profile management endpoints
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);

  /**
   * List Customers with Advanced Filtering
   * GET /api/customers
   */
  router.get('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const {
      limit = 10,
      offset = 0,
      q,
      group_id,
      has_account,
      date_from,
      date_to,
      sort_by = 'created_at',
      sort_order = 'DESC',
      expand = 'orders,addresses',
    } = req.query;

    // Build filter conditions
    const filters: any = {};

    if (q) {
      filters.$or = [
        { first_name: { $ilike: `%${q}%` } },
        { last_name: { $ilike: `%${q}%` } },
        { email: { $ilike: `%${q}%` } },
        { phone: { $ilike: `%${q}%` } },
      ];
    }

    if (group_id) {
      filters.groups = { id: group_id };
    }

    if (has_account !== undefined) {
      filters.has_account = has_account === 'true';
    }

    if (date_from || date_to) {
      filters.created_at = {};
      if (date_from) filters.created_at.$gte = new Date(date_from as string);
      if (date_to) filters.created_at.$lte = new Date(date_to as string);
    }

    try {
      const [customers, count] = await Promise.all([
        customerService.list(filters, {
          relations: expand.toString().split(','),
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { [sort_by as string]: sort_order },
        }),
        customerService.count(filters),
      ]);

      // Add computed fields
      const enhancedCustomers = customers.map((customer: any) => ({
        ...customer,
        computed: {
          total_orders: customer.orders?.length || 0,
          total_spent: this.calculateTotalSpent(customer.orders),
          last_order_date: this.getLastOrderDate(customer.orders),
          customer_lifetime_value: this.calculateCLV(customer),
        },
      }));

      res.json({
        customers: enhancedCustomers,
        count: enhancedCustomers.length,
        total: count,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        has_more: count > parseInt(offset as string) + parseInt(limit as string),
        filters: {
          q,
          group_id,
          has_account,
          date_from,
          date_to,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_LIST_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Get Customer with Enhanced Details
   * GET /api/customers/:id
   */
  router.get('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const { id } = req.params;
    const { expand = 'orders,addresses,groups' } = req.query;

    try {
      const customer = await customerService.retrieve(id, {
        relations: expand.toString().split(','),
      });

      if (!customer) {
        return res.status(404).json({
          error: 'Customer not found',
          code: 'CUSTOMER_NOT_FOUND',
          customer_id: id,
        });
      }

      // Add computed fields and analytics
      const enhancedCustomer = {
        ...customer,
        computed: {
          total_orders: customer.orders?.length || 0,
          total_spent: this.calculateTotalSpent(customer.orders),
          average_order_value: this.calculateAverageOrderValue(customer.orders),
          last_order_date: this.getLastOrderDate(customer.orders),
          customer_lifetime_value: this.calculateCLV(customer),
          preferred_categories: await this.getPreferredCategories(customer.orders),
          activity_timeline: await this.getCustomerTimeline(id),
        },
      };

      res.json({
        customer: enhancedCustomer,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_RETRIEVAL_ERROR',
        customer_id: id,
      });
    }
  }));

  /**
   * Create Customer
   * POST /api/customers
   */
  router.post('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const {
      email,
      first_name,
      last_name,
      phone,
      password,
      addresses = [],
      groups = [],
      metadata = {},
    } = req.body;

    // Validation
    if (!email) {
      return res.status(400).json({
        error: 'Email is required',
        code: 'MISSING_EMAIL',
      });
    }

    try {
      // Check if customer already exists
      const existingCustomer = await customerService.list({ email });
      if (existingCustomer.length > 0) {
        return res.status(409).json({
          error: 'Customer with this email already exists',
          code: 'DUPLICATE_EMAIL',
          email,
        });
      }

      const customerData = {
        email,
        first_name,
        last_name,
        phone,
        password,
        addresses,
        groups,
        metadata: {
          ...metadata,
          created_by: req.user?.id,
          tenant_id: req.headers['x-tenant-id'],
        },
      };

      const customer = await customerService.create(customerData);

      res.status(201).json({
        customer,
        message: 'Customer created successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_CREATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * Update Customer
   * PUT /api/customers/:id
   */
  router.put('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const { id } = req.params;
    const updateData = req.body;

    try {
      // Add update metadata
      updateData.metadata = {
        ...updateData.metadata,
        updated_by: req.user?.id,
        updated_at: new Date().toISOString(),
      };

      const customer = await customerService.update(id, updateData);

      res.json({
        customer,
        message: 'Customer updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_UPDATE_ERROR',
        customer_id: id,
      });
    }
  }));

  /**
   * Delete Customer
   * DELETE /api/customers/:id
   */
  router.delete('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const { id } = req.params;

    try {
      await customerService.delete(id);

      res.json({
        message: 'Customer deleted successfully',
        customer_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_DELETION_ERROR',
        customer_id: id,
      });
    }
  }));

  /**
   * Get Customer Orders
   * GET /api/customers/:id/orders
   */
  router.get('/:id/orders', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const orderService = req.scope.resolve('orderService');
    const { id } = req.params;
    const { limit = 10, offset = 0, status } = req.query;

    try {
      const filters: any = { customer_id: id };
      if (status) {
        filters.status = status;
      }

      const [orders, count] = await Promise.all([
        orderService.list(filters, {
          relations: ['items', 'shipping_address'],
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { created_at: 'DESC' },
        }),
        orderService.count(filters),
      ]);

      res.json({
        orders,
        count: orders.length,
        total: count,
        customer_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_ORDERS_ERROR',
        customer_id: id,
      });
    }
  }));

  /**
   * Add Customer Address
   * POST /api/customers/:id/addresses
   */
  router.post('/:id/addresses', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const { id } = req.params;
    const addressData = req.body;

    try {
      const address = await customerService.addAddress(id, addressData);

      res.status(201).json({
        address,
        message: 'Address added successfully',
        customer_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'ADDRESS_CREATION_ERROR',
        customer_id: id,
      });
    }
  }));

  /**
   * Get Customer Analytics
   * GET /api/customers/analytics
   */
  router.get('/analytics', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const customerService = req.scope.resolve('customerService');
    const { period = '30d', segment } = req.query;

    try {
      const analytics = await this.getCustomerAnalytics(customerService, period as string, segment as string);

      res.json({
        analytics,
        period,
        segment,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'CUSTOMER_ANALYTICS_ERROR',
      });
    }
  }));

  // Helper methods
  private calculateTotalSpent(orders: any[]): number {
    if (!orders || orders.length === 0) return 0;
    return orders.reduce((total, order) => total + (order.total || 0), 0);
  }

  private calculateAverageOrderValue(orders: any[]): number {
    if (!orders || orders.length === 0) return 0;
    const totalSpent = this.calculateTotalSpent(orders);
    return totalSpent / orders.length;
  }

  private getLastOrderDate(orders: any[]): string | null {
    if (!orders || orders.length === 0) return null;
    const sortedOrders = orders.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    return sortedOrders[0].created_at;
  }

  private calculateCLV(customer: any): number {
    // Simple CLV calculation - implement more sophisticated model
    const totalSpent = this.calculateTotalSpent(customer.orders);
    const orderCount = customer.orders?.length || 0;
    const avgOrderValue = orderCount > 0 ? totalSpent / orderCount : 0;
    
    // Assume customer will make 5 more orders (simple prediction)
    return totalSpent + (avgOrderValue * 5);
  }

  private async getPreferredCategories(orders: any[]): Promise<string[]> {
    // Mock implementation - analyze order items to find preferred categories
    return ['Electronics', 'Clothing', 'Books'];
  }

  private async getCustomerTimeline(customerId: string): Promise<any[]> {
    // Mock implementation - get customer activity timeline
    return [
      {
        event: 'customer_registered',
        timestamp: new Date().toISOString(),
        description: 'Customer registered',
      },
      {
        event: 'first_order',
        timestamp: new Date().toISOString(),
        description: 'Placed first order',
      },
    ];
  }

  private async getCustomerAnalytics(customerService: any, period: string, segment?: string): Promise<any> {
    // Mock implementation - implement real customer analytics
    return {
      total_customers: 1250,
      new_customers: 45,
      active_customers: 890,
      customer_retention_rate: 0.75,
      average_clv: 1200,
      segments: {
        high_value: 120,
        medium_value: 450,
        low_value: 680,
      },
      acquisition_channels: {
        organic: 40,
        paid_ads: 25,
        referral: 20,
        social: 15,
      },
    };
  }

  return router;
};
