/**
 * Webhook Routes
 * Webhook management and event handling endpoints
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../../middleware/error-handler';
import { requireAuth } from '../../middleware/auth';
import { validateTenant } from '../../middleware/tenant';

export default (rootDirectory: string): Router => {
  const router = Router();

  // Middleware
  router.use(validateTenant);

  /**
   * Register Webhook
   * POST /api/webhooks
   */
  router.post('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const {
      url,
      events,
      secret,
      description,
      is_active = true,
      metadata = {},
    } = req.body;

    if (!url || !events || !Array.isArray(events)) {
      return res.status(400).json({
        error: 'URL and events array are required',
        code: 'MISSING_REQUIRED_FIELDS',
      });
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return res.status(400).json({
        error: 'Invalid URL format',
        code: 'INVALID_URL',
      });
    }

    // Validate events
    const validEvents = this.getValidEvents();
    const invalidEvents = events.filter((event: string) => !validEvents.includes(event));
    
    if (invalidEvents.length > 0) {
      return res.status(400).json({
        error: 'Invalid events specified',
        code: 'INVALID_EVENTS',
        invalid_events: invalidEvents,
        valid_events: validEvents,
      });
    }

    try {
      const webhook = await webhookService.create({
        url,
        events,
        secret,
        description,
        is_active,
        metadata: {
          ...metadata,
          created_by: req.user?.id,
          tenant_id: req.headers['x-tenant-id'],
        },
      });

      res.status(201).json({
        webhook,
        message: 'Webhook registered successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_CREATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }
  }));

  /**
   * List Webhooks
   * GET /api/webhooks
   */
  router.get('/', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const {
      limit = 10,
      offset = 0,
      is_active,
      event,
    } = req.query;

    const filters: any = {};

    if (is_active !== undefined) {
      filters.is_active = is_active === 'true';
    }

    if (event) {
      filters.events = { $contains: [event] };
    }

    try {
      const [webhooks, count] = await Promise.all([
        webhookService.list(filters, {
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { created_at: 'DESC' },
        }),
        webhookService.count(filters),
      ]);

      // Add computed fields
      const enhancedWebhooks = webhooks.map((webhook: any) => ({
        ...webhook,
        computed: {
          total_deliveries: webhook.deliveries?.length || 0,
          last_delivery: this.getLastDelivery(webhook.deliveries),
          success_rate: this.calculateSuccessRate(webhook.deliveries),
        },
      }));

      res.json({
        webhooks: enhancedWebhooks,
        count: enhancedWebhooks.length,
        total: count,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        has_more: count > parseInt(offset as string) + parseInt(limit as string),
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_LIST_ERROR',
      });
    }
  }));

  /**
   * Get Webhook Details
   * GET /api/webhooks/:id
   */
  router.get('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { id } = req.params;

    try {
      const webhook = await webhookService.retrieve(id, {
        relations: ['deliveries'],
      });

      if (!webhook) {
        return res.status(404).json({
          error: 'Webhook not found',
          code: 'WEBHOOK_NOT_FOUND',
          webhook_id: id,
        });
      }

      const enhancedWebhook = {
        ...webhook,
        computed: {
          total_deliveries: webhook.deliveries?.length || 0,
          last_delivery: this.getLastDelivery(webhook.deliveries),
          success_rate: this.calculateSuccessRate(webhook.deliveries),
          recent_deliveries: webhook.deliveries?.slice(0, 10) || [],
        },
      };

      res.json({
        webhook: enhancedWebhook,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_RETRIEVAL_ERROR',
        webhook_id: id,
      });
    }
  }));

  /**
   * Update Webhook
   * PUT /api/webhooks/:id
   */
  router.put('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { id } = req.params;
    const updateData = req.body;

    // Validate events if provided
    if (updateData.events) {
      const validEvents = this.getValidEvents();
      const invalidEvents = updateData.events.filter((event: string) => !validEvents.includes(event));
      
      if (invalidEvents.length > 0) {
        return res.status(400).json({
          error: 'Invalid events specified',
          code: 'INVALID_EVENTS',
          invalid_events: invalidEvents,
        });
      }
    }

    try {
      updateData.metadata = {
        ...updateData.metadata,
        updated_by: req.user?.id,
        updated_at: new Date().toISOString(),
      };

      const webhook = await webhookService.update(id, updateData);

      res.json({
        webhook,
        message: 'Webhook updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_UPDATE_ERROR',
        webhook_id: id,
      });
    }
  }));

  /**
   * Delete Webhook
   * DELETE /api/webhooks/:id
   */
  router.delete('/:id', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { id } = req.params;

    try {
      await webhookService.delete(id);

      res.json({
        message: 'Webhook deleted successfully',
        webhook_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_DELETION_ERROR',
        webhook_id: id,
      });
    }
  }));

  /**
   * Test Webhook
   * POST /api/webhooks/:id/test
   */
  router.post('/:id/test', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { id } = req.params;
    const { event_type = 'test' } = req.body;

    try {
      const webhook = await webhookService.retrieve(id);
      
      if (!webhook) {
        return res.status(404).json({
          error: 'Webhook not found',
          code: 'WEBHOOK_NOT_FOUND',
          webhook_id: id,
        });
      }

      // Send test payload
      const testPayload = this.generateTestPayload(event_type);
      const delivery = await this.deliverWebhook(webhook, testPayload);

      res.json({
        delivery,
        message: 'Test webhook sent successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_TEST_ERROR',
        webhook_id: id,
      });
    }
  }));

  /**
   * Get Webhook Deliveries
   * GET /api/webhooks/:id/deliveries
   */
  router.get('/:id/deliveries', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { id } = req.params;
    const {
      limit = 10,
      offset = 0,
      status,
      event_type,
    } = req.query;

    try {
      const filters: any = { webhook_id: id };

      if (status) {
        filters.status = status;
      }

      if (event_type) {
        filters.event_type = event_type;
      }

      const [deliveries, count] = await Promise.all([
        webhookService.listDeliveries(filters, {
          take: parseInt(limit as string),
          skip: parseInt(offset as string),
          order: { created_at: 'DESC' },
        }),
        webhookService.countDeliveries(filters),
      ]);

      res.json({
        deliveries,
        count: deliveries.length,
        total: count,
        webhook_id: id,
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_DELIVERIES_ERROR',
        webhook_id: id,
      });
    }
  }));

  /**
   * Retry Webhook Delivery
   * POST /api/webhooks/deliveries/:delivery_id/retry
   */
  router.post('/deliveries/:delivery_id/retry', requireAuth, asyncHandler(async (req: Request, res: Response) => {
    const webhookService = req.scope.resolve('webhookService');
    const { delivery_id } = req.params;

    try {
      const delivery = await webhookService.retrieveDelivery(delivery_id);
      
      if (!delivery) {
        return res.status(404).json({
          error: 'Delivery not found',
          code: 'DELIVERY_NOT_FOUND',
          delivery_id,
        });
      }

      const webhook = await webhookService.retrieve(delivery.webhook_id);
      const retryDelivery = await this.deliverWebhook(webhook, delivery.payload);

      res.json({
        delivery: retryDelivery,
        message: 'Webhook delivery retried successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error: any) {
      res.status(500).json({
        error: error.message,
        code: 'WEBHOOK_RETRY_ERROR',
        delivery_id,
      });
    }
  }));

  /**
   * Get Available Events
   * GET /api/webhooks/events
   */
  router.get('/events', asyncHandler(async (req: Request, res: Response) => {
    const events = this.getValidEvents();
    const eventCategories = this.getEventCategories();

    res.json({
      events,
      categories: eventCategories,
      total_events: events.length,
      timestamp: new Date().toISOString(),
    });
  }));

  // Helper methods
  private getValidEvents(): string[] {
    return [
      'order.created',
      'order.updated',
      'order.completed',
      'order.cancelled',
      'payment.authorized',
      'payment.captured',
      'payment.failed',
      'payment.refunded',
      'product.created',
      'product.updated',
      'product.deleted',
      'inventory.updated',
      'customer.created',
      'customer.updated',
      'cart.created',
      'cart.updated',
      'fulfillment.created',
      'fulfillment.shipped',
      'fulfillment.delivered',
      'return.created',
      'return.approved',
      'return.rejected',
    ];
  }

  private getEventCategories(): Record<string, string[]> {
    return {
      orders: ['order.created', 'order.updated', 'order.completed', 'order.cancelled'],
      payments: ['payment.authorized', 'payment.captured', 'payment.failed', 'payment.refunded'],
      products: ['product.created', 'product.updated', 'product.deleted'],
      inventory: ['inventory.updated'],
      customers: ['customer.created', 'customer.updated'],
      carts: ['cart.created', 'cart.updated'],
      fulfillment: ['fulfillment.created', 'fulfillment.shipped', 'fulfillment.delivered'],
      returns: ['return.created', 'return.approved', 'return.rejected'],
    };
  }

  private getLastDelivery(deliveries: any[]): any | null {
    if (!deliveries || deliveries.length === 0) return null;
    return deliveries.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0];
  }

  private calculateSuccessRate(deliveries: any[]): number {
    if (!deliveries || deliveries.length === 0) return 0;
    const successful = deliveries.filter(d => d.status === 'success').length;
    return (successful / deliveries.length) * 100;
  }

  private generateTestPayload(eventType: string): any {
    const basePayload = {
      id: `evt_${Date.now()}`,
      type: eventType,
      created_at: new Date().toISOString(),
      test: true,
    };

    switch (eventType) {
      case 'order.created':
        return {
          ...basePayload,
          data: {
            id: 'order_test_123',
            status: 'pending',
            total: 1000,
            customer_email: '<EMAIL>',
          },
        };
      case 'payment.captured':
        return {
          ...basePayload,
          data: {
            id: 'pay_test_123',
            amount: 1000,
            status: 'captured',
            order_id: 'order_test_123',
          },
        };
      default:
        return {
          ...basePayload,
          data: {
            message: 'This is a test webhook payload',
          },
        };
    }
  }

  private async deliverWebhook(webhook: any, payload: any): Promise<any> {
    // Mock implementation - implement actual webhook delivery
    const delivery = {
      id: `del_${Date.now()}`,
      webhook_id: webhook.id,
      event_type: payload.type,
      payload,
      status: 'success',
      response_status: 200,
      response_body: 'OK',
      attempts: 1,
      created_at: new Date().toISOString(),
    };

    // In real implementation, make HTTP request to webhook.url
    // Handle retries, timeouts, and error responses

    return delivery;
  }

  return router;
};
