/**
 * Authentication Middleware
 * JWT token validation and user authentication
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { createError } from './error-handler';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    tenant_id?: string;
  };
}

/**
 * JWT Authentication Middleware
 */
export const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      throw createError('Authorization header required', 401, 'MISSING_AUTH_HEADER');
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

    if (!token) {
      throw createError('Authentication token required', 401, 'MISSING_TOKEN');
    }

    // Verify JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, jwtSecret) as any;

    // Attach user info to request
    req.user = {
      id: decoded.id || decoded.sub,
      email: decoded.email,
      role: decoded.role || 'user',
      tenant_id: decoded.tenant_id,
    };

    next();
  } catch (error: any) {
    if (error.name === 'JsonWebTokenError') {
      next(createError('Invalid authentication token', 401, 'INVALID_TOKEN'));
    } else if (error.name === 'TokenExpiredError') {
      next(createError('Authentication token expired', 401, 'TOKEN_EXPIRED'));
    } else {
      next(error);
    }
  }
};

/**
 * Development Mode Authentication
 * Uses hardcoded credentials for development
 */
export const devAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'development') {
    // Use hardcoded dev credentials
    req.user = {
      id: 'dev_user_123',
      email: '<EMAIL>',
      role: 'admin',
      tenant_id: 'dev_tenant',
    };
    return next();
  }

  // In non-development environments, use regular auth
  return requireAuth(req, res, next);
};

/**
 * Generate JWT Token
 */
export const generateToken = (payload: any, expiresIn: string = '24h'): string => {
  const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  return jwt.sign(payload, jwtSecret, { expiresIn });
};

/**
 * Login Handler for Development
 */
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password, username } = req.body;

    // Development mode - use hardcoded credentials
    if (process.env.NODE_ENV === 'development') {
      if (
        (username === 'demo' && password === 'demo') ||
        (email === '<EMAIL>' && password === 'demo')
      ) {
        const user = {
          id: 'dev_user_123',
          email: '<EMAIL>',
          username: 'demo',
          role: 'admin',
          tenant_id: 'dev_tenant',
        };

        const accessToken = generateToken(user);

        return res.json({
          success: true,
          user,
          access_token: accessToken,
          token_type: 'Bearer',
          expires_in: 86400,
          timestamp: new Date().toISOString(),
        });
      }
    }

    throw createError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
  } catch (error) {
    next(error);
  }
};

// Export types for TypeScript
export type { AuthenticatedRequest };
export const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      throw createError('Authorization header required', 401, 'MISSING_AUTH_HEADER');
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

    if (!token) {
      throw createError('Authentication token required', 401, 'MISSING_TOKEN');
    }

    // Verify JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, jwtSecret) as any;

    // Attach user info to request
    req.user = {
      id: decoded.id || decoded.sub,
      email: decoded.email,
      role: decoded.role || 'user',
      tenant_id: decoded.tenant_id,
    };

    next();
  } catch (error: any) {
    if (error.name === 'JsonWebTokenError') {
      next(createError('Invalid authentication token', 401, 'INVALID_TOKEN'));
    } else if (error.name === 'TokenExpiredError') {
      next(createError('Authentication token expired', 401, 'TOKEN_EXPIRED'));
    } else {
      next(error);
    }
  }
};

/**
 * Optional Authentication Middleware
 * Attaches user info if token is present, but doesn't require it
 */
export const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return next();
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

    if (!token) {
      return next();
    }

    // Verify JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, jwtSecret) as any;

    // Attach user info to request
    req.user = {
      id: decoded.id || decoded.sub,
      email: decoded.email,
      role: decoded.role || 'user',
      tenant_id: decoded.tenant_id,
    };

    next();
  } catch (error: any) {
    // For optional auth, we don't throw errors for invalid tokens
    // Just proceed without user info
    next();
  }
};

/**
 * Role-based Authorization Middleware
 */
export const requireRole = (roles: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError('Authentication required', 401, 'AUTHENTICATION_REQUIRED'));
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(req.user.role)) {
      return next(
        createError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS', {
          required_roles: allowedRoles,
          user_role: req.user.role,
        })
      );
    }

    next();
  };
};

/**
 * Admin Authorization Middleware
 */
export const requireAdmin = requireRole(['admin', 'super_admin']);

/**
 * Development Mode Authentication
 * Uses hardcoded credentials for development
 */
export const devAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'development') {
    // Use hardcoded dev credentials
    req.user = {
      id: 'dev_user_123',
      email: '<EMAIL>',
      role: 'admin',
      tenant_id: 'dev_tenant',
    };
    return next();
  }

  // In non-development environments, use regular auth
  return requireAuth(req, res, next);
};

/**
 * API Key Authentication Middleware
 */
export const requireApiKey = (req: Request, res: Response, next: NextFunction) => {
  try {
    const apiKey = req.headers['x-api-key'] as string;

    if (!apiKey) {
      throw createError('API key required', 401, 'MISSING_API_KEY');
    }

    // Validate API key (implement your validation logic)
    const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];

    if (!validApiKeys.includes(apiKey)) {
      throw createError('Invalid API key', 401, 'INVALID_API_KEY');
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Generate JWT Token
 */
export const generateToken = (payload: any, expiresIn: string = '24h'): string => {
  const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  return jwt.sign(payload, jwtSecret, { expiresIn });
};

/**
 * Verify JWT Token
 */
export const verifyToken = (token: string): any => {
  const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  return jwt.verify(token, jwtSecret);
};

/**
 * Refresh Token Middleware
 */
export const refreshToken = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const refreshToken = req.body.refresh_token || req.headers['x-refresh-token'];

    if (!refreshToken) {
      throw createError('Refresh token required', 401, 'MISSING_REFRESH_TOKEN');
    }

    // Verify refresh token
    const jwtSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret';
    const decoded = jwt.verify(refreshToken, jwtSecret) as any;

    // Generate new access token
    const newToken = generateToken({
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      tenant_id: decoded.tenant_id,
    });

    res.json({
      access_token: newToken,
      token_type: 'Bearer',
      expires_in: 86400, // 24 hours
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    if (error.name === 'JsonWebTokenError') {
      next(createError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN'));
    } else if (error.name === 'TokenExpiredError') {
      next(createError('Refresh token expired', 401, 'REFRESH_TOKEN_EXPIRED'));
    } else {
      next(error);
    }
  }
};

/**
 * Login Handler
 */
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password, username } = req.body;

    // Development mode - use hardcoded credentials
    if (process.env.NODE_ENV === 'development') {
      if (
        (username === 'demo' && password === 'demo') ||
        (email === '<EMAIL>' && password === 'demo')
      ) {
        const user = {
          id: 'dev_user_123',
          email: '<EMAIL>',
          username: 'demo',
          role: 'admin',
          tenant_id: 'dev_tenant',
        };

        const accessToken = generateToken(user);
        const refreshToken = generateToken(user, '7d');

        return res.json({
          success: true,
          user,
          access_token: accessToken,
          refresh_token: refreshToken,
          token_type: 'Bearer',
          expires_in: 86400,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Production mode - implement actual authentication
    // This would typically involve:
    // 1. Validate credentials against database
    // 2. Hash password comparison
    // 3. Generate tokens
    // 4. Return user info and tokens

    throw createError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
  } catch (error) {
    next(error);
  }
};

/**
 * Logout Handler
 */
export const logout = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // In a real implementation, you might:
    // 1. Blacklist the token
    // 2. Remove refresh token from database
    // 3. Clear any session data

    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get Current User
 */
export const getCurrentUser = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401, 'NOT_AUTHENTICATED');
    }

    // In a real implementation, you might fetch additional user data from database
    res.json({
      user: req.user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

// Export types for TypeScript
export type { AuthenticatedRequest };
