/**
 * Tenant Validation Middleware
 * Multi-tenant support and validation
 */

import { Request, Response, NextFunction } from 'express';
import { createError } from './error-handler';

interface TenantRequest extends Request {
  tenant?: {
    id: string;
    name: string;
    settings: any;
  };
}

/**
 * Validate Tenant Middleware
 */
export const validateTenant = (req: TenantRequest, res: Response, next: NextFunction) => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;

    // For development mode, use default tenant
    if (process.env.NODE_ENV === 'development') {
      req.tenant = {
        id: tenantId || 'dev_tenant',
        name: 'Development Tenant',
        settings: {
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          features: ['all'],
        },
      };
      return next();
    }

    if (!tenantId) {
      throw createError('Tenant ID required', 400, 'MISSING_TENANT_ID');
    }

    // Validate tenant exists and is active
    // In a real implementation, you would check against a database
    const validTenants = process.env.VALID_TENANTS?.split(',') || ['dev_tenant'];
    
    if (!validTenants.includes(tenantId)) {
      throw createError('Invalid tenant', 403, 'INVALID_TENANT', { tenant_id: tenantId });
    }

    // Mock tenant data - in real implementation, fetch from database
    req.tenant = {
      id: tenantId,
      name: `Tenant ${tenantId}`,
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['products', 'orders', 'customers'],
      },
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Require Tenant Feature
 */
export const requireFeature = (feature: string) => {
  return (req: TenantRequest, res: Response, next: NextFunction) => {
    if (!req.tenant) {
      return next(createError('Tenant validation required', 400, 'TENANT_VALIDATION_REQUIRED'));
    }

    const hasFeature = req.tenant.settings?.features?.includes(feature) || 
                      req.tenant.settings?.features?.includes('all');

    if (!hasFeature) {
      return next(createError(`Feature '${feature}' not available for this tenant`, 403, 'FEATURE_NOT_AVAILABLE', {
        feature,
        tenant_id: req.tenant.id,
        available_features: req.tenant.settings?.features || [],
      }));
    }

    next();
  };
};

/**
 * Get Tenant Settings
 */
export const getTenantSettings = (req: TenantRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.tenant) {
      throw createError('Tenant not found', 404, 'TENANT_NOT_FOUND');
    }

    res.json({
      tenant: req.tenant,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

// Export types for TypeScript
export type { TenantRequest };
