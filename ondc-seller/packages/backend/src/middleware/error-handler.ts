/**
 * Error Handler Middleware
 * Centralized error handling for API routes
 */

import { Request, Response, NextFunction } from 'express';

export interface CustomError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * Async handler wrapper to catch async errors
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Global error handler middleware
 */
export const errorHandler = (
  error: CustomError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = error.statusCode || 500;
  let message = error.message || 'Internal Server Error';
  let code = error.code || 'INTERNAL_ERROR';

  // Log error for debugging
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query,
    headers: req.headers,
    timestamp: new Date().toISOString(),
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = 'Validation failed';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    code = 'INVALID_ID';
    message = 'Invalid ID format';
  } else if (error.name === 'MongoError' && error.code === 11000) {
    statusCode = 409;
    code = 'DUPLICATE_ENTRY';
    message = 'Duplicate entry';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = 'INVALID_TOKEN';
    message = 'Invalid authentication token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    code = 'TOKEN_EXPIRED';
    message = 'Authentication token expired';
  }

  // Prepare error response
  const errorResponse: any = {
    success: false,
    error: message,
    code,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  // Add additional details in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = error.stack;
    errorResponse.details = error.details;
  }

  // Add request ID if available
  if (req.headers['x-request-id']) {
    errorResponse.request_id = req.headers['x-request-id'];
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    code: 'ROUTE_NOT_FOUND',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  // Generate request ID
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  req.headers['x-request-id'] = requestId;

  // Log request
  console.log('API Request:', {
    request_id: requestId,
    method: req.method,
    url: req.url,
    user_agent: req.headers['user-agent'],
    ip: req.ip,
    timestamp: new Date().toISOString(),
  });

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log('API Response:', {
      request_id: requestId,
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
    });
  });

  next();
};

/**
 * Rate limiting error handler
 */
export const rateLimitHandler = (req: Request, res: Response) => {
  res.status(429).json({
    success: false,
    error: 'Too many requests',
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Rate limit exceeded. Please try again later.',
    retry_after: 60, // seconds
    timestamp: new Date().toISOString(),
  });
};

/**
 * Validation error formatter
 */
export const formatValidationError = (errors: any[]): any => {
  return {
    message: 'Validation failed',
    code: 'VALIDATION_ERROR',
    errors: errors.map(error => ({
      field: error.path || error.field,
      message: error.message,
      value: error.value,
    })),
  };
};

/**
 * Create custom error
 */
export const createError = (
  message: string,
  statusCode: number = 500,
  code: string = 'CUSTOM_ERROR',
  details?: any
): CustomError => {
  const error = new Error(message) as CustomError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
};

/**
 * Database error handler
 */
export const handleDatabaseError = (error: any): CustomError => {
  if (error.code === '23505') {
    // PostgreSQL unique violation
    return createError('Duplicate entry', 409, 'DUPLICATE_ENTRY');
  } else if (error.code === '23503') {
    // PostgreSQL foreign key violation
    return createError('Referenced record not found', 400, 'FOREIGN_KEY_VIOLATION');
  } else if (error.code === '23502') {
    // PostgreSQL not null violation
    return createError('Required field missing', 400, 'REQUIRED_FIELD_MISSING');
  }

  return createError('Database error', 500, 'DATABASE_ERROR', error.message);
};

/**
 * External API error handler
 */
export const handleExternalAPIError = (error: any, service: string): CustomError => {
  if (error.response) {
    // HTTP error response
    return createError(
      `${service} API error: ${error.response.data?.message || error.message}`,
      error.response.status || 500,
      'EXTERNAL_API_ERROR',
      {
        service,
        status: error.response.status,
        data: error.response.data,
      }
    );
  } else if (error.request) {
    // Network error
    return createError(
      `${service} API network error`,
      503,
      'EXTERNAL_API_NETWORK_ERROR',
      { service }
    );
  }

  return createError(
    `${service} API error: ${error.message}`,
    500,
    'EXTERNAL_API_ERROR',
    { service }
  );
};
