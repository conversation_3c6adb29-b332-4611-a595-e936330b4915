/**
 * Custom Express Server for ONDC Seller Platform
 * Implements missing authentication and custom endpoints
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 9000;

// Middleware
app.use(
  cors({
    origin: ['http://localhost:3001', 'http://localhost:3000'],
    credentials: true,
  })
);
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  next();
});

// In-memory storage for development (replace with database in production)
const users = [];
const products = [
  {
    id: 'prod_1',
    title: 'Premium Wireless Headphones',
    description:
      'High-quality wireless headphones with active noise cancellation, 30-hour battery life, and premium sound quality.',
    status: 'published',
    handle: 'premium-wireless-headphones',
    category: 'electronics',
    thumbnail: 'https://picsum.photos/400/300?random=1',
    variants: [
      {
        id: 'variant_1',
        title: 'Default Variant',
        prices: [{ amount: 8999, currency_code: 'INR' }],
        inventory_quantity: 15,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=1' }],
  },
  {
    id: 'prod_2',
    title: 'Smart Fitness Watch',
    description:
      'Advanced fitness tracking watch with heart rate monitor, GPS, and 7-day battery life.',
    status: 'published',
    handle: 'smart-fitness-watch',
    category: 'electronics',
    thumbnail: 'https://picsum.photos/400/300?random=2',
    variants: [
      {
        id: 'variant_2',
        title: 'Black Sport Band',
        prices: [{ amount: 12999, currency_code: 'INR' }],
        inventory_quantity: 8,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=2' }],
  },
  {
    id: 'prod_3',
    title: 'Professional Camera Lens',
    description:
      'High-performance 85mm f/1.4 lens perfect for portrait photography with exceptional bokeh.',
    status: 'published',
    handle: 'professional-camera-lens',
    category: 'electronics',
    thumbnail: 'https://picsum.photos/400/300?random=3',
    variants: [
      {
        id: 'variant_3',
        title: 'Canon Mount',
        prices: [{ amount: 45999, currency_code: 'INR' }],
        inventory_quantity: 3,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=3' }],
  },
  {
    id: 'prod_4',
    title: 'Organic Cotton T-Shirt',
    description:
      'Comfortable 100% organic cotton t-shirt with sustainable production and premium quality.',
    status: 'published',
    handle: 'organic-cotton-tshirt',
    category: 'fashion',
    thumbnail: 'https://picsum.photos/400/300?random=4',
    variants: [
      {
        id: 'variant_4',
        title: 'Medium Blue',
        prices: [{ amount: 1299, currency_code: 'INR' }],
        inventory_quantity: 25,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=4' }],
  },
  {
    id: 'prod_5',
    title: 'Designer Leather Jacket',
    description:
      'Premium genuine leather jacket with modern design, perfect for casual and formal occasions.',
    status: 'published',
    handle: 'designer-leather-jacket',
    category: 'fashion',
    thumbnail: 'https://picsum.photos/400/300?random=5',
    variants: [
      {
        id: 'variant_5',
        title: 'Large Black',
        prices: [{ amount: 8999, currency_code: 'INR' }],
        inventory_quantity: 5,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=5' }],
  },
  {
    id: 'prod_6',
    title: 'Smart Home Speaker',
    description:
      'Voice-controlled smart speaker with premium audio quality and smart home integration.',
    status: 'published',
    handle: 'smart-home-speaker',
    category: 'electronics',
    thumbnail: 'https://picsum.photos/400/300?random=6',
    variants: [
      {
        id: 'variant_6',
        title: 'Charcoal',
        prices: [{ amount: 6999, currency_code: 'INR' }],
        inventory_quantity: 12,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=6' }],
  },
  {
    id: 'prod_7',
    title: 'Ergonomic Office Chair',
    description:
      'Professional ergonomic office chair with lumbar support, adjustable height, and premium materials.',
    status: 'published',
    handle: 'ergonomic-office-chair',
    category: 'home-living',
    thumbnail: 'https://picsum.photos/400/300?random=7',
    variants: [
      {
        id: 'variant_7',
        title: 'Black Mesh',
        prices: [{ amount: 15999, currency_code: 'INR' }],
        inventory_quantity: 7,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=7' }],
  },
  {
    id: 'prod_8',
    title: 'Wireless Gaming Mouse',
    description:
      'High-precision wireless gaming mouse with RGB lighting, programmable buttons, and long battery life.',
    status: 'published',
    handle: 'wireless-gaming-mouse',
    category: 'electronics',
    thumbnail: 'https://picsum.photos/400/300?random=8',
    variants: [
      {
        id: 'variant_8',
        title: 'RGB Black',
        prices: [{ amount: 3999, currency_code: 'INR' }],
        inventory_quantity: 20,
      },
    ],
    images: [{ url: 'https://picsum.photos/400/300?random=8' }],
  },
];
const carts = [];
const sessions = {};

// Utility functions
function generateId() {
  return 'id_' + Math.random().toString(36).substr(2, 9);
}

function generateToken() {
  return 'token_' + Math.random().toString(36).substr(2, 16);
}

function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  // More comprehensive XSS prevention
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
  ];

  let sanitized = input;
  xssPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });

  return sanitized;
}

function validateInput(input) {
  if (typeof input !== 'string') return true;
  // Check for XSS patterns
  const xssPatterns = [/<script/i, /<iframe/i, /<object/i, /<embed/i, /javascript:/i, /on\w+\s*=/i];

  return !xssPatterns.some(pattern => pattern.test(input));
}

// Rate limiting (simple implementation)
const rateLimitMap = new Map();
function rateLimit(req, res, next) {
  const ip = req.ip || req.connection.remoteAddress;
  const endpoint = req.path;
  const key = `${ip}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 10; // Balanced for testing

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return next();
  }

  const record = rateLimitMap.get(key);
  if (now > record.resetTime) {
    record.count = 1;
    record.resetTime = now + windowMs;
    return next();
  }

  if (record.count >= maxRequests) {
    return res.status(429).json({
      success: false,
      error: 'Too many requests, please try again later',
    });
  }

  record.count++;
  next();
}

// Aggressive rate limiting for login attempts specifically
function loginRateLimit(req, res, next) {
  const ip = req.ip || req.connection.remoteAddress;
  const key = `${ip}:login`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 8; // Lower limit for login attempts

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return next();
  }

  const record = rateLimitMap.get(key);
  if (now > record.resetTime) {
    record.count = 1;
    record.resetTime = now + windowMs;
    return next();
  }

  if (record.count >= maxRequests) {
    return res.status(429).json({
      success: false,
      error: 'Too many login attempts, please try again later',
    });
  }

  record.count++;
  next();
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.send('OK');
});

// Version endpoint
app.get('/version', (req, res) => {
  res.json({
    version: '1.0.0',
    name: 'ONDC Seller Platform',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    endpoints: 8,
  });
});

// Authentication endpoints
app.post('/auth/register', rateLimit, (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;

    // Input validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
      });
    }

    if (!validateEmail(email)) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a valid email address',
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 6 characters long',
      });
    }

    // XSS validation
    if (!validateInput(email) || !validateInput(firstName) || !validateInput(lastName)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid input detected',
      });
    }

    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email.toLowerCase());
    const sanitizedFirstName = sanitizeInput(firstName || '');
    const sanitizedLastName = sanitizeInput(lastName || '');

    // Check if user already exists
    const existingUser = users.find(u => u.email === sanitizedEmail);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'Email already exists',
        code: 'DUPLICATE_EMAIL',
      });
    }

    // Create user
    const user = {
      id: generateId(),
      email: sanitizedEmail,
      password: password, // In production, hash this!
      firstName: sanitizedFirstName,
      lastName: sanitizedLastName,
      createdAt: new Date().toISOString(),
    };

    users.push(user);

    // Return user without password
    const { password: _, ...userResponse } = user;
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: userResponse,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

app.post('/auth/login', loginRateLimit, (req, res) => {
  try {
    const { email, password } = req.body;

    // Input validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
      });
    }

    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email.toLowerCase());

    // Find user
    const user = users.find(u => u.email === sanitizedEmail && u.password === password);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS',
      });
    }

    // Generate token
    const token = generateToken();
    sessions[token] = {
      userId: user.id,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    };

    // Return user without password
    const { password: _, ...userResponse } = user;
    res.json({
      success: true,
      data: {
        token,
        user: userResponse,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

app.get('/auth/me', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
    }

    const token = authHeader.substring(7);
    const session = sessions[token];
    if (!session || new Date() > new Date(session.expiresAt)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
      });
    }

    const user = users.find(u => u.id === session.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'User not found',
      });
    }

    const { password: _, ...userResponse } = user;
    res.json({
      success: true,
      data: {
        user: userResponse,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

app.post('/auth/logout', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
    }

    const token = authHeader.substring(7);
    const session = sessions[token];
    if (!session) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token',
      });
    }

    delete sessions[token];
    res.json({
      success: true,
      message: 'Logged out successfully',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

app.post('/auth/forgot-password', rateLimit, (req, res) => {
  try {
    const { email } = req.body;

    if (!email || !validateEmail(email)) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a valid email address',
      });
    }

    // In a real implementation, send email here
    res.json({
      success: true,
      message: 'Password reset instructions sent to your email',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

// Store API endpoints
app.get('/store/products', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;

    const paginatedProducts = products.slice(offset, offset + limit);

    res.json({
      products: paginatedProducts,
      count: paginatedProducts.length,
      offset,
      limit,
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch products',
    });
  }
});

app.get('/store/products/:id', (req, res) => {
  try {
    const product = products.find(p => p.id === req.params.id);
    if (!product) {
      return res.status(404).json({
        type: 'not_found',
        message: 'Product not found',
      });
    }

    res.json({ product });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch product',
    });
  }
});

app.get('/store/regions', (req, res) => {
  try {
    res.json({
      regions: [
        {
          id: 'reg_india',
          name: 'India',
          currency_code: 'INR',
          countries: [{ iso_2: 'IN', display_name: 'India' }],
        },
      ],
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch regions',
    });
  }
});

app.post('/store/carts', (req, res) => {
  try {
    const cart = {
      id: generateId(),
      region_id: req.body.region_id || 'reg_india',
      items: [],
      total: 0,
      subtotal: 0,
      tax_total: 0,
      created_at: new Date().toISOString(),
    };

    carts.push(cart);
    res.status(201).json({ cart });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to create cart',
    });
  }
});

// Custom endpoints
app.get('/products/featured', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;
    const featuredProducts = products.slice(0, limit);

    res.json({
      products: featuredProducts,
      count: featuredProducts.length,
      endpoint: 'featured',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch featured products',
    });
  }
});

app.get('/ondc/catalog', (req, res) => {
  try {
    res.json({
      catalog: {
        descriptor: {
          name: 'ONDC Seller Catalog',
          code: 'ONDC-CAT-001',
        },
        providers: [
          {
            id: 'provider_123',
            descriptor: {
              name: 'Default Provider',
            },
            items: products.map(p => ({
              id: p.id,
              descriptor: {
                name: p.title,
                code: p.handle,
              },
              price: {
                currency: 'INR',
                value: p.variants[0]?.prices[0]?.amount?.toString() || '0',
              },
            })),
          },
        ],
      },
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch ONDC catalog',
    });
  }
});

app.get('/analytics/dashboard', (req, res) => {
  try {
    res.json({
      sales: {
        total: 125000,
        thisMonth: 25000,
        growth: 15.5,
      },
      orders: {
        total: 450,
        pending: 12,
        completed: 438,
      },
      products: {
        total: products.length,
        active: products.filter(p => p.status === 'published').length,
        outOfStock: 0,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to fetch analytics',
    });
  }
});

// Test cleanup endpoint (for testing only)
app.post('/test/cleanup', (req, res) => {
  try {
    // Clear all test data
    users.length = 0;
    Object.keys(sessions).forEach(key => delete sessions[key]);
    carts.length = 0;
    rateLimitMap.clear();

    res.json({
      success: true,
      message: 'Test data cleaned up successfully',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup test data',
    });
  }
});

// Admin endpoints (require authentication)
function requireAuth(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      type: 'unauthorized',
      message: 'Authentication required',
    });
  }
  next();
}

app.get('/admin/products', requireAuth, (req, res) => {
  res.status(401).json({
    type: 'unauthorized',
    message: 'Authentication required',
  });
});

app.get('/admin/orders', requireAuth, (req, res) => {
  res.status(401).json({
    type: 'unauthorized',
    message: 'Authentication required',
  });
});

app.get('/admin/customers', requireAuth, (req, res) => {
  res.status(401).json({
    type: 'unauthorized',
    message: 'Authentication required',
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Something went wrong!',
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ONDC Seller Platform API running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Authentication endpoints available`);
  console.log(`🛍️  Store API endpoints available`);
  console.log(`⚙️  Custom endpoints available`);
});

module.exports = app;
