openapi: 3.0.3
info:
  title: ONDC Seller Platform - Admin Analytics API
  description: |
    Comprehensive API specification for the ONDC Seller Platform Admin Analytics system.
    
    This API provides endpoints for:
    - User Activity Analytics with real-time metrics
    - Cart & Wishlist Management with privacy controls
    - Featured Products Management with drag-and-drop functionality
    
    ## Authentication
    - **Development Mode**: Use hardcoded credentials (demo/demo) or Bearer dev-token
    - **Production Mode**: OneSSO (Keycloak) JWT tokens
    
    ## Rate Limiting
    - 1000 requests per hour per user
    - 100 requests per minute for analytics endpoints
    
    ## Real-time Features
    - WebSocket connections for live updates
    - Supabase Realtime or RabbitMQ integration
  version: 1.0.0
  contact:
    name: ONDC Seller Platform Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000/api
    description: Development server
  - url: https://api.ondc-seller.com/api
    description: Production server

security:
  - BearerAuth: []
  - DevAuth: []

paths:
  /analytics/user-activity:
    get:
      summary: Get User Activity Analytics
      description: |
        Retrieve comprehensive user activity analytics including:
        - Real-time active users count
        - Visitor trends with device segmentation
        - Browser distribution statistics
        - Geographic distribution data
        - Device analytics breakdown
        - Top pages with performance metrics
      tags:
        - User Activity Analytics
      parameters:
        - name: dateRange
          in: query
          description: Date range for analytics data
          required: false
          schema:
            type: string
            enum: [1d, 7d, 30d, 90d]
            default: 7d
        - name: filters
          in: query
          description: Additional filters for data
          required: false
          schema:
            type: object
            properties:
              country:
                type: string
              deviceType:
                type: string
                enum: [desktop, mobile, tablet]
              browser:
                type: string
      responses:
        '200':
          description: User activity analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserActivityResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /analytics/cart-analytics:
    get:
      summary: Get Cart & Wishlist Analytics
      description: |
        Retrieve cart and wishlist management analytics including:
        - Active carts with user information
        - Abandoned carts with recovery insights
        - Wishlist data with user preferences
        - Conversion rates and metrics
        - Cart value analytics
      tags:
        - Cart & Wishlist Analytics
      responses:
        '200':
          description: Cart and wishlist analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CartAnalyticsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /analytics/featured-products:
    get:
      summary: Get Featured Products Analytics
      description: |
        Retrieve featured products management analytics including:
        - Current featured products with positions
        - Top selling products with sales data
        - Scheduled promotions with dates
        - Performance metrics and insights
      tags:
        - Featured Products Analytics
      responses:
        '200':
          description: Featured products analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeaturedProductsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /analytics/export:
    get:
      summary: Export Analytics Data
      description: |
        Export analytics data in various formats (CSV, JSON, XLSX).
        Supports all analytics types with date range filtering.
      tags:
        - Data Export
      parameters:
        - name: type
          in: query
          description: Type of analytics to export
          required: true
          schema:
            type: string
            enum: [user-activity, cart-analytics, featured-products]
        - name: format
          in: query
          description: Export format
          required: true
          schema:
            type: string
            enum: [csv, json, xlsx]
        - name: dateRange
          in: query
          description: Date range for export
          required: false
          schema:
            type: string
            enum: [1d, 7d, 30d, 90d]
            default: 7d
      responses:
        '200':
          description: Exported data file
          content:
            text/csv:
              schema:
                type: string
                format: binary
            application/json:
              schema:
                type: object
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /products/featured:
    get:
      summary: Get Featured Products
      description: Get all featured products with their positions and sections
      tags:
        - Featured Products Management
      responses:
        '200':
          description: List of featured products
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeaturedProductsListResponse'

    post:
      summary: Add Featured Product
      description: Add a product to featured products section
      tags:
        - Featured Products Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddFeaturedProductRequest'
      responses:
        '201':
          description: Featured product added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeaturedProductResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /products/featured/{id}:
    put:
      summary: Update Featured Product
      description: Update featured product position or section
      tags:
        - Featured Products Management
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeaturedProductRequest'
      responses:
        '200':
          description: Featured product updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeaturedProductResponse'
        '404':
          $ref: '#/components/responses/NotFoundError'

    delete:
      summary: Remove Featured Product
      description: Remove a product from featured products
      tags:
        - Featured Products Management
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Featured product removed successfully
        '404':
          $ref: '#/components/responses/NotFoundError'

  /promotions:
    get:
      summary: Get Scheduled Promotions
      description: Get all scheduled promotions with their products
      tags:
        - Promotions Management
      responses:
        '200':
          description: List of scheduled promotions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PromotionsListResponse'

    post:
      summary: Create Scheduled Promotion
      description: Create a new scheduled promotion
      tags:
        - Promotions Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePromotionRequest'
      responses:
        '201':
          description: Promotion created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PromotionResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: OneSSO (Keycloak) JWT token for production
    DevAuth:
      type: http
      scheme: bearer
      bearerFormat: token
      description: Development token (use "dev-token" for development mode)

  schemas:
    UserActivityResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            activeUsers:
              type: integer
              description: Current active users count
              example: 1247
            visitorTrends:
              type: array
              items:
                type: object
                properties:
                  date:
                    type: string
                    format: date
                  desktop:
                    type: integer
                  mobile:
                    type: integer
                  tablet:
                    type: integer
            browserDistribution:
              type: array
              items:
                type: object
                properties:
                  browser:
                    type: string
                  users:
                    type: integer
                  percentage:
                    type: number
                    format: float
            geographicData:
              type: array
              items:
                type: object
                properties:
                  country:
                    type: string
                  users:
                    type: integer
                  percentage:
                    type: number
                    format: float
                  flag:
                    type: string
            deviceAnalytics:
              type: array
              items:
                type: object
                properties:
                  device:
                    type: string
                  users:
                    type: integer
                  percentage:
                    type: number
                    format: float
            topPages:
              type: array
              items:
                type: object
                properties:
                  path:
                    type: string
                  views:
                    type: integer
                  avgSessionDuration:
                    type: integer
                  bounceRate:
                    type: number
                    format: float
        meta:
          type: object
          properties:
            total:
              type: integer
            dateRange:
              type: string
            generatedAt:
              type: string
              format: date-time

    CartAnalyticsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            activeCarts:
              type: array
              items:
                $ref: '#/components/schemas/CartItem'
            abandonedCarts:
              type: array
              items:
                $ref: '#/components/schemas/CartItem'
            wishlists:
              type: array
              items:
                $ref: '#/components/schemas/WishlistItem'
            metrics:
              type: object
              properties:
                totalActiveCarts:
                  type: integer
                totalAbandonedCarts:
                  type: integer
                totalWishlists:
                  type: integer
                conversionRate:
                  type: number
                  format: float
                averageCartValue:
                  type: number
                  format: float

    FeaturedProductsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            featuredProducts:
              type: array
              items:
                $ref: '#/components/schemas/FeaturedProduct'
            topSellingProducts:
              type: array
              items:
                $ref: '#/components/schemas/Product'
            scheduledPromotions:
              type: array
              items:
                $ref: '#/components/schemas/Promotion'
            performanceMetrics:
              type: object
              properties:
                totalFeaturedProducts:
                  type: integer
                totalTopSellingProducts:
                  type: integer
                totalScheduledPromotions:
                  type: integer

    CartItem:
      type: object
      properties:
        id:
          type: string
        user:
          $ref: '#/components/schemas/User'
        items:
          type: array
          items:
            type: object
            properties:
              product:
                $ref: '#/components/schemas/Product'
              quantity:
                type: integer
              price:
                type: number
                format: decimal
        total:
          type: number
          format: decimal
        lastUpdated:
          type: string
          format: date-time

    WishlistItem:
      type: object
      properties:
        id:
          type: string
        user:
          $ref: '#/components/schemas/User'
        items:
          type: array
          items:
            type: object
            properties:
              product:
                $ref: '#/components/schemas/Product'
              addedAt:
                type: string
                format: date-time

    FeaturedProduct:
      type: object
      properties:
        id:
          type: string
        product:
          $ref: '#/components/schemas/Product'
        position:
          type: integer
        section:
          type: string
          enum: [featured, top-selling, hot-deals]
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Product:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        price:
          type: number
          format: decimal
        image:
          type: string
          format: uri
        sales:
          type: integer
        revenue:
          type: number
          format: decimal
        isActive:
          type: boolean

    User:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        email:
          type: string
          format: email
        role:
          type: string
          enum: [ADMIN, SELLER, CUSTOMER]

    Promotion:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        isActive:
          type: boolean
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'

    AddFeaturedProductRequest:
      type: object
      required:
        - productId
        - position
        - section
      properties:
        productId:
          type: string
        position:
          type: integer
          minimum: 1
        section:
          type: string
          enum: [featured, top-selling, hot-deals]

    UpdateFeaturedProductRequest:
      type: object
      properties:
        position:
          type: integer
          minimum: 1
        section:
          type: string
          enum: [featured, top-selling, hot-deals]
        isActive:
          type: boolean

    CreatePromotionRequest:
      type: object
      required:
        - name
        - startDate
        - endDate
        - productIds
      properties:
        name:
          type: string
        description:
          type: string
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        productIds:
          type: array
          items:
            type: string

    FeaturedProductsListResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/FeaturedProduct'

    FeaturedProductResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/FeaturedProduct'

    PromotionsListResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/Promotion'

    PromotionResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/Promotion'

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
        code:
          type: string
        details:
          type: object

  responses:
    UnauthorizedError:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Authentication token required"
            code: "UNAUTHORIZED"

    BadRequestError:
      description: Invalid request parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Invalid request parameters"
            code: "BAD_REQUEST"

    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Resource not found"
            code: "NOT_FOUND"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "Internal server error"
            code: "INTERNAL_ERROR"

tags:
  - name: User Activity Analytics
    description: Real-time user activity tracking and analytics
  - name: Cart & Wishlist Analytics
    description: Cart and wishlist management with privacy controls
  - name: Featured Products Analytics
    description: Featured products performance and management
  - name: Featured Products Management
    description: CRUD operations for featured products
  - name: Promotions Management
    description: Scheduled promotions management
  - name: Data Export
    description: Export analytics data in various formats
