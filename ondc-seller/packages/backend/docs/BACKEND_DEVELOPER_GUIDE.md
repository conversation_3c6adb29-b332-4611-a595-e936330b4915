# 🔧 ONDC Seller Platform - Backend Developer Guide

## 📋 Table of Contents

1. [Backend Architecture](#backend-architecture)
2. [API Development](#api-development)
3. [Database Management](#database-management)
4. [Authentication & Authorization](#authentication--authorization)
5. [Real-time Features](#real-time-features)
6. [Analytics Implementation](#analytics-implementation)
7. [Testing Strategy](#testing-strategy)
8. [Performance Optimization](#performance-optimization)
9. [Deployment & DevOps](#deployment--devops)
10. [Monitoring & Logging](#monitoring--logging)

---

## 🏗️ Backend Architecture

### Technology Stack

```
Backend Stack:
├── Runtime: Node.js 18+
├── Framework: Express.js
├── Database: PostgreSQL (Supabase)
├── ORM: Prisma
├── Authentication: OneSSO (Keycloak)
├── Real-time: Supabase Realtime / RabbitMQ
├── Caching: Redis
├── File Storage: Supabase Storage
└── API Documentation: OpenAPI/Swagger
```

### Project Structure

```
packages/backend/
├── src/
│   ├── controllers/          # Route handlers
│   │   ├── analytics.ts     # Analytics endpoints
│   │   ├── auth.ts          # Authentication
│   │   ├── products.ts      # Product management
│   │   ├── users.ts         # User management
│   │   └── orders.ts        # Order processing
│   ├── middleware/          # Express middleware
│   │   ├── auth.ts          # Authentication middleware
│   │   ├── validation.ts    # Request validation
│   │   ├── rateLimit.ts     # Rate limiting
│   │   └── errorHandler.ts  # Error handling
│   ├── services/            # Business logic
│   │   ├── analytics.ts     # Analytics service
│   │   ├── cart.ts          # Cart management
│   │   ├── notifications.ts # Real-time notifications
│   │   └── products.ts      # Product service
│   ├── models/              # Database models
│   ├── routes/              # API routes
│   ├── utils/               # Utility functions
│   ├── config/              # Configuration
│   └── types/               # TypeScript definitions
├── prisma/                  # Database schema & migrations
├── tests/                   # Test files
├── docs/                    # API documentation
└── scripts/                 # Deployment scripts
```

### Environment Configuration

```bash
# .env.development
NODE_ENV=development
PORT=8000
DATABASE_URL="postgresql://postgres:password@localhost:5432/ondc_seller_dev"
REDIS_URL="redis://localhost:6379"

# Authentication
KEYCLOAK_URL="http://localhost:8080"
KEYCLOAK_REALM="ondc-seller"
KEYCLOAK_CLIENT_ID="ondc-backend"
KEYCLOAK_CLIENT_SECRET="your-client-secret"

# Development Auth (hardcoded)
DEV_AUTH_ENABLED=true
DEV_USERNAME="demo"
DEV_PASSWORD="demo"

# Supabase
SUPABASE_URL="http://localhost:54321"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# RabbitMQ (optional)
RABBITMQ_URL="amqp://localhost:5672"

# Monitoring
ENABLE_CONSOLE_MONITORING=true
LOG_LEVEL="debug"
```

---

## 🚀 API Development

### Express Server Setup

```typescript
// File: src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { rateLimitMiddleware } from './middleware/rateLimit';

// Import routes
import analyticsRoutes from './routes/analytics';
import productsRoutes from './routes/products';
import usersRoutes from './routes/users';
import ordersRoutes from './routes/orders';

const app = express();

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  })
);

// Performance middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
app.use(rateLimitMiddleware);

// Authentication middleware
app.use('/api', authMiddleware);

// API routes
app.use('/api/analytics', analyticsRoutes);
app.use('/api/products', productsRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/orders', ordersRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling
app.use(errorHandler);

export default app;
```

### Analytics API Implementation

```typescript
// File: src/controllers/analytics.ts
import { Request, Response } from 'express';
import { AnalyticsService } from '../services/analytics';
import { validateRequest } from '../utils/validation';
import { logger } from '../utils/logger';

export class AnalyticsController {
  async getUserActivity(req: Request, res: Response) {
    try {
      const { dateRange, filters } = validateRequest(req.query, {
        dateRange: 'string',
        filters: 'object',
      });

      const data = await AnalyticsService.getUserActivity({
        dateRange,
        filters,
      });

      logger.info('User activity analytics retrieved', {
        userId: req.user?.id,
        dateRange,
        recordCount: data.length,
      });

      res.json({
        success: true,
        data,
        meta: {
          total: data.length,
          dateRange,
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Failed to retrieve user activity analytics', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics data',
      });
    }
  }

  async getCartAnalytics(req: Request, res: Response) {
    try {
      const data = await AnalyticsService.getCartAnalytics();

      res.json({
        success: true,
        data: {
          activeCarts: data.activeCarts,
          abandonedCarts: data.abandonedCarts,
          conversionRate: data.conversionRate,
          averageCartValue: data.averageCartValue,
          topProducts: data.topProducts,
        },
      });
    } catch (error) {
      logger.error('Failed to retrieve cart analytics', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve cart analytics',
      });
    }
  }

  async getFeaturedProductsAnalytics(req: Request, res: Response) {
    try {
      const data = await AnalyticsService.getFeaturedProductsAnalytics();

      res.json({
        success: true,
        data: {
          featuredProducts: data.featuredProducts,
          topSellingProducts: data.topSellingProducts,
          scheduledPromotions: data.scheduledPromotions,
          performanceMetrics: data.performanceMetrics,
        },
      });
    } catch (error) {
      logger.error('Failed to retrieve featured products analytics', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve featured products analytics',
      });
    }
  }

  async exportAnalytics(req: Request, res: Response) {
    try {
      const { type, format, dateRange } = req.query;

      const data = await AnalyticsService.exportData({
        type: type as string,
        format: format as 'csv' | 'json' | 'xlsx',
        dateRange: dateRange as string,
      });

      const filename = `analytics-${type}-${Date.now()}.${format}`;

      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.setHeader('Content-Type', getContentType(format as string));

      res.send(data);
    } catch (error) {
      logger.error('Failed to export analytics data', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export analytics data',
      });
    }
  }
}

function getContentType(format: string): string {
  switch (format) {
    case 'csv':
      return 'text/csv';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    default:
      return 'application/json';
  }
}
```

### Analytics Service Implementation

```typescript
// File: src/services/analytics.ts
import { PrismaClient } from '@prisma/client';
import { subDays, startOfDay, endOfDay } from 'date-fns';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export class AnalyticsService {
  static async getUserActivity(options: { dateRange?: string; filters?: any }) {
    const { dateRange = '7d', filters = {} } = options;

    const startDate = this.getStartDate(dateRange);
    const endDate = new Date();

    try {
      // Get active users count
      const activeUsers = await prisma.userSession.count({
        where: {
          lastActivity: {
            gte: subDays(new Date(), 1),
          },
        },
      });

      // Get visitor trends
      const visitorTrends = await prisma.userActivity.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          userId: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Get browser distribution
      const browserStats = await prisma.userSession.groupBy({
        by: ['browser'],
        _count: {
          id: true,
        },
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get geographic distribution
      const geoStats = await prisma.userSession.groupBy({
        by: ['country'],
        _count: {
          id: true,
        },
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get device analytics
      const deviceStats = await prisma.userSession.groupBy({
        by: ['deviceType'],
        _count: {
          id: true,
        },
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get top pages
      const topPages = await prisma.pageView.groupBy({
        by: ['path'],
        _count: {
          id: true,
        },
        _avg: {
          sessionDuration: true,
          bounceRate: true,
        },
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      });

      return {
        activeUsers,
        visitorTrends: this.formatVisitorTrends(visitorTrends),
        browserDistribution: this.formatBrowserStats(browserStats),
        geographicData: this.formatGeoStats(geoStats),
        deviceAnalytics: this.formatDeviceStats(deviceStats),
        topPages: this.formatTopPages(topPages),
      };
    } catch (error) {
      logger.error('Error retrieving user activity analytics', error);
      throw new Error('Failed to retrieve user activity analytics');
    }
  }

  static async getCartAnalytics() {
    try {
      // Active carts
      const activeCarts = await prisma.cart.findMany({
        where: {
          status: 'active',
          updatedAt: {
            gte: subDays(new Date(), 7),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  name: true,
                  price: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      // Abandoned carts
      const abandonedCarts = await prisma.cart.findMany({
        where: {
          status: 'abandoned',
          updatedAt: {
            gte: subDays(new Date(), 30),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  name: true,
                  price: true,
                },
              },
            },
          },
        },
      });

      // Wishlists
      const wishlists = await prisma.wishlist.findMany({
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  name: true,
                  price: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      // Calculate metrics
      const totalCarts = activeCarts.length + abandonedCarts.length;
      const conversionRate = totalCarts > 0 ? (activeCarts.length / totalCarts) * 100 : 0;

      const averageCartValue =
        activeCarts.reduce((sum, cart) => {
          const cartTotal = cart.items.reduce(
            (itemSum, item) => itemSum + item.quantity * item.product.price,
            0
          );
          return sum + cartTotal;
        }, 0) / (activeCarts.length || 1);

      return {
        activeCarts,
        abandonedCarts,
        wishlists,
        conversionRate,
        averageCartValue,
        metrics: {
          totalActiveCarts: activeCarts.length,
          totalAbandonedCarts: abandonedCarts.length,
          totalWishlists: wishlists.length,
          conversionRate: Math.round(conversionRate * 100) / 100,
          averageCartValue: Math.round(averageCartValue * 100) / 100,
        },
      };
    } catch (error) {
      logger.error('Error retrieving cart analytics', error);
      throw new Error('Failed to retrieve cart analytics');
    }
  }

  static async getFeaturedProductsAnalytics() {
    try {
      // Featured products
      const featuredProducts = await prisma.featuredProduct.findMany({
        include: {
          product: {
            select: {
              id: true,
              name: true,
              price: true,
              image: true,
              sales: true,
            },
          },
        },
        orderBy: {
          position: 'asc',
        },
      });

      // Top selling products
      const topSellingProducts = await prisma.product.findMany({
        orderBy: {
          sales: 'desc',
        },
        take: 20,
        select: {
          id: true,
          name: true,
          price: true,
          sales: true,
          revenue: true,
          image: true,
        },
      });

      // Scheduled promotions
      const scheduledPromotions = await prisma.promotion.findMany({
        where: {
          startDate: {
            gte: new Date(),
          },
        },
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  price: true,
                },
              },
            },
          },
        },
        orderBy: {
          startDate: 'asc',
        },
      });

      return {
        featuredProducts,
        topSellingProducts,
        scheduledPromotions,
        performanceMetrics: {
          totalFeaturedProducts: featuredProducts.length,
          totalTopSellingProducts: topSellingProducts.length,
          totalScheduledPromotions: scheduledPromotions.length,
        },
      };
    } catch (error) {
      logger.error('Error retrieving featured products analytics', error);
      throw new Error('Failed to retrieve featured products analytics');
    }
  }

  // Helper methods
  private static getStartDate(dateRange: string): Date {
    switch (dateRange) {
      case '1d':
        return subDays(new Date(), 1);
      case '7d':
        return subDays(new Date(), 7);
      case '30d':
        return subDays(new Date(), 30);
      case '90d':
        return subDays(new Date(), 90);
      default:
        return subDays(new Date(), 7);
    }
  }

  private static formatVisitorTrends(data: any[]): any[] {
    return data.map(item => ({
      date: item.createdAt.toISOString().split('T')[0],
      visitors: item._count.userId,
    }));
  }

  private static formatBrowserStats(data: any[]): any[] {
    const total = data.reduce((sum, item) => sum + item._count.id, 0);
    return data.map(item => ({
      browser: item.browser,
      users: item._count.id,
      percentage: Math.round((item._count.id / total) * 100 * 100) / 100,
    }));
  }

  private static formatGeoStats(data: any[]): any[] {
    const total = data.reduce((sum, item) => sum + item._count.id, 0);
    return data.map(item => ({
      country: item.country,
      users: item._count.id,
      percentage: Math.round((item._count.id / total) * 100 * 100) / 100,
    }));
  }

  private static formatDeviceStats(data: any[]): any[] {
    const total = data.reduce((sum, item) => sum + item._count.id, 0);
    return data.map(item => ({
      device: item.deviceType,
      users: item._count.id,
      percentage: Math.round((item._count.id / total) * 100 * 100) / 100,
    }));
  }

  private static formatTopPages(data: any[]): any[] {
    return data.map(item => ({
      path: item.path,
      views: item._count.id,
      avgSessionDuration: Math.round(item._avg.sessionDuration || 0),
      bounceRate: Math.round((item._avg.bounceRate || 0) * 100) / 100,
    }));
  }
}
```

---

## 🗄️ Database Management

### Prisma Schema for Analytics

```prisma
// File: prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Activity Tracking
model UserSession {
  id           String   @id @default(cuid())
  userId       String?
  sessionId    String   @unique
  browser      String?
  deviceType   String?
  country      String?
  lastActivity DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("user_sessions")
}

model UserActivity {
  id        String   @id @default(cuid())
  userId    String?
  sessionId String
  action    String
  path      String?
  metadata  Json?
  createdAt DateTime @default(now())

  @@map("user_activities")
}

model PageView {
  id              String  @id @default(cuid())
  sessionId       String
  path            String
  sessionDuration Int?    // in seconds
  bounceRate      Float?  // 0-1
  createdAt       DateTime @default(now())

  @@map("page_views")
}

// Cart & Wishlist Models
model Cart {
  id        String     @id @default(cuid())
  userId    String
  status    CartStatus @default(ACTIVE)
  total     Decimal    @default(0)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  user  User       @relation(fields: [userId], references: [id])
  items CartItem[]

  @@map("carts")
}

model CartItem {
  id        String  @id @default(cuid())
  cartId    String
  productId String
  quantity  Int
  price     Decimal

  cart    Cart    @relation(fields: [cartId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@map("cart_items")
}

model Wishlist {
  id        String   @id @default(cuid())
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user  User           @relation(fields: [userId], references: [id])
  items WishlistItem[]

  @@map("wishlists")
}

model WishlistItem {
  id         String   @id @default(cuid())
  wishlistId String
  productId  String
  addedAt    DateTime @default(now())

  wishlist Wishlist @relation(fields: [wishlistId], references: [id])
  product  Product  @relation(fields: [productId], references: [id])

  @@map("wishlist_items")
}

// Featured Products Models
model FeaturedProduct {
  id        String   @id @default(cuid())
  productId String
  position  Int
  section   String   @default("featured") // featured, top-selling, hot-deals
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  product Product @relation(fields: [productId], references: [id])

  @@unique([productId, section])
  @@map("featured_products")
}

model Promotion {
  id          String   @id @default(cuid())
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  products PromotionProduct[]

  @@map("promotions")
}

model PromotionProduct {
  id          String @id @default(cuid())
  promotionId String
  productId   String

  promotion Promotion @relation(fields: [promotionId], references: [id])
  product   Product   @relation(fields: [productId], references: [id])

  @@unique([promotionId, productId])
  @@map("promotion_products")
}

// Core Models
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      UserRole @default(CUSTOMER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  carts     Cart[]
  wishlists Wishlist[]
  orders    Order[]

  @@map("users")
}

model Product {
  id          String  @id @default(cuid())
  name        String
  description String?
  price       Decimal
  image       String?
  sales       Int     @default(0)
  revenue     Decimal @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  cartItems         CartItem[]
  wishlistItems     WishlistItem[]
  featuredProducts  FeaturedProduct[]
  promotionProducts PromotionProduct[]
  orderItems        OrderItem[]

  @@map("products")
}

model Order {
  id        String      @id @default(cuid())
  userId    String
  status    OrderStatus @default(PENDING)
  total     Decimal
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal

  order   Order   @relation(fields: [orderId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Enums
enum UserRole {
  ADMIN
  SELLER
  CUSTOMER
}

enum CartStatus {
  ACTIVE
  ABANDONED
  CONVERTED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
}
```

### Database Migrations

```bash
# Create migration
npx prisma migrate dev --name add_analytics_tables

# Apply migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

### Database Seeding

```typescript
// File: prisma/seed.ts
import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create users
  const users = await Promise.all(
    Array.from({ length: 100 }, async () => {
      return prisma.user.create({
        data: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          role: faker.helpers.arrayElement(['CUSTOMER', 'SELLER']),
        },
      });
    })
  );

  // Create products
  const products = await Promise.all(
    Array.from({ length: 50 }, async () => {
      return prisma.product.create({
        data: {
          name: faker.commerce.productName(),
          description: faker.commerce.productDescription(),
          price: faker.number.float({ min: 10, max: 1000, precision: 0.01 }),
          image: faker.image.url(),
          sales: faker.number.int({ min: 0, max: 500 }),
          revenue: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
        },
      });
    })
  );

  // Create user sessions for analytics
  await Promise.all(
    Array.from({ length: 1000 }, async () => {
      return prisma.userSession.create({
        data: {
          userId: faker.helpers.arrayElement(users).id,
          sessionId: faker.string.uuid(),
          browser: faker.helpers.arrayElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
          deviceType: faker.helpers.arrayElement(['desktop', 'mobile', 'tablet']),
          country: faker.location.country(),
          lastActivity: faker.date.recent({ days: 30 }),
        },
      });
    })
  );

  // Create user activities
  await Promise.all(
    Array.from({ length: 5000 }, async () => {
      return prisma.userActivity.create({
        data: {
          userId: faker.helpers.arrayElement(users).id,
          sessionId: faker.string.uuid(),
          action: faker.helpers.arrayElement(['page_view', 'click', 'purchase', 'add_to_cart']),
          path: faker.helpers.arrayElement(['/products', '/cart', '/checkout', '/profile']),
          metadata: {
            timestamp: faker.date.recent({ days: 7 }).toISOString(),
            userAgent: faker.internet.userAgent(),
          },
        },
      });
    })
  );

  // Create carts
  await Promise.all(
    Array.from({ length: 200 }, async () => {
      const cart = await prisma.cart.create({
        data: {
          userId: faker.helpers.arrayElement(users).id,
          status: faker.helpers.arrayElement(['ACTIVE', 'ABANDONED', 'CONVERTED']),
          total: faker.number.float({ min: 20, max: 500, precision: 0.01 }),
        },
      });

      // Add items to cart
      await Promise.all(
        Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, async () => {
          const product = faker.helpers.arrayElement(products);
          return prisma.cartItem.create({
            data: {
              cartId: cart.id,
              productId: product.id,
              quantity: faker.number.int({ min: 1, max: 3 }),
              price: product.price,
            },
          });
        })
      );
    })
  );

  // Create featured products
  await Promise.all(
    Array.from({ length: 10 }, async (_, index) => {
      return prisma.featuredProduct.create({
        data: {
          productId: faker.helpers.arrayElement(products).id,
          position: index + 1,
          section: 'featured',
        },
      });
    })
  );

  console.log('✅ Database seeding completed!');
}

main()
  .catch(e => {
    console.error('❌ Database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

---

## 🔐 Authentication & Authorization

### Development Mode Authentication

```typescript
// File: src/middleware/auth.ts
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

export async function authMiddleware(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Development mode: hardcoded authentication
    if (process.env.NODE_ENV === 'development' && process.env.DEV_AUTH_ENABLED === 'true') {
      const authHeader = req.headers.authorization;

      if (authHeader === 'Bearer dev-token') {
        req.user = {
          id: 'dev-user-id',
          email: '<EMAIL>',
          role: 'ADMIN',
        };
        return next();
      }

      // Check for basic auth with demo credentials
      if (authHeader?.startsWith('Basic ')) {
        const credentials = Buffer.from(authHeader.split(' ')[1], 'base64').toString();
        const [username, password] = credentials.split(':');

        if (username === process.env.DEV_USERNAME && password === process.env.DEV_PASSWORD) {
          req.user = {
            id: 'dev-user-id',
            email: '<EMAIL>',
            role: 'ADMIN',
          };
          return next();
        }
      }
    }

    // Production mode: OneSSO/Keycloak authentication
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Authentication token required',
      });
    }

    // Verify JWT token with Keycloak
    const decoded = await verifyKeycloakToken(token);
    req.user = {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.realm_access?.roles?.includes('admin') ? 'ADMIN' : 'USER',
    };

    next();
  } catch (error) {
    logger.error('Authentication failed', error);
    res.status(401).json({
      success: false,
      error: 'Invalid authentication token',
    });
  }
}

async function verifyKeycloakToken(token: string) {
  // In production, verify token with Keycloak
  const response = await fetch(
    `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid_connect/userinfo`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error('Invalid token');
  }

  return response.json();
}

// Role-based authorization middleware
export function requireRole(role: string) {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
    }

    if (req.user.role !== role && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
      });
    }

    next();
  };
}
```

---

## 🔔 Real-time Features

### Supabase Realtime Implementation

```typescript
// File: src/services/realtime.ts
import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger';

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

export class RealtimeService {
  static async broadcastUserActivity(data: { userId: string; action: string; metadata?: any }) {
    try {
      await supabase.channel('user-activity').send({
        type: 'broadcast',
        event: 'activity',
        payload: {
          ...data,
          timestamp: new Date().toISOString(),
        },
      });

      logger.info('User activity broadcasted', data);
    } catch (error) {
      logger.error('Failed to broadcast user activity', error);
    }
  }

  static async broadcastCartUpdate(data: {
    cartId: string;
    userId: string;
    action: 'add' | 'remove' | 'update' | 'abandon';
    items?: any[];
  }) {
    try {
      await supabase.channel('cart-updates').send({
        type: 'broadcast',
        event: 'cart_update',
        payload: {
          ...data,
          timestamp: new Date().toISOString(),
        },
      });

      logger.info('Cart update broadcasted', data);
    } catch (error) {
      logger.error('Failed to broadcast cart update', error);
    }
  }

  static async subscribeToUserActivity(callback: (payload: any) => void) {
    return supabase
      .channel('user-activity')
      .on('broadcast', { event: 'activity' }, callback)
      .subscribe();
  }

  static async subscribeToCartUpdates(callback: (payload: any) => void) {
    return supabase
      .channel('cart-updates')
      .on('broadcast', { event: 'cart_update' }, callback)
      .subscribe();
  }
}
```

### RabbitMQ Alternative Implementation

```typescript
// File: src/services/rabbitmq.ts
import amqp, { Connection, Channel } from 'amqplib';
import { logger } from '../utils/logger';

export class RabbitMQService {
  private static instance: RabbitMQService;
  private connection: Connection | null = null;
  private channel: Channel | null = null;

  static getInstance(): RabbitMQService {
    if (!RabbitMQService.instance) {
      RabbitMQService.instance = new RabbitMQService();
    }
    return RabbitMQService.instance;
  }

  async connect(): Promise<void> {
    try {
      this.connection = await amqp.connect(process.env.RABBITMQ_URL!);
      this.channel = await this.connection.createChannel();

      // Declare exchanges and queues
      await this.channel.assertExchange('analytics', 'topic', { durable: true });
      await this.channel.assertQueue('user-activity', { durable: true });
      await this.channel.assertQueue('cart-updates', { durable: true });

      logger.info('Connected to RabbitMQ');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ', error);
      throw error;
    }
  }

  async publishUserActivity(data: any): Promise<void> {
    if (!this.channel) await this.connect();

    try {
      await this.channel!.publish(
        'analytics',
        'user.activity',
        Buffer.from(
          JSON.stringify({
            ...data,
            timestamp: new Date().toISOString(),
          })
        )
      );

      logger.info('User activity published to RabbitMQ', data);
    } catch (error) {
      logger.error('Failed to publish user activity', error);
    }
  }

  async publishCartUpdate(data: any): Promise<void> {
    if (!this.channel) await this.connect();

    try {
      await this.channel!.publish(
        'analytics',
        'cart.update',
        Buffer.from(
          JSON.stringify({
            ...data,
            timestamp: new Date().toISOString(),
          })
        )
      );

      logger.info('Cart update published to RabbitMQ', data);
    } catch (error) {
      logger.error('Failed to publish cart update', error);
    }
  }

  async subscribeToUserActivity(callback: (data: any) => void): Promise<void> {
    if (!this.channel) await this.connect();

    try {
      await this.channel!.bindQueue('user-activity', 'analytics', 'user.activity');

      this.channel!.consume('user-activity', msg => {
        if (msg) {
          const data = JSON.parse(msg.content.toString());
          callback(data);
          this.channel!.ack(msg);
        }
      });

      logger.info('Subscribed to user activity queue');
    } catch (error) {
      logger.error('Failed to subscribe to user activity', error);
    }
  }

  async close(): Promise<void> {
    try {
      await this.channel?.close();
      await this.connection?.close();
      logger.info('RabbitMQ connection closed');
    } catch (error) {
      logger.error('Error closing RabbitMQ connection', error);
    }
  }
}
```

---

## 🧪 Testing Strategy

### Test Structure

```
tests/
├── unit/                    # Unit tests
│   ├── services/           # Service layer tests
│   ├── controllers/        # Controller tests
│   └── utils/              # Utility function tests
├── integration/            # Integration tests
│   ├── api/               # API endpoint tests
│   ├── database/          # Database integration tests
│   └── realtime/          # Real-time feature tests
├── e2e/                   # End-to-end tests
│   ├── analytics/         # Analytics workflow tests
│   └── auth/              # Authentication flow tests
└── load/                  # Load testing
    ├── analytics/         # Analytics performance tests
    └── api/               # API load tests
```

### Unit Testing with Jest

```typescript
// File: tests/unit/services/analytics.test.ts
import { AnalyticsService } from '../../../src/services/analytics';
import { PrismaClient } from '@prisma/client';

// Mock Prisma
jest.mock('@prisma/client');
const mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>;

describe('AnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserActivity', () => {
    it('should return user activity data with correct structure', async () => {
      // Mock data
      const mockUserSessions = [
        { id: '1', browser: 'Chrome', deviceType: 'desktop', country: 'India' },
      ];

      mockPrisma.userSession.count.mockResolvedValue(100);
      mockPrisma.userActivity.groupBy.mockResolvedValue([]);
      mockPrisma.userSession.groupBy.mockResolvedValue(mockUserSessions);
      mockPrisma.pageView.groupBy.mockResolvedValue([]);

      const result = await AnalyticsService.getUserActivity({ dateRange: '7d' });

      expect(result).toHaveProperty('activeUsers');
      expect(result).toHaveProperty('visitorTrends');
      expect(result).toHaveProperty('browserDistribution');
      expect(result).toHaveProperty('geographicData');
      expect(result).toHaveProperty('deviceAnalytics');
      expect(result).toHaveProperty('topPages');

      expect(mockPrisma.userSession.count).toHaveBeenCalled();
    });

    it('should handle date range filtering correctly', async () => {
      await AnalyticsService.getUserActivity({ dateRange: '30d' });

      // Verify that the correct date range was used in queries
      expect(mockPrisma.userActivity.groupBy).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            createdAt: expect.objectContaining({
              gte: expect.any(Date),
              lte: expect.any(Date),
            }),
          }),
        })
      );
    });
  });

  describe('getCartAnalytics', () => {
    it('should calculate conversion rate correctly', async () => {
      const mockActiveCarts = [
        { id: '1', status: 'active', items: [] },
        { id: '2', status: 'active', items: [] },
      ];
      const mockAbandonedCarts = [{ id: '3', status: 'abandoned', items: [] }];

      mockPrisma.cart.findMany
        .mockResolvedValueOnce(mockActiveCarts)
        .mockResolvedValueOnce(mockAbandonedCarts);
      mockPrisma.wishlist.findMany.mockResolvedValue([]);

      const result = await AnalyticsService.getCartAnalytics();

      expect(result.metrics.conversionRate).toBe(66.67); // 2/3 * 100
      expect(result.metrics.totalActiveCarts).toBe(2);
      expect(result.metrics.totalAbandonedCarts).toBe(1);
    });
  });
});
```

### Integration Testing

```typescript
// File: tests/integration/api/analytics.test.ts
import request from 'supertest';
import app from '../../../src/app';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

describe('Analytics API Integration', () => {
  beforeAll(async () => {
    // Setup test database
    await prisma.$connect();
  });

  afterAll(async () => {
    // Cleanup test database
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clear test data
    await prisma.userActivity.deleteMany();
    await prisma.userSession.deleteMany();
  });

  describe('GET /api/analytics/user-activity', () => {
    it('should return user activity analytics', async () => {
      // Create test data
      await prisma.userSession.create({
        data: {
          sessionId: 'test-session',
          browser: 'Chrome',
          deviceType: 'desktop',
          country: 'India',
        },
      });

      const response = await request(app)
        .get('/api/analytics/user-activity')
        .set('Authorization', 'Bearer dev-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('activeUsers');
      expect(response.body.data).toHaveProperty('visitorTrends');
      expect(response.body.meta).toHaveProperty('generatedAt');
    });

    it('should handle date range parameter', async () => {
      const response = await request(app)
        .get('/api/analytics/user-activity?dateRange=30d')
        .set('Authorization', 'Bearer dev-token')
        .expect(200);

      expect(response.body.meta.dateRange).toBe('30d');
    });

    it('should require authentication', async () => {
      await request(app).get('/api/analytics/user-activity').expect(401);
    });
  });

  describe('GET /api/analytics/export', () => {
    it('should export data in CSV format', async () => {
      const response = await request(app)
        .get('/api/analytics/export?type=user-activity&format=csv')
        .set('Authorization', 'Bearer dev-token')
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
    });

    it('should export data in JSON format', async () => {
      const response = await request(app)
        .get('/api/analytics/export?type=user-activity&format=json')
        .set('Authorization', 'Bearer dev-token')
        .expect(200);

      expect(response.headers['content-type']).toContain('application/json');
    });
  });
});
```

### Load Testing with Artillery

```yaml
# File: tests/load/analytics-load-test.yml
config:
  target: 'http://localhost:8000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: 'Warm up'
    - duration: 120
      arrivalRate: 50
      name: 'Ramp up load'
    - duration: 300
      arrivalRate: 100
      name: 'Sustained load'
  defaults:
    headers:
      Authorization: 'Bearer dev-token'

scenarios:
  - name: 'Analytics API Load Test'
    weight: 100
    flow:
      - get:
          url: '/api/analytics/user-activity'
          capture:
            - json: '$.data.activeUsers'
              as: 'activeUsers'
      - think: 2
      - get:
          url: '/api/analytics/cart-analytics'
      - think: 1
      - get:
          url: '/api/analytics/featured-products'
      - think: 3
      - get:
          url: '/api/analytics/export?type=user-activity&format=json'
```

### Test Commands

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run with coverage
npm run test:coverage

# Run load tests
npm run test:load

# Run specific test file
npm test -- tests/unit/services/analytics.test.ts

# Watch mode for development
npm run test:watch
```

---

## ⚡ Performance Optimization

### Caching Strategy

```typescript
// File: src/services/cache.ts
import Redis from 'ioredis';
import { logger } from '../utils/logger';

const redis = new Redis(process.env.REDIS_URL!);

export class CacheService {
  private static TTL = {
    USER_ACTIVITY: 300, // 5 minutes
    CART_ANALYTICS: 600, // 10 minutes
    FEATURED_PRODUCTS: 1800, // 30 minutes
  };

  static async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.error('Cache get error', { key, error });
      return null;
    }
  }

  static async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      if (ttl) {
        await redis.setex(key, ttl, serialized);
      } else {
        await redis.set(key, serialized);
      }
    } catch (error) {
      logger.error('Cache set error', { key, error });
    }
  }

  static async invalidate(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      logger.error('Cache invalidation error', { pattern, error });
    }
  }

  // Analytics-specific cache methods
  static async getUserActivityCache(dateRange: string, filters: any) {
    const key = `analytics:user-activity:${dateRange}:${JSON.stringify(filters)}`;
    return this.get(key);
  }

  static async setUserActivityCache(dateRange: string, filters: any, data: any) {
    const key = `analytics:user-activity:${dateRange}:${JSON.stringify(filters)}`;
    await this.set(key, data, this.TTL.USER_ACTIVITY);
  }

  static async getCartAnalyticsCache() {
    return this.get('analytics:cart-analytics');
  }

  static async setCartAnalyticsCache(data: any) {
    await this.set('analytics:cart-analytics', data, this.TTL.CART_ANALYTICS);
  }
}
```

### Database Query Optimization

```typescript
// File: src/services/analytics-optimized.ts
import { PrismaClient } from '@prisma/client';
import { CacheService } from './cache';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export class OptimizedAnalyticsService {
  static async getUserActivity(options: { dateRange?: string; filters?: any }) {
    const { dateRange = '7d', filters = {} } = options;

    // Check cache first
    const cached = await CacheService.getUserActivityCache(dateRange, filters);
    if (cached) {
      logger.info('Returning cached user activity data');
      return cached;
    }

    const startDate = this.getStartDate(dateRange);
    const endDate = new Date();

    try {
      // Use Promise.all for parallel queries
      const [activeUsers, visitorTrends, browserStats, geoStats, deviceStats, topPages] =
        await Promise.all([
          // Active users with optimized query
          prisma.userSession.count({
            where: {
              lastActivity: { gte: subDays(new Date(), 1) },
            },
          }),

          // Visitor trends with date truncation
          prisma.$queryRaw`
          SELECT DATE(created_at) as date, COUNT(DISTINCT user_id) as visitors
          FROM user_activities
          WHERE created_at >= ${startDate} AND created_at <= ${endDate}
          GROUP BY DATE(created_at)
          ORDER BY date ASC
        `,

          // Browser stats with aggregation
          prisma.userSession.groupBy({
            by: ['browser'],
            _count: { id: true },
            where: {
              createdAt: { gte: startDate, lte: endDate },
            },
          }),

          // Geographic stats
          prisma.userSession.groupBy({
            by: ['country'],
            _count: { id: true },
            where: {
              createdAt: { gte: startDate, lte: endDate },
            },
            orderBy: { _count: { id: 'desc' } },
            take: 10,
          }),

          // Device stats
          prisma.userSession.groupBy({
            by: ['deviceType'],
            _count: { id: true },
            where: {
              createdAt: { gte: startDate, lte: endDate },
            },
          }),

          // Top pages with performance metrics
          prisma.pageView.groupBy({
            by: ['path'],
            _count: { id: true },
            _avg: { sessionDuration: true, bounceRate: true },
            where: {
              createdAt: { gte: startDate, lte: endDate },
            },
            orderBy: { _count: { id: 'desc' } },
            take: 10,
          }),
        ]);

      const result = {
        activeUsers,
        visitorTrends: this.formatVisitorTrends(visitorTrends),
        browserDistribution: this.formatBrowserStats(browserStats),
        geographicData: this.formatGeoStats(geoStats),
        deviceAnalytics: this.formatDeviceStats(deviceStats),
        topPages: this.formatTopPages(topPages),
      };

      // Cache the result
      await CacheService.setUserActivityCache(dateRange, filters, result);

      return result;
    } catch (error) {
      logger.error('Error retrieving optimized user activity analytics', error);
      throw new Error('Failed to retrieve user activity analytics');
    }
  }

  // Database connection pooling
  static async optimizeConnections() {
    // Configure Prisma connection pool
    await prisma.$connect();

    // Set up connection pool monitoring
    setInterval(async () => {
      const metrics = await prisma.$metrics.json();
      logger.info('Database connection metrics', metrics);
    }, 60000); // Log every minute
  }
}
```

### API Rate Limiting

```typescript
// File: src/middleware/rateLimit.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL!);

// General API rate limiting
export const generalRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // 1000 requests per hour
  message: {
    success: false,
    error: 'Too many requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Analytics-specific rate limiting
export const analyticsRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute for analytics
  message: {
    success: false,
    error: 'Analytics rate limit exceeded, please try again later',
    code: 'ANALYTICS_RATE_LIMIT_EXCEEDED',
  },
  skip: req => {
    // Skip rate limiting for development mode
    return process.env.NODE_ENV === 'development';
  },
});

// Export rate limiting (more restrictive)
export const exportRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 exports per minute
  message: {
    success: false,
    error: 'Export rate limit exceeded, please try again later',
    code: 'EXPORT_RATE_LIMIT_EXCEEDED',
  },
});
```

---

## 🚀 Deployment & DevOps

### Docker Configuration

```dockerfile
# File: Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S backend -u 1001

# Copy built application
COPY --from=builder --chown=backend:nodejs /app/dist ./dist
COPY --from=builder --chown=backend:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=backend:nodejs /app/package*.json ./
COPY --from=builder --chown=backend:nodejs /app/prisma ./prisma

USER backend

EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/healthcheck.js

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

### Docker Compose for Development

```yaml
# File: docker-compose.dev.yml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    ports:
      - '8000:8000'
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/ondc_seller_dev
      - REDIS_URL=redis://redis:6379
      - DEV_AUTH_ENABLED=true
      - DEV_USERNAME=demo
      - DEV_PASSWORD=demo
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    command: npm run dev

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ondc_seller_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
```

### Production Deployment Script

```bash
#!/bin/bash
# File: scripts/deploy.sh

set -e

echo "🚀 Starting deployment..."

# Build and tag Docker image
docker build -t ondc-seller-backend:latest .
docker tag ondc-seller-backend:latest ondc-seller-backend:$(git rev-parse --short HEAD)

# Run database migrations
echo "📊 Running database migrations..."
docker run --rm \
  -e DATABASE_URL="$PRODUCTION_DATABASE_URL" \
  ondc-seller-backend:latest \
  npx prisma migrate deploy

# Deploy to production
echo "🌐 Deploying to production..."
docker-compose -f docker-compose.prod.yml up -d

# Health check
echo "🏥 Performing health check..."
sleep 10
curl -f http://localhost:8000/health || exit 1

echo "✅ Deployment completed successfully!"
```

### Environment-specific Configurations

```typescript
// File: src/config/index.ts
export const config = {
  port: process.env.PORT || 8000,
  nodeEnv: process.env.NODE_ENV || 'development',

  database: {
    url: process.env.DATABASE_URL!,
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '5000'),
  },

  redis: {
    url: process.env.REDIS_URL!,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
  },

  auth: {
    keycloakUrl: process.env.KEYCLOAK_URL!,
    keycloakRealm: process.env.KEYCLOAK_REALM!,
    keycloakClientId: process.env.KEYCLOAK_CLIENT_ID!,
    keycloakClientSecret: process.env.KEYCLOAK_CLIENT_SECRET!,
    devAuthEnabled: process.env.DEV_AUTH_ENABLED === 'true',
    devUsername: process.env.DEV_USERNAME || 'demo',
    devPassword: process.env.DEV_PASSWORD || 'demo',
  },

  realtime: {
    supabaseUrl: process.env.SUPABASE_URL!,
    supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    rabbitmqUrl: process.env.RABBITMQ_URL,
  },

  monitoring: {
    enableConsoleMonitoring: process.env.ENABLE_CONSOLE_MONITORING === 'true',
    logLevel: process.env.LOG_LEVEL || 'info',
    enableMetrics: process.env.ENABLE_METRICS === 'true',
  },

  security: {
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    rateLimitEnabled: process.env.RATE_LIMIT_ENABLED !== 'false',
    helmetEnabled: process.env.HELMET_ENABLED !== 'false',
  },
};

// Validate required environment variables
export function validateConfig() {
  const required = ['DATABASE_URL', 'REDIS_URL', 'SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
```

---

## 📊 Monitoring & Logging

### Structured Logging

```typescript
// File: src/utils/logger.ts
import winston from 'winston';
import { config } from '../config';

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta,
      service: 'ondc-seller-backend',
      environment: config.nodeEnv,
    });
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: config.monitoring.logLevel,
  format: logFormat,
  defaultMeta: {
    service: 'ondc-seller-backend',
    version: process.env.npm_package_version || '1.0.0',
  },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }),

    // File transport for production
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
    }),
  ],
});

// Add HTTP request logging
export function logRequest(req: any, res: any, next: any) {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;

    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id,
    });
  });

  next();
}

// Analytics-specific logging
export const analyticsLogger = {
  userActivity: (data: any) => {
    logger.info('User activity tracked', {
      type: 'analytics',
      category: 'user-activity',
      ...data,
    });
  },

  cartEvent: (data: any) => {
    logger.info('Cart event tracked', {
      type: 'analytics',
      category: 'cart-event',
      ...data,
    });
  },

  featuredProductEvent: (data: any) => {
    logger.info('Featured product event tracked', {
      type: 'analytics',
      category: 'featured-product',
      ...data,
    });
  },
};
```

### Error Tracking and Monitoring

```typescript
// File: src/middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export function errorHandler(err: AppError, req: Request, res: Response, next: NextFunction) {
  // Set default error values
  err.statusCode = err.statusCode || 500;
  err.isOperational = err.isOperational || false;

  // Log error details
  logger.error('Application Error', {
    error: {
      message: err.message,
      stack: err.stack,
      statusCode: err.statusCode,
      isOperational: err.isOperational,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      userId: (req as any).user?.id,
    },
  });

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    res.status(err.statusCode).json({
      success: false,
      error: err.message,
      stack: err.stack,
      details: {
        statusCode: err.statusCode,
        isOperational: err.isOperational,
      },
    });
  } else {
    // Production error response
    if (err.isOperational) {
      res.status(err.statusCode).json({
        success: false,
        error: err.message,
        code: getErrorCode(err.statusCode),
      });
    } else {
      // Don't leak error details in production
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      });
    }
  }
}

function getErrorCode(statusCode: number): string {
  switch (statusCode) {
    case 400:
      return 'BAD_REQUEST';
    case 401:
      return 'UNAUTHORIZED';
    case 403:
      return 'FORBIDDEN';
    case 404:
      return 'NOT_FOUND';
    case 429:
      return 'RATE_LIMIT_EXCEEDED';
    case 500:
      return 'INTERNAL_ERROR';
    default:
      return 'UNKNOWN_ERROR';
  }
}

// Custom error classes
export class ValidationError extends Error {
  statusCode = 400;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends Error {
  statusCode = 401;
  isOperational = true;

  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  statusCode = 403;
  isOperational = true;

  constructor(message: string = 'Insufficient permissions') {
    super(message);
    this.name = 'AuthorizationError';
  }
}
```

### Performance Monitoring

```typescript
// File: src/middleware/metrics.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

interface PerformanceMetrics {
  requestCount: number;
  responseTime: number[];
  errorCount: number;
  activeConnections: number;
}

class MetricsCollector {
  private metrics: PerformanceMetrics = {
    requestCount: 0,
    responseTime: [],
    errorCount: 0,
    activeConnections: 0,
  };

  recordRequest(duration: number, statusCode: number) {
    this.metrics.requestCount++;
    this.metrics.responseTime.push(duration);

    if (statusCode >= 400) {
      this.metrics.errorCount++;
    }

    // Keep only last 1000 response times
    if (this.metrics.responseTime.length > 1000) {
      this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
    }
  }

  getMetrics() {
    const responseTimes = this.metrics.responseTime;
    const avgResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
        : 0;

    const p95ResponseTime =
      responseTimes.length > 0
        ? responseTimes.sort((a, b) => a - b)[Math.floor(responseTimes.length * 0.95)]
        : 0;

    return {
      ...this.metrics,
      avgResponseTime: Math.round(avgResponseTime),
      p95ResponseTime: Math.round(p95ResponseTime),
      errorRate:
        this.metrics.requestCount > 0
          ? (this.metrics.errorCount / this.metrics.requestCount) * 100
          : 0,
    };
  }

  reset() {
    this.metrics = {
      requestCount: 0,
      responseTime: [],
      errorCount: 0,
      activeConnections: 0,
    };
  }
}

const metricsCollector = new MetricsCollector();

export function metricsMiddleware(req: Request, res: Response, next: NextFunction) {
  const start = Date.now();

  metricsCollector.metrics.activeConnections++;

  res.on('finish', () => {
    const duration = Date.now() - start;
    metricsCollector.recordRequest(duration, res.statusCode);
    metricsCollector.metrics.activeConnections--;
  });

  next();
}

// Metrics endpoint
export function getMetricsHandler(req: Request, res: Response) {
  const metrics = metricsCollector.getMetrics();

  res.json({
    success: true,
    data: {
      ...metrics,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
    },
  });
}

// Log metrics periodically
setInterval(() => {
  const metrics = metricsCollector.getMetrics();

  logger.info('Performance Metrics', {
    type: 'metrics',
    ...metrics,
    memory: process.memoryUsage(),
    uptime: process.uptime(),
  });
}, 60000); // Log every minute
```

### Health Check Implementation

```typescript
// File: src/routes/health.ts
import express from 'express';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();
const redis = new Redis(process.env.REDIS_URL!);

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    database: HealthCheck;
    redis: HealthCheck;
    memory: HealthCheck;
    disk: HealthCheck;
  };
}

interface HealthCheck {
  status: 'pass' | 'fail' | 'warn';
  responseTime?: number;
  details?: any;
}

async function checkDatabase(): Promise<HealthCheck> {
  try {
    const start = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - start;

    return {
      status: responseTime < 1000 ? 'pass' : 'warn',
      responseTime,
      details: { connectionPool: 'active' },
    };
  } catch (error) {
    logger.error('Database health check failed', error);
    return {
      status: 'fail',
      details: { error: (error as Error).message },
    };
  }
}

async function checkRedis(): Promise<HealthCheck> {
  try {
    const start = Date.now();
    await redis.ping();
    const responseTime = Date.now() - start;

    return {
      status: responseTime < 500 ? 'pass' : 'warn',
      responseTime,
      details: { connection: 'active' },
    };
  } catch (error) {
    logger.error('Redis health check failed', error);
    return {
      status: 'fail',
      details: { error: (error as Error).message },
    };
  }
}

function checkMemory(): HealthCheck {
  const memUsage = process.memoryUsage();
  const totalMem = memUsage.heapTotal;
  const usedMem = memUsage.heapUsed;
  const memoryUsagePercent = (usedMem / totalMem) * 100;

  let status: 'pass' | 'warn' | 'fail' = 'pass';
  if (memoryUsagePercent > 90) status = 'fail';
  else if (memoryUsagePercent > 80) status = 'warn';

  return {
    status,
    details: {
      heapUsed: Math.round(usedMem / 1024 / 1024),
      heapTotal: Math.round(totalMem / 1024 / 1024),
      usagePercent: Math.round(memoryUsagePercent),
    },
  };
}

function checkDisk(): HealthCheck {
  // Simplified disk check - in production, use proper disk space monitoring
  return {
    status: 'pass',
    details: { available: 'sufficient' },
  };
}

router.get('/health', async (req, res) => {
  try {
    const [database, redis] = await Promise.all([checkDatabase(), checkRedis()]);

    const memory = checkMemory();
    const disk = checkDisk();

    const checks = { database, redis, memory, disk };
    const hasFailures = Object.values(checks).some(check => check.status === 'fail');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warn');

    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    if (hasFailures) overallStatus = 'unhealthy';
    else if (hasWarnings) overallStatus = 'degraded';

    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      checks,
    };

    const statusCode = overallStatus === 'unhealthy' ? 503 : 200;
    res.status(statusCode).json(healthStatus);
  } catch (error) {
    logger.error('Health check failed', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.status(200).json({ status: 'ready' });
  } catch (error) {
    res.status(503).json({ status: 'not ready' });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
  res.status(200).json({ status: 'alive' });
});

export default router;
```

### Console Monitoring for Development

```typescript
// File: src/utils/consoleMonitor.ts
import { logger } from './logger';

export function setupConsoleMonitoring() {
  if (process.env.NODE_ENV !== 'development') return;

  // Override console methods to capture logs
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
  };

  console.log = (...args) => {
    logger.info('Console Log', { args: args.map(String) });
    originalConsole.log.apply(console, args);
  };

  console.error = (...args) => {
    logger.error('Console Error', { args: args.map(String) });
    originalConsole.error.apply(console, args);
  };

  console.warn = (...args) => {
    logger.warn('Console Warning', { args: args.map(String) });
    originalConsole.warn.apply(console, args);
  };

  console.info = (...args) => {
    logger.info('Console Info', { args: args.map(String) });
    originalConsole.info.apply(console, args);
  };

  // Monitor unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Promise Rejection', {
      reason: String(reason),
      promise: String(promise),
    });
  });

  // Monitor uncaught exceptions
  process.on('uncaughtException', error => {
    logger.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  });

  logger.info('Console monitoring enabled for development mode');
}
```

### Change Tracking and Restore Points

```typescript
// File: src/utils/changeTracking.ts
import fs from 'fs/promises';
import path from 'path';
import { logger } from './logger';

interface RestorePoint {
  id: string;
  timestamp: string;
  description: string;
  version: string;
  gitCommit?: string;
  changes: string[];
}

interface ChangeLog {
  timestamp: string;
  type: 'create' | 'update' | 'delete';
  resource: string;
  resourceId: string;
  userId?: string;
  changes: any;
}

export class ChangeTracker {
  private static logDir = 'logs';
  private static restorePointsFile = path.join(this.logDir, 'restore-points.log');
  private static changesFile = path.join(this.logDir, 'changes.log');

  static async createRestorePoint(description: string, changes: string[] = []) {
    const restorePoint: RestorePoint = {
      id: `restore-${Date.now()}`,
      timestamp: new Date().toISOString(),
      description,
      version: process.env.npm_package_version || '1.0.0',
      gitCommit: process.env.GIT_COMMIT,
      changes,
    };

    try {
      await this.ensureLogDir();
      await fs.appendFile(this.restorePointsFile, JSON.stringify(restorePoint) + '\n');

      logger.info('Restore point created', restorePoint);
      return restorePoint.id;
    } catch (error) {
      logger.error('Failed to create restore point', error);
      throw error;
    }
  }

  static async logChange(
    type: 'create' | 'update' | 'delete',
    resource: string,
    resourceId: string,
    changes: any,
    userId?: string
  ) {
    const changeLog: ChangeLog = {
      timestamp: new Date().toISOString(),
      type,
      resource,
      resourceId,
      userId,
      changes,
    };

    try {
      await this.ensureLogDir();
      await fs.appendFile(this.changesFile, JSON.stringify(changeLog) + '\n');

      logger.info('Change logged', changeLog);
    } catch (error) {
      logger.error('Failed to log change', error);
    }
  }

  static async getRestorePoints(limit: number = 10): Promise<RestorePoint[]> {
    try {
      const content = await fs.readFile(this.restorePointsFile, 'utf-8');
      const lines = content.trim().split('\n').filter(Boolean);

      return lines
        .slice(-limit)
        .map(line => JSON.parse(line))
        .reverse();
    } catch (error) {
      logger.error('Failed to read restore points', error);
      return [];
    }
  }

  static async getChanges(
    since?: string,
    resource?: string,
    limit: number = 100
  ): Promise<ChangeLog[]> {
    try {
      const content = await fs.readFile(this.changesFile, 'utf-8');
      const lines = content.trim().split('\n').filter(Boolean);

      let changes = lines.map(line => JSON.parse(line)).reverse();

      if (since) {
        changes = changes.filter(change => change.timestamp >= since);
      }

      if (resource) {
        changes = changes.filter(change => change.resource === resource);
      }

      return changes.slice(0, limit);
    } catch (error) {
      logger.error('Failed to read changes', error);
      return [];
    }
  }

  private static async ensureLogDir() {
    try {
      await fs.access(this.logDir);
    } catch {
      await fs.mkdir(this.logDir, { recursive: true });
    }
  }
}

// Middleware to automatically log API changes
export function changeTrackingMiddleware(req: any, res: any, next: any) {
  const originalSend = res.send;

  res.send = function (data: any) {
    // Log changes for POST, PUT, PATCH, DELETE requests
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
      const resource = req.route?.path || req.path;
      const resourceId = req.params.id || 'unknown';

      let changeType: 'create' | 'update' | 'delete' = 'update';
      if (req.method === 'POST') changeType = 'create';
      if (req.method === 'DELETE') changeType = 'delete';

      ChangeTracker.logChange(
        changeType,
        resource,
        resourceId,
        {
          method: req.method,
          body: req.body,
          params: req.params,
          query: req.query,
        },
        req.user?.id
      );
    }

    return originalSend.call(this, data);
  };

  next();
}
```

---

## 🎯 Next Steps & Best Practices

### Development Workflow

1. **Setup Development Environment**

   ```bash
   # Clone and setup
   git clone <repository>
   cd ondc-seller/packages/backend
   npm install

   # Setup environment
   cp .env.example .env.development

   # Start services
   docker-compose -f docker-compose.dev.yml up -d

   # Run migrations
   npx prisma migrate dev

   # Start development server
   npm run dev
   ```

2. **Code Quality Standards**

   - Use TypeScript for all new code
   - Follow ESLint and Prettier configurations
   - Write unit tests for all services
   - Document all API endpoints
   - Use structured logging throughout

3. **Database Best Practices**

   - Always use migrations for schema changes
   - Index frequently queried columns
   - Use connection pooling in production
   - Implement proper error handling
   - Monitor query performance

4. **Security Guidelines**

   - Validate all input data
   - Use parameterized queries
   - Implement rate limiting
   - Log security events
   - Keep dependencies updated

5. **Performance Optimization**
   - Implement caching strategies
   - Use database indexes effectively
   - Monitor and optimize slow queries
   - Implement proper pagination
   - Use compression for responses

### Production Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] Monitoring and logging setup
- [ ] Health checks configured
- [ ] Backup strategy implemented
- [ ] Security scanning completed
- [ ] Load testing performed
- [ ] Documentation updated
- [ ] Team training completed

---

_📚 This comprehensive backend developer guide provides all the necessary information to develop, test, deploy, and maintain the ONDC Seller Platform backend. For specific questions or advanced configurations, refer to the individual service documentation or contact the development team._
