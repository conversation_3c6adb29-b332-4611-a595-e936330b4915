openapi: 3.0.3
info:
  title: ONDC Seller Enhanced Backend API
  description: Enhanced e-commerce backend API with MCP integration and comprehensive endpoints
  version: 1.0.0
  contact:
    name: ONDC Seller Platform
    email: <EMAIL>

servers:
  - url: http://localhost:9000
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server

security:
  - BearerAuth: []
  - TenantAuth: []

paths:
  # MCP Endpoints
  /mcp/tools:
    get:
      summary: List available MCP tools
      tags: [MCP]
      responses:
        '200':
          description: List of available tools
          content:
            application/json:
              schema:
                type: object
                properties:
                  tools:
                    type: array
                    items:
                      $ref: '#/components/schemas/MCPTool'
                  total_count:
                    type: integer
                  timestamp:
                    type: string
                    format: date-time

  /mcp/tools/execute:
    post:
      summary: Execute MCP tool
      tags: [MCP]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                tool:
                  type: string
                  example: "medusa_store_list_products"
                parameters:
                  type: object
                  example:
                    limit: 10
                    offset: 0
                    expand: "variants,images"
              required:
                - tool
      responses:
        '200':
          description: Tool execution result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  tool:
                    type: string
                  result:
                    type: object
                  timestamp:
                    type: string
                    format: date-time

  # Enhanced Product Endpoints
  /api/products:
    get:
      summary: List products with advanced filtering
      tags: [Products]
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
        - name: category_id
          in: query
          schema:
            type: string
        - name: q
          in: query
          schema:
            type: string
          description: Search query
        - name: price_min
          in: query
          schema:
            type: integer
        - name: price_max
          in: query
          schema:
            type: integer
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, published, rejected]
      responses:
        '200':
          description: List of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  total:
                    type: integer
                  has_more:
                    type: boolean

    post:
      summary: Create new product
      tags: [Products]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
                  message:
                    type: string

  /api/products/{id}:
    get:
      summary: Get product by ID
      tags: [Products]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: expand
          in: query
          schema:
            type: string
            default: "variants,images,categories"
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'

    put:
      summary: Update product
      tags: [Products]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductRequest'
      responses:
        '200':
          description: Product updated successfully

    delete:
      summary: Delete product
      tags: [Products]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Product deleted successfully

  # Enhanced Order Endpoints
  /api/orders:
    get:
      summary: List orders with advanced filtering
      tags: [Orders]
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
        - name: status
          in: query
          schema:
            type: string
        - name: customer_id
          in: query
          schema:
            type: string
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: List of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                  total:
                    type: integer

  /api/orders/{id}:
    get:
      summary: Get order by ID
      tags: [Orders]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order details
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'

  /api/orders/{id}/status:
    put:
      summary: Update order status
      tags: [Orders]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [pending, processing, shipped, delivered, cancelled]
                reason:
                  type: string
                notes:
                  type: string
              required:
                - status
      responses:
        '200':
          description: Order status updated successfully

  # Customer Endpoints
  /api/customers:
    get:
      summary: List customers
      tags: [Customers]
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
        - name: q
          in: query
          schema:
            type: string
          description: Search query
      responses:
        '200':
          description: List of customers
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'

    post:
      summary: Create new customer
      tags: [Customers]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully

  # Health Endpoints
  /api/health:
    get:
      summary: Basic health check
      tags: [Health]
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  uptime:
                    type: number
                  timestamp:
                    type: string
                    format: date-time

  /api/health/detailed:
    get:
      summary: Detailed health check
      tags: [Health]
      responses:
        '200':
          description: Detailed health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealth'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    TenantAuth:
      type: apiKey
      in: header
      name: x-tenant-id

  schemas:
    MCPTool:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        category:
          type: string
          enum: [store, admin, sync, utility]
        parameters:
          type: object

    Product:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [draft, published, rejected]
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
        images:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time

    ProductVariant:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'

    Price:
      type: object
      properties:
        amount:
          type: integer
        currency_code:
          type: string

    Order:
      type: object
      properties:
        id:
          type: string
        display_id:
          type: string
        status:
          type: string
        total:
          type: integer
        customer_id:
          type: string
        created_at:
          type: string
          format: date-time

    Customer:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        created_at:
          type: string
          format: date-time

    CreateProductRequest:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [draft, published]
        variants:
          type: array
          items:
            type: object
      required:
        - title

    UpdateProductRequest:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        status:
          type: string

    CreateCustomerRequest:
      type: object
      properties:
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        phone:
          type: string
      required:
        - email

    DetailedHealth:
      type: object
      properties:
        status:
          type: string
        system:
          type: object
        services:
          type: object
        database:
          type: object
        external_services:
          type: object

tags:
  - name: MCP
    description: Model Context Protocol endpoints
  - name: Products
    description: Enhanced product management
  - name: Orders
    description: Enhanced order management
  - name: Customers
    description: Customer management
  - name: Health
    description: Health and monitoring endpoints
