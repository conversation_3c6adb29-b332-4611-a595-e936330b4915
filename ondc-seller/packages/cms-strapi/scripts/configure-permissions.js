/**
 * Configure Strapi API Permissions Script
 * 
 * This script helps configure API permissions for public access to content types.
 * Since we can't programmatically set permissions without admin authentication,
 * this script provides instructions and verification.
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';

// Public API client
const publicAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Test API endpoints to check permissions
 */
async function testAPIPermissions() {
  console.log('🔍 Testing API permissions...');
  
  const endpoints = [
    '/product-categories',
    '/product-categories?populate=*',
    '/products',
    '/products?populate=*',
    '/products?filters[featured][$eq]=true',
    '/product-categories?filters[featured][$eq]=true'
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing: ${endpoint}`);
      const response = await publicAPI.get(endpoint);
      const status = response.status;
      const dataCount = response.data.data ? response.data.data.length : 0;
      
      results.push({
        endpoint,
        status,
        success: true,
        dataCount,
        message: `✅ Success - ${dataCount} items returned`
      });
      
      console.log(`  ✅ ${status} - ${dataCount} items returned`);
      
    } catch (error) {
      const status = error.response?.status || 'Unknown';
      const message = error.response?.data?.error?.message || error.message;
      
      results.push({
        endpoint,
        status,
        success: false,
        dataCount: 0,
        message: `❌ ${status} - ${message}`
      });
      
      console.log(`  ❌ ${status} - ${message}`);
    }
  }
  
  return results;
}

/**
 * Display permission configuration instructions
 */
function displayPermissionInstructions() {
  console.log('\n📋 MANUAL PERMISSION CONFIGURATION INSTRUCTIONS:');
  console.log('='.repeat(60));
  console.log('');
  console.log('1. Open Strapi Admin Panel:');
  console.log('   🌐 http://localhost:1339/admin');
  console.log('');
  console.log('2. Navigate to Settings → Users & Permissions Plugin → Roles');
  console.log('');
  console.log('3. Click on "Public" role');
  console.log('');
  console.log('4. In the Permissions section, find and configure:');
  console.log('');
  console.log('   📂 PRODUCT-CATEGORY:');
  console.log('   ✅ Check "find" (allows GET /api/product-categories)');
  console.log('   ✅ Check "findOne" (allows GET /api/product-categories/:id)');
  console.log('');
  console.log('   🛍️ PRODUCT:');
  console.log('   ✅ Check "find" (allows GET /api/products)');
  console.log('   ✅ Check "findOne" (allows GET /api/products/:id)');
  console.log('');
  console.log('5. Click "Save" to apply the permissions');
  console.log('');
  console.log('6. Test the API endpoints using this script:');
  console.log('   📝 node configure-permissions.js');
  console.log('');
  console.log('='.repeat(60));
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Strapi API Permissions Configuration Tool');
  console.log('');
  
  // Test current permissions
  const results = await testAPIPermissions();
  
  // Check if all endpoints are working
  const allWorking = results.every(result => result.success);
  
  if (allWorking) {
    console.log('\n🎉 All API endpoints are working correctly!');
    console.log('✅ Permissions are properly configured.');
    
    // Display summary
    console.log('\n📊 API Endpoints Summary:');
    results.forEach(result => {
      console.log(`  ${result.message}`);
    });
    
  } else {
    console.log('\n⚠️ Some API endpoints are not accessible.');
    console.log('❌ Permissions need to be configured manually.');
    
    // Display failed endpoints
    console.log('\n❌ Failed Endpoints:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`  ${result.endpoint}: ${result.message}`);
    });
    
    // Display working endpoints
    const workingEndpoints = results.filter(r => r.success);
    if (workingEndpoints.length > 0) {
      console.log('\n✅ Working Endpoints:');
      workingEndpoints.forEach(result => {
        console.log(`  ${result.endpoint}: ${result.message}`);
      });
    }
    
    // Show instructions
    displayPermissionInstructions();
  }
  
  return allWorking;
}

// Execute the script
if (require.main === module) {
  main().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Script execution failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testAPIPermissions,
  displayPermissionInstructions,
  main
};
