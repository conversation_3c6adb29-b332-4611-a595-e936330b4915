/**
 * <PERSON><PERSON><PERSON> to populate Strapi with page content
 * This script adds the rich content to Strapi database
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';

// Rich content for all pages
const pages = [
  {
    title: 'About Us',
    slug: 'about-us',
    content: `<h2>Welcome to ONDC Seller Platform</h2>
<p>We are a marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC).</p>

<h2>Our Mission</h2>
<p>Our mission is to democratize digital commerce in India by providing a platform that enables sellers of all sizes to reach customers across the country.</p>

<h2>What We Offer</h2>
<ul>
<li>Seamless integration with ONDC network</li>
<li>Comprehensive seller tools and analytics</li>
<li>Multi-channel inventory management</li>
<li>Secure payment processing</li>
<li>24/7 customer support</li>
</ul>

<h2>Our Vision</h2>
<p>To create an inclusive digital commerce ecosystem that empowers every seller in India to participate in the digital economy and reach customers nationwide.</p>`,
    excerpt: 'Learn about ONDC Seller Platform - connecting buyers and sellers across India through the Open Network for Digital Commerce.',
    metaTitle: 'About Us - ONDC Seller Platform',
    metaDescription: 'Discover how ONDC Seller Platform is democratizing digital commerce in India by connecting sellers with customers nationwide.',
    status: 'published',
    template: 'about',
    featured: false,
    author: 'Admin'
  },
  {
    title: 'Contact Us',
    slug: 'contact',
    content: `<h2>Get in Touch</h2>
<p>Have questions about the ONDC Seller Platform? We're here to help!</p>

<h3>Contact Information</h3>
<p><strong>Email:</strong> <EMAIL></p>
<p><strong>Phone:</strong> +91 1234567890</p>
<p><strong>Address:</strong><br>
123 Commerce Street<br>
Tech Park, Bangalore<br>
Karnataka, India</p>

<h3>Business Hours</h3>
<p>Monday - Friday: 9:00 AM - 6:00 PM IST<br>
Saturday: 10:00 AM - 2:00 PM IST<br>
Sunday: Closed</p>

<h3>Support</h3>
<p>For technical support, please use our contact form or email us directly. We typically respond within 24 hours during business days.</p>`,
    excerpt: 'Contact ONDC Seller Platform for support, questions, or business inquiries. We are here to help you succeed.',
    metaTitle: 'Contact Us - ONDC Seller Platform',
    metaDescription: 'Get in touch with ONDC Seller Platform support team. Find our contact information, business hours, and support options.',
    status: 'published',
    template: 'contact',
    featured: false,
    author: 'Admin'
  },
  {
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `<h2>Privacy Policy</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Information We Collect</h3>
<p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>

<h3>2. How We Use Your Information</h3>
<p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

<h3>3. Information Sharing</h3>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

<h3>4. Data Security</h3>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h3>5. Your Rights</h3>
<p>You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.</p>

<h3>6. Contact Us</h3>
<p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>`,
    excerpt: 'Our privacy policy explains how we collect, use, and protect your personal information on the ONDC Seller Platform.',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription: 'Read our privacy policy to understand how ONDC Seller Platform collects, uses, and protects your personal information.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin'
  }
];

async function updateExistingPage(pageData) {
  try {
    // First, find the page by slug
    const findResponse = await axios.get(
      `${STRAPI_URL}/api/pages?filters[slug][$eq]=${pageData.slug}`
    );

    if (findResponse.data.data.length > 0) {
      const pageId = findResponse.data.data[0].id;
      
      // Update the existing page
      const response = await axios.put(
        `${STRAPI_URL}/api/pages/${pageId}`,
        {
          data: pageData
        },
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );
      
      console.log(`✅ Updated page: ${pageData.title}`);
      return response.data;
    } else {
      // Create new page
      const response = await axios.post(
        `${STRAPI_URL}/api/pages`,
        {
          data: pageData
        },
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );
      
      console.log(`✅ Created page: ${pageData.title}`);
      return response.data;
    }
  } catch (error) {
    console.error(`❌ Error processing page ${pageData.title}:`, error.response?.data || error.message);
    return null;
  }
}

async function populatePages() {
  console.log('🚀 Starting page population...');
  
  for (const page of pages) {
    await updateExistingPage(page);
    // Add a small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('🎉 Page population completed!');
  
  // Verify the results
  try {
    const response = await axios.get(`${STRAPI_URL}/api/pages`);
    console.log(`📊 Total pages in Strapi: ${response.data.data.length}`);
    response.data.data.forEach(page => {
      console.log(`   - ${page.title} (${page.slug})`);
    });
  } catch (error) {
    console.error('Error fetching pages for verification:', error.message);
  }
}

// Run the population
populatePages().catch(console.error);
