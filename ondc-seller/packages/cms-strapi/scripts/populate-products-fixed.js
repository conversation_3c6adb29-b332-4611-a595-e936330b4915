/**
 * Fixed Product Population Script
 * 
 * This script populates products for existing categories in Strapi CMS
 * Fixed to match the actual product schema (no slug field)
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';

// Public API client
const publicAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper function to create rich text content
function createRichTextContent(text) {
  return [
    {
      type: 'paragraph',
      children: [
        {
          type: 'text',
          text: text
        }
      ]
    }
  ];
}

// Product templates for different categories
const productTemplates = {
  electronics: [
    { name: 'Premium Wireless Headphones', basePrice: 299.99, description: 'High-quality wireless headphones with noise cancellation' },
    { name: 'Smart Fitness Watch', basePrice: 199.99, description: 'Advanced fitness tracking with heart rate monitor' },
    { name: 'Professional Camera Lens', basePrice: 599.99, description: 'Professional-grade camera lens for photography enthusiasts' },
    { name: 'Gaming Mechanical Keyboard', basePrice: 149.99, description: 'RGB mechanical keyboard for gaming and productivity' },
    { name: 'Wireless Charging Pad', basePrice: 49.99, description: 'Fast wireless charging for compatible devices' },
    { name: '4K Webcam', basePrice: 129.99, description: 'Ultra HD webcam for streaming and video calls' },
    { name: 'Bluetooth Speaker', basePrice: 79.99, description: 'Portable Bluetooth speaker with premium sound quality' },
    { name: 'USB-C Hub', basePrice: 89.99, description: 'Multi-port USB-C hub with HDMI and USB 3.0' },
    { name: 'Wireless Mouse', basePrice: 59.99, description: 'Ergonomic wireless mouse with precision tracking' },
    { name: 'Phone Case with MagSafe', basePrice: 39.99, description: 'Protective case with magnetic wireless charging support' },
    { name: 'Tablet Stand', basePrice: 29.99, description: 'Adjustable stand for tablets and smartphones' },
    { name: 'Power Bank 20000mAh', basePrice: 69.99, description: 'High-capacity portable charger with fast charging' }
  ],
  fashion: [
    { name: 'Organic Cotton T-Shirt', basePrice: 29.99, description: 'Comfortable organic cotton t-shirt in various colors' },
    { name: 'Designer Denim Jeans', basePrice: 89.99, description: 'Premium denim jeans with modern fit' },
    { name: 'Casual Summer Dress', basePrice: 59.99, description: 'Light and breezy summer dress for casual occasions' },
    { name: 'Leather Crossbody Bag', basePrice: 79.99, description: 'Genuine leather crossbody bag with adjustable strap' },
    { name: 'Running Sneakers', basePrice: 119.99, description: 'Lightweight running shoes with cushioned sole' },
    { name: 'Wool Blend Sweater', basePrice: 69.99, description: 'Cozy wool blend sweater for cooler weather' },
    { name: 'Silk Scarf', basePrice: 49.99, description: 'Elegant silk scarf with artistic print' },
    { name: 'Leather Belt', basePrice: 39.99, description: 'Classic leather belt with metal buckle' },
    { name: 'Cotton Pajama Set', basePrice: 45.99, description: 'Comfortable cotton pajama set for better sleep' },
    { name: 'Denim Jacket', basePrice: 79.99, description: 'Classic denim jacket with vintage wash' },
    { name: 'Athletic Shorts', basePrice: 34.99, description: 'Moisture-wicking athletic shorts for workouts' },
    { name: 'Formal Dress Shirt', basePrice: 54.99, description: 'Crisp formal dress shirt for business occasions' }
  ],
  'home-garden': [
    { name: 'Premium Coffee Maker', basePrice: 199.99, description: 'Professional-grade coffee maker with programmable features' },
    { name: 'Ergonomic Office Chair', basePrice: 299.99, description: 'Comfortable office chair with lumbar support' },
    { name: 'LED Desk Lamp', basePrice: 49.99, description: 'Adjustable LED desk lamp with multiple brightness levels' },
    { name: 'Bamboo Cutting Board Set', basePrice: 34.99, description: 'Eco-friendly bamboo cutting boards in various sizes' },
    { name: 'Ceramic Dinnerware Set', basePrice: 89.99, description: 'Complete dinnerware set for 4 people' },
    { name: 'Memory Foam Pillow', basePrice: 59.99, description: 'Contoured memory foam pillow for better sleep' },
    { name: 'Indoor Plant Collection', basePrice: 79.99, description: 'Set of 3 low-maintenance indoor plants' },
    { name: 'Storage Ottoman', basePrice: 69.99, description: 'Multi-functional storage ottoman with cushioned top' },
    { name: 'Wall Art Canvas Set', basePrice: 89.99, description: 'Modern abstract wall art canvas set of 3' },
    { name: 'Essential Oil Diffuser', basePrice: 45.99, description: 'Ultrasonic essential oil diffuser with LED lights' },
    { name: 'Throw Blanket', basePrice: 39.99, description: 'Soft and cozy throw blanket for living room' },
    { name: 'Kitchen Scale', basePrice: 29.99, description: 'Digital kitchen scale with precise measurements' }
  ]
};

// Generate products for a category
function generateProductsForCategory(categorySlug, categoryName, count = 8) {
  const products = [];
  const templates = productTemplates[categorySlug] || [
    { name: `Premium ${categoryName} Item`, basePrice: 99.99, description: `High-quality ${categoryName.toLowerCase()} product` },
    { name: `Professional ${categoryName} Tool`, basePrice: 149.99, description: `Professional-grade ${categoryName.toLowerCase()} equipment` },
    { name: `Eco-Friendly ${categoryName} Product`, basePrice: 79.99, description: `Sustainable ${categoryName.toLowerCase()} option` },
    { name: `Luxury ${categoryName} Collection`, basePrice: 199.99, description: `Premium ${categoryName.toLowerCase()} collection item` }
  ];
  
  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length];
    const variation = Math.floor(i / templates.length) + 1;
    
    const productName = variation > 1 ? `${template.name} v${variation}` : template.name;
    
    const product = {
      name: productName,
      description: createRichTextContent(template.description),
      short_description: template.description,
      price: Math.round((template.basePrice + (Math.random() * 50 - 25)) * 100) / 100,
      sale_price: Math.random() > 0.7 ? Math.round(template.basePrice * 0.8 * 100) / 100 : null,
      sku: `${categorySlug.toUpperCase()}-${String(i + 1).padStart(3, '0')}`,
      inventory_quantity: Math.floor(Math.random() * 100) + 10,
      product_status: 'Published',
      featured: Math.random() > 0.8,
      tags: `${categoryName}, Premium, Quality`,
      weight: Math.round((Math.random() * 5 + 0.1) * 100) / 100
    };
    
    products.push(product);
  }
  
  return products;
}

// Main function to populate products
async function populateProducts() {
  console.log('🛍️ Starting product population...');
  
  try {
    // Get existing categories
    const categoriesResponse = await publicAPI.get('/product-categories');
    const categories = categoriesResponse.data.data;
    
    console.log(`📂 Found ${categories.length} categories`);
    
    let totalProducts = 0;
    
    // Map category names to slugs for product generation
    const categoryMappings = {
      'Electronics': 'electronics',
      'Fashion & Apparel': 'fashion',
      'Home & Garden': 'home-garden',
      'Health & Beauty': 'beauty-health',
      'Sports & Outdoors': 'sports-fitness',
      'Books & Media': 'books-media',
      'Automotive': 'automotive',
      'Food & Beverages': 'food-beverages'
    };
    
    // Filter to main categories only (not subcategories)
    const mainCategories = categories.filter(category => {
      const categoryName = category.name || category.attributes?.name;
      return categoryMappings[categoryName] !== undefined;
    });
    
    console.log(`📂 Found ${mainCategories.length} main categories to populate`);
    
    for (const category of mainCategories) {
      const categoryName = category.name || category.attributes?.name;
      const categorySlug = categoryMappings[categoryName];
      
      console.log(`📦 Creating products for category: ${categoryName}`);
      
      const products = generateProductsForCategory(categorySlug, categoryName, 10);
      
      for (const productData of products) {
        try {
          const product = {
            data: {
              ...productData,
              categories: [category.id]
            }
          };
          
          const response = await publicAPI.post('/products', product);
          totalProducts++;
          console.log(`  ✅ Created product: ${productData.name} (ID: ${response.data.data.id})`);
          
          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 200));
          
        } catch (error) {
          console.warn(`  ⚠️ Could not create product ${productData.name}:`, error.response?.data?.error?.message || error.message);
        }
      }
    }
    
    console.log(`✅ Products population completed. Created ${totalProducts} products.`);
    
  } catch (error) {
    console.error('❌ Error populating products:', error.message);
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  populateProducts().then(() => {
    console.log('🎉 Product population completed successfully!');
  }).catch(error => {
    console.error('❌ Product population failed:', error.message);
    process.exit(1);
  });
}

module.exports = { populateProducts };
