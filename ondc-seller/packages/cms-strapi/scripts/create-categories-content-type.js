/**
 * <PERSON><PERSON><PERSON> to create Categories content type in Strapi CMS
 * 
 * This script creates the Category content type with all required fields
 * for comprehensive e-commerce category management.
 */

const axios = require('axios');

const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1339';
const STRAPI_TOKEN = process.env.STRAPI_API_TOKEN;

// Category content type schema
const categoryContentType = {
  kind: 'collectionType',
  collectionName: 'categories',
  info: {
    singularName: 'category',
    pluralName: 'categories',
    displayName: 'Category',
    description: 'E-commerce product categories with hierarchical structure'
  },
  options: {
    draftAndPublish: true,
    comment: ''
  },
  pluginOptions: {},
  attributes: {
    name: {
      type: 'string',
      required: true,
      maxLength: 100,
      minLength: 2
    },
    slug: {
      type: 'uid',
      targetField: 'name',
      required: true
    },
    description: {
      type: 'richtext'
    },
    image: {
      type: 'media',
      multiple: false,
      required: false,
      allowedTypes: ['images']
    },
    parentCategory: {
      type: 'relation',
      relation: 'manyToOne',
      target: 'api::category.category',
      inversedBy: 'subcategories'
    },
    subcategories: {
      type: 'relation',
      relation: 'oneToMany',
      target: 'api::category.category',
      mappedBy: 'parentCategory'
    },
    isActive: {
      type: 'boolean',
      default: true,
      required: true
    },
    sortOrder: {
      type: 'integer',
      default: 0
    },
    metaTitle: {
      type: 'string',
      maxLength: 60
    },
    metaDescription: {
      type: 'text',
      maxLength: 160
    },
    featuredProducts: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::product.product',
      inversedBy: 'featuredInCategories'
    },
    productCount: {
      type: 'integer',
      default: 0
    },
    level: {
      type: 'integer',
      default: 0,
      min: 0,
      max: 3
    },
    path: {
      type: 'string',
      maxLength: 500
    }
  }
};

async function createCategoryContentType() {
  try {
    console.log('🚀 Creating Category content type in Strapi CMS...');

    const headers = {
      'Content-Type': 'application/json'
    };

    if (STRAPI_TOKEN) {
      headers['Authorization'] = `Bearer ${STRAPI_TOKEN}`;
    }

    // Create the content type
    const response = await axios.post(
      `${STRAPI_URL}/content-type-builder/content-types`,
      {
        data: {
          contentType: categoryContentType
        }
      },
      { headers }
    );

    console.log('✅ Category content type created successfully!');
    console.log('📊 Content type details:', {
      name: categoryContentType.info.displayName,
      singularName: categoryContentType.info.singularName,
      pluralName: categoryContentType.info.pluralName,
      fieldsCount: Object.keys(categoryContentType.attributes).length
    });

    // Set up API permissions for public access
    await setupCategoryPermissions();

    return response.data;
  } catch (error) {
    console.error('❌ Error creating Category content type:', error.response?.data || error.message);
    
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log('ℹ️ Category content type already exists, skipping creation...');
      await setupCategoryPermissions();
      return { success: true, message: 'Content type already exists' };
    }
    
    throw error;
  }
}

async function setupCategoryPermissions() {
  try {
    console.log('🔐 Setting up Category API permissions...');

    const headers = {
      'Content-Type': 'application/json'
    };

    if (STRAPI_TOKEN) {
      headers['Authorization'] = `Bearer ${STRAPI_TOKEN}`;
    }

    // Get current permissions
    const permissionsResponse = await axios.get(
      `${STRAPI_URL}/users-permissions/permissions`,
      { headers }
    );

    const permissions = permissionsResponse.data.permissions;

    // Set up public permissions for categories
    const publicPermissions = {
      ...permissions,
      'api::category': {
        controllers: {
          category: {
            find: {
              enabled: true,
              policy: ''
            },
            findOne: {
              enabled: true,
              policy: ''
            }
          }
        }
      }
    };

    // Update permissions
    await axios.put(
      `${STRAPI_URL}/users-permissions/permissions`,
      { permissions: publicPermissions },
      { headers }
    );

    console.log('✅ Category API permissions configured successfully!');
  } catch (error) {
    console.error('⚠️ Warning: Could not set up permissions automatically:', error.response?.data || error.message);
    console.log('ℹ️ Please manually enable public access for Category content type in Strapi admin');
  }
}

// Run the script
if (require.main === module) {
  createCategoryContentType()
    .then(() => {
      console.log('🎉 Category content type setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to create Category content type:', error);
      process.exit(1);
    });
}

module.exports = { createCategoryContentType, setupCategoryPermissions };
