/**
 * <PERSON><PERSON><PERSON> to create Products content type in Strapi CMS
 * 
 * This script creates the Product content type with all required fields
 * for comprehensive e-commerce product management.
 */

const axios = require('axios');

const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1339';
const STRAPI_TOKEN = process.env.STRAPI_API_TOKEN;

// Product content type schema
const productContentType = {
  kind: 'collectionType',
  collectionName: 'products',
  info: {
    singularName: 'product',
    pluralName: 'products',
    displayName: 'Product',
    description: 'E-commerce products with comprehensive details and relationships'
  },
  options: {
    draftAndPublish: true,
    comment: ''
  },
  pluginOptions: {},
  attributes: {
    name: {
      type: 'string',
      required: true,
      maxLength: 200,
      minLength: 2
    },
    slug: {
      type: 'uid',
      targetField: 'name',
      required: true
    },
    description: {
      type: 'richtext'
    },
    shortDescription: {
      type: 'text',
      maxLength: 300
    },
    price: {
      type: 'decimal',
      required: true,
      min: 0
    },
    comparePrice: {
      type: 'decimal',
      min: 0
    },
    sku: {
      type: 'string',
      unique: true,
      maxLength: 50
    },
    images: {
      type: 'media',
      multiple: true,
      required: false,
      allowedTypes: ['images']
    },
    categories: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::category.category',
      inversedBy: 'products'
    },
    isActive: {
      type: 'boolean',
      default: true,
      required: true
    },
    isFeatured: {
      type: 'boolean',
      default: false
    },
    stock: {
      type: 'integer',
      default: 0,
      min: 0
    },
    weight: {
      type: 'decimal',
      min: 0
    },
    dimensions: {
      type: 'json'
    },
    tags: {
      type: 'json'
    },
    metaTitle: {
      type: 'string',
      maxLength: 60
    },
    metaDescription: {
      type: 'text',
      maxLength: 160
    },
    featuredInCategories: {
      type: 'relation',
      relation: 'manyToMany',
      target: 'api::category.category',
      mappedBy: 'featuredProducts'
    },
    brand: {
      type: 'string',
      maxLength: 100
    },
    model: {
      type: 'string',
      maxLength: 100
    },
    color: {
      type: 'string',
      maxLength: 50
    },
    size: {
      type: 'string',
      maxLength: 50
    },
    material: {
      type: 'string',
      maxLength: 100
    },
    rating: {
      type: 'decimal',
      min: 0,
      max: 5,
      default: 0
    },
    reviewCount: {
      type: 'integer',
      default: 0,
      min: 0
    },
    salesCount: {
      type: 'integer',
      default: 0,
      min: 0
    },
    viewCount: {
      type: 'integer',
      default: 0,
      min: 0
    }
  }
};

async function createProductContentType() {
  try {
    console.log('🚀 Creating Product content type in Strapi CMS...');

    const headers = {
      'Content-Type': 'application/json'
    };

    if (STRAPI_TOKEN) {
      headers['Authorization'] = `Bearer ${STRAPI_TOKEN}`;
    }

    // Create the content type
    const response = await axios.post(
      `${STRAPI_URL}/content-type-builder/content-types`,
      {
        data: {
          contentType: productContentType
        }
      },
      { headers }
    );

    console.log('✅ Product content type created successfully!');
    console.log('📊 Content type details:', {
      name: productContentType.info.displayName,
      singularName: productContentType.info.singularName,
      pluralName: productContentType.info.pluralName,
      fieldsCount: Object.keys(productContentType.attributes).length
    });

    // Set up API permissions for public access
    await setupProductPermissions();

    return response.data;
  } catch (error) {
    console.error('❌ Error creating Product content type:', error.response?.data || error.message);
    
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log('ℹ️ Product content type already exists, skipping creation...');
      await setupProductPermissions();
      return { success: true, message: 'Content type already exists' };
    }
    
    throw error;
  }
}

async function setupProductPermissions() {
  try {
    console.log('🔐 Setting up Product API permissions...');

    const headers = {
      'Content-Type': 'application/json'
    };

    if (STRAPI_TOKEN) {
      headers['Authorization'] = `Bearer ${STRAPI_TOKEN}`;
    }

    // Get current permissions
    const permissionsResponse = await axios.get(
      `${STRAPI_URL}/users-permissions/permissions`,
      { headers }
    );

    const permissions = permissionsResponse.data.permissions;

    // Set up public permissions for products
    const publicPermissions = {
      ...permissions,
      'api::product': {
        controllers: {
          product: {
            find: {
              enabled: true,
              policy: ''
            },
            findOne: {
              enabled: true,
              policy: ''
            }
          }
        }
      }
    };

    // Update permissions
    await axios.put(
      `${STRAPI_URL}/users-permissions/permissions`,
      { permissions: publicPermissions },
      { headers }
    );

    console.log('✅ Product API permissions configured successfully!');
  } catch (error) {
    console.error('⚠️ Warning: Could not set up permissions automatically:', error.response?.data || error.message);
    console.log('ℹ️ Please manually enable public access for Product content type in Strapi admin');
  }
}

// Run the script
if (require.main === module) {
  createProductContentType()
    .then(() => {
      console.log('🎉 Product content type setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to create Product content type:', error);
      process.exit(1);
    });
}

module.exports = { createProductContentType, setupProductPermissions };
