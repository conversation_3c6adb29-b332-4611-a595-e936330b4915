/**
 * Auto-populate Strapi CMS after permissions are configured
 * 
 * This script waits for permissions to be configured and then automatically
 * populates the CMS with comprehensive e-commerce data.
 */

const { testAPIPermissions } = require('./configure-permissions');
const { populateComprehensiveData } = require('./populate-comprehensive-data');

/**
 * Wait for permissions to be configured
 */
async function waitForPermissions(maxAttempts = 30, intervalSeconds = 10) {
  console.log('⏳ Waiting for API permissions to be configured...');
  console.log(`Will check every ${intervalSeconds} seconds for up to ${maxAttempts} attempts.`);
  console.log('');
  console.log('👉 Please configure permissions in Strapi admin panel:');
  console.log('   🌐 http://localhost:1339/admin');
  console.log('   📋 Follow the guide in PERMISSION_SETUP_GUIDE.md');
  console.log('');
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log(`🔍 Attempt ${attempt}/${maxAttempts}: Testing API permissions...`);
    
    try {
      const results = await testAPIPermissions();
      const allWorking = results.every(result => result.success);
      
      if (allWorking) {
        console.log('✅ All API permissions are configured correctly!');
        console.log('🚀 Proceeding with data population...');
        return true;
      } else {
        const failedCount = results.filter(r => !r.success).length;
        console.log(`❌ ${failedCount} endpoints still failing. Waiting ${intervalSeconds} seconds...`);
      }
      
    } catch (error) {
      console.log(`❌ Permission test failed: ${error.message}`);
    }
    
    if (attempt < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, intervalSeconds * 1000));
    }
  }
  
  console.log('⏰ Timeout reached. Permissions were not configured in time.');
  return false;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🎯 Strapi CMS Auto-Population Tool');
  console.log('='.repeat(50));
  console.log('');
  
  try {
    // Step 1: Wait for permissions
    const permissionsReady = await waitForPermissions();
    
    if (!permissionsReady) {
      console.log('❌ Could not proceed without proper API permissions.');
      console.log('📋 Please manually configure permissions and run:');
      console.log('   node scripts/populate-comprehensive-data.js');
      process.exit(1);
    }
    
    // Step 2: Populate data
    console.log('');
    console.log('🚀 Starting comprehensive data population...');
    await populateComprehensiveData();
    
    // Step 3: Final verification
    console.log('');
    console.log('🔍 Running final verification...');
    const finalResults = await testAPIPermissions();
    const workingEndpoints = finalResults.filter(r => r.success);
    
    console.log('');
    console.log('🎉 AUTO-POPULATION COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(50));
    console.log('');
    console.log('📊 Final Status:');
    workingEndpoints.forEach(result => {
      console.log(`  ✅ ${result.endpoint}: ${result.dataCount} items`);
    });
    
    console.log('');
    console.log('🌐 Test your frontend at: http://localhost:3000');
    console.log('🔧 Strapi admin panel: http://localhost:1339/admin');
    
  } catch (error) {
    console.error('❌ Auto-population failed:', error.message);
    console.log('');
    console.log('🔧 Manual steps:');
    console.log('1. Check Strapi server is running');
    console.log('2. Configure permissions manually');
    console.log('3. Run: node scripts/populate-comprehensive-data.js');
    process.exit(1);
  }
}

// Execute the script
if (require.main === module) {
  main();
}

module.exports = {
  waitForPermissions,
  main
};
