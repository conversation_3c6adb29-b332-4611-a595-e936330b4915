/**
 * <PERSON><PERSON>t to populate Strapi CMS with comprehensive e-commerce products
 * 
 * This script creates realistic products for each category with proper pricing,
 * descriptions, and metadata for the ONDC Seller Platform
 */

const axios = require('axios');

const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Comprehensive product catalog
const products = [
  // Electronics - Smartphones
  {
    name: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system. Features 6.7-inch Super Retina XDR display with ProMotion technology.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Latest iPhone with titanium design and A17 Pro chip',
    price: 134900,
    sale_price: 129900,
    sku: 'APPLE-IP15PM-256',
    inventory_quantity: 50,
    product_status: 'Published',
    featured: true,
    tags: 'iPhone, Apple, Smartphone, Premium, 5G',
    weight: 0.221,
    categorySlug: 'smartphones'
  },
  {
    name: 'Samsung Galaxy S24 Ultra',
    slug: 'samsung-galaxy-s24-ultra',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Premium Android smartphone with S Pen, 200MP camera, and AI-powered features. 6.8-inch Dynamic AMOLED display with 120Hz refresh rate.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Premium Android with S Pen and 200MP camera',
    price: 124999,
    sale_price: 119999,
    sku: 'SAMSUNG-GS24U-256',
    inventory_quantity: 35,
    product_status: 'Published',
    featured: true,
    tags: 'Samsung, Galaxy, Android, S Pen, Camera',
    weight: 0.232,
    categorySlug: 'smartphones'
  },
  {
    name: 'Google Pixel 8 Pro',
    slug: 'google-pixel-8-pro',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Google\'s flagship smartphone with advanced AI photography, Tensor G3 chip, and pure Android experience. 6.7-inch LTPO OLED display.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Google flagship with AI photography and Tensor G3',
    price: 106999,
    sale_price: 99999,
    sku: 'GOOGLE-P8P-128',
    inventory_quantity: 25,
    product_status: 'Published',
    featured: false,
    tags: 'Google, Pixel, Android, AI, Photography',
    weight: 0.213,
    categorySlug: 'smartphones'
  },

  // Electronics - Laptops
  {
    name: 'MacBook Pro 16-inch M3 Max',
    slug: 'macbook-pro-16-m3-max',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Professional laptop with M3 Max chip, 16-inch Liquid Retina XDR display, and up to 22 hours of battery life. Perfect for creative professionals.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Professional laptop with M3 Max chip',
    price: 399900,
    sale_price: 389900,
    sku: 'APPLE-MBP16-M3MAX',
    inventory_quantity: 15,
    product_status: 'Published',
    featured: true,
    tags: 'MacBook, Apple, Laptop, M3, Professional',
    weight: 2.16,
    categorySlug: 'laptops-computers'
  },
  {
    name: 'Dell XPS 13 Plus',
    slug: 'dell-xps-13-plus',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Ultra-portable laptop with 13.4-inch InfinityEdge display, Intel Core i7 processor, and premium build quality. Perfect for productivity.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Ultra-portable laptop with InfinityEdge display',
    price: 149999,
    sale_price: 139999,
    sku: 'DELL-XPS13P-I7',
    inventory_quantity: 20,
    product_status: 'Published',
    featured: false,
    tags: 'Dell, XPS, Laptop, Intel, Ultrabook',
    weight: 1.26,
    categorySlug: 'laptops-computers'
  },

  // Fashion - Men's Clothing
  {
    name: 'Premium Cotton Polo Shirt',
    slug: 'premium-cotton-polo-shirt',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Classic polo shirt made from 100% premium cotton. Comfortable fit with ribbed collar and cuffs. Available in multiple colors.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Classic polo shirt in premium cotton',
    price: 2999,
    sale_price: 2499,
    sku: 'FASHION-POLO-M-L',
    inventory_quantity: 100,
    product_status: 'Published',
    featured: false,
    tags: 'Polo, Cotton, Casual, Men, Shirt',
    weight: 0.3,
    categorySlug: 'mens-clothing'
  },
  {
    name: 'Slim Fit Denim Jeans',
    slug: 'slim-fit-denim-jeans',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Modern slim-fit jeans in premium denim fabric. Comfortable stretch material with classic five-pocket design.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Modern slim-fit jeans in premium denim',
    price: 4999,
    sale_price: 3999,
    sku: 'FASHION-JEANS-M-32',
    inventory_quantity: 75,
    product_status: 'Published',
    featured: true,
    tags: 'Jeans, Denim, Slim Fit, Men, Casual',
    weight: 0.6,
    categorySlug: 'mens-clothing'
  },

  // Home & Garden
  {
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Professional ergonomic office chair with lumbar support, adjustable height, and breathable mesh back. Perfect for long work sessions.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Professional ergonomic chair with lumbar support',
    price: 24999,
    sale_price: 19999,
    sku: 'FURNITURE-CHAIR-ERG-BK',
    inventory_quantity: 30,
    product_status: 'Published',
    featured: true,
    tags: 'Office Chair, Ergonomic, Furniture, Work',
    weight: 18.5,
    categorySlug: 'home-garden'
  },
  {
    name: 'Smart LED Table Lamp',
    slug: 'smart-led-table-lamp',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'WiFi-enabled smart table lamp with adjustable brightness and color temperature. Control via smartphone app or voice commands.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'WiFi-enabled smart lamp with app control',
    price: 5999,
    sale_price: 4999,
    sku: 'LIGHTING-LAMP-SMART-WH',
    inventory_quantity: 60,
    product_status: 'Published',
    featured: false,
    tags: 'Smart Home, LED, Lamp, WiFi, Lighting',
    weight: 1.2,
    categorySlug: 'home-garden'
  },

  // Health & Beauty
  {
    name: 'Vitamin C Serum',
    slug: 'vitamin-c-serum',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Anti-aging vitamin C serum with hyaluronic acid. Brightens skin, reduces fine lines, and provides antioxidant protection.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Anti-aging serum with vitamin C and hyaluronic acid',
    price: 2499,
    sale_price: 1999,
    sku: 'BEAUTY-SERUM-VITC-30ML',
    inventory_quantity: 80,
    product_status: 'Published',
    featured: true,
    tags: 'Skincare, Vitamin C, Anti-aging, Serum',
    weight: 0.1,
    categorySlug: 'health-beauty'
  },

  // Sports & Outdoors
  {
    name: 'Yoga Mat Premium',
    slug: 'yoga-mat-premium',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Premium non-slip yoga mat made from eco-friendly TPE material. 6mm thickness for optimal comfort and stability.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Premium non-slip yoga mat in eco-friendly material',
    price: 3999,
    sale_price: 2999,
    sku: 'SPORTS-YOGA-MAT-6MM',
    inventory_quantity: 45,
    product_status: 'Published',
    featured: false,
    tags: 'Yoga, Fitness, Mat, Exercise, Eco-friendly',
    weight: 1.8,
    categorySlug: 'sports-outdoors'
  },

  // Food & Beverages
  {
    name: 'Organic Green Tea',
    slug: 'organic-green-tea',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Premium organic green tea leaves sourced from high-altitude gardens. Rich in antioxidants with a delicate, refreshing flavor.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Premium organic green tea with antioxidants',
    price: 899,
    sale_price: 749,
    sku: 'FOOD-TEA-GREEN-ORG-100G',
    inventory_quantity: 120,
    product_status: 'Published',
    featured: true,
    tags: 'Organic, Green Tea, Antioxidants, Healthy',
    weight: 0.1,
    categorySlug: 'food-beverages'
  },
  {
    name: 'Artisan Coffee Beans',
    slug: 'artisan-coffee-beans',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Single-origin arabica coffee beans roasted to perfection. Medium roast with notes of chocolate and caramel.',
            type: 'text'
          }
        ]
      }
    ],
    short_description: 'Single-origin arabica beans with chocolate notes',
    price: 1299,
    sale_price: 1099,
    sku: 'FOOD-COFFEE-ARTISAN-250G',
    inventory_quantity: 90,
    product_status: 'Published',
    featured: false,
    tags: 'Coffee, Arabica, Single Origin, Artisan',
    weight: 0.25,
    categorySlug: 'food-beverages'
  }
];

async function createProduct(productData) {
  try {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_TOKEN}`
    };

    const response = await axios.post(
      `${STRAPI_URL}/api/products`,
      { data: productData },
      { headers }
    );

    console.log(`✅ Created product: ${productData.name}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log(`ℹ️ Product already exists: ${productData.name}`);
      return null;
    }
    console.error(`❌ Error creating product ${productData.name}:`, error.response?.data || error.message);
    throw error;
  }
}

async function populateProducts() {
  console.log('🚀 Starting product population...');
  console.log('=' .repeat(60));

  try {
    let createdCount = 0;
    let skippedCount = 0;

    console.log('\n📦 Creating products...');
    for (const product of products) {
      const result = await createProduct(product);
      if (result) {
        createdCount++;
      } else {
        skippedCount++;
      }
      // Wait a moment between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Product population completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ Total products processed: ${products.length}`);
    console.log(`✅ Products created: ${createdCount}`);
    console.log(`ℹ️ Products skipped (already exist): ${skippedCount}`);
    
    console.log('\n📊 Products by category:');
    const categoryCount = {};
    products.forEach(product => {
      categoryCount[product.categorySlug] = (categoryCount[product.categorySlug] || 0) + 1;
    });
    
    Object.entries(categoryCount).forEach(([category, count]) => {
      console.log(`• ${category}: ${count} products`);
    });

  } catch (error) {
    console.error('\n💥 Failed to populate products:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure Strapi server is running on http://localhost:1339');
    console.log('2. Check if API token is valid');
    console.log('3. Verify products content type exists');
    console.log('4. Check Strapi server logs for detailed errors');
    
    process.exit(1);
  }
}

// Run the population if this script is executed directly
if (require.main === module) {
  populateProducts();
}

module.exports = { populateProducts, products };
