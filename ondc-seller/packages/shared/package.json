{"name": "@ondc-seller/shared", "version": "1.0.0", "description": "Shared utilities and configurations for ONDC Seller Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:endpoints": "node dist/test-endpoints.js"}, "keywords": ["ondc", "seller", "api", "endpoints", "shared"], "author": "ONDC Seller Platform", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "dependencies": {}, "files": ["dist/**/*", "README.md"]}