/**
 * Shared package exports
 */

export * from './api-endpoints';
export * from './endpoint-tester';

// Re-export commonly used types and utilities
export { EndpointMapper } from './api-endpoints';
export { EndpointTester, runEndpointTests } from './endpoint-tester';

export default {
  API_ENDPOINTS: require('./api-endpoints').API_ENDPOINTS,
  EndpointMapper: require('./api-endpoints').EndpointMapper,
  EndpointTester: require('./endpoint-tester').EndpointTester,
  runEndpointTests: require('./endpoint-tester').runEndpointTests,
};
