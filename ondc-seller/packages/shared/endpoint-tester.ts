/**
 * Endpoint Testing Utility
 * Tests bidirectional endpoint mapping between backend and frontend
 */

import { API_ENDPOINTS, EndpointMapper } from './api-endpoints';

export interface TestResult {
  endpoint: string;
  method: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  responseTime?: number;
  statusCode?: number;
  data?: any;
}

export interface TestSuite {
  name: string;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}

export class EndpointTester {
  private baseURL: string;
  private publishableKey?: string;
  private adminToken?: string;

  constructor(config: {
    baseURL: string;
    publishableKey?: string;
    adminToken?: string;
  }) {
    this.baseURL = config.baseURL;
    this.publishableKey = config.publishableKey;
    this.adminToken = config.adminToken;
  }

  /**
   * Test a single endpoint
   */
  async testEndpoint(
    category: string,
    name: string,
    params: Record<string, any> = {},
    body?: Record<string, any>
  ): Promise<TestResult> {
    const config = EndpointMapper.getEndpoint(category, name);
    
    if (!config) {
      return {
        endpoint: `${category}.${name}`,
        method: 'UNKNOWN',
        status: 'error',
        message: 'Endpoint configuration not found'
      };
    }

    const startTime = Date.now();
    
    try {
      // Build URL
      const pathParams: Record<string, any> = {};
      const queryParams: Record<string, any> = {};

      Object.entries(params).forEach(([key, value]) => {
        if (config.path.includes(`:${key}`)) {
          pathParams[key] = value;
        } else {
          queryParams[key] = value;
        }
      });

      const url = EndpointMapper.buildUrl(config.path, pathParams);
      const queryString = EndpointMapper.buildQueryString(queryParams);
      const fullUrl = `${this.baseURL}${url}${queryString}`;

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      // Add authentication
      switch (config.auth) {
        case 'publishable':
          if (this.publishableKey) {
            headers['x-publishable-api-key'] = this.publishableKey;
          }
          break;
        case 'admin':
        case 'bearer':
          if (this.adminToken) {
            headers['Authorization'] = `Bearer ${this.adminToken}`;
          }
          break;
      }

      // Prepare request options
      const options: RequestInit = {
        method: config.method,
        headers,
      };

      if (body && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
        options.body = JSON.stringify(body);
      }

      // Make request
      const response = await fetch(fullUrl, options);
      const responseTime = Date.now() - startTime;

      let responseData: any;
      try {
        responseData = await response.json();
      } catch {
        responseData = await response.text();
      }

      if (response.ok) {
        return {
          endpoint: `${category}.${name}`,
          method: config.method,
          status: 'success',
          message: 'Endpoint responded successfully',
          responseTime,
          statusCode: response.status,
          data: responseData
        };
      } else {
        return {
          endpoint: `${category}.${name}`,
          method: config.method,
          status: 'error',
          message: `HTTP ${response.status}: ${responseData?.error || response.statusText}`,
          responseTime,
          statusCode: response.status,
          data: responseData
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        endpoint: `${category}.${name}`,
        method: config.method,
        status: 'error',
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime
      };
    }
  }

  /**
   * Test all endpoints in a category
   */
  async testCategory(category: string): Promise<TestSuite> {
    const endpoints = EndpointMapper.getCategoryEndpoints(category);
    const results: TestResult[] = [];

    for (const [name, config] of Object.entries(endpoints)) {
      // Prepare test parameters based on endpoint requirements
      const params = this.generateTestParams(config);
      const body = this.generateTestBody(config);

      const result = await this.testEndpoint(category, name, params, body);
      results.push(result);
    }

    const summary = {
      total: results.length,
      passed: results.filter(r => r.status === 'success').length,
      failed: results.filter(r => r.status === 'error').length,
      warnings: results.filter(r => r.status === 'warning').length,
    };

    return {
      name: category,
      results,
      summary
    };
  }

  /**
   * Test all endpoints
   */
  async testAllEndpoints(): Promise<TestSuite[]> {
    const categories = EndpointMapper.getCategories();
    const testSuites: TestSuite[] = [];

    for (const category of categories) {
      const suite = await this.testCategory(category);
      testSuites.push(suite);
    }

    return testSuites;
  }

  /**
   * Generate test parameters for an endpoint
   */
  private generateTestParams(config: any): Record<string, any> {
    const params: Record<string, any> = {};

    if (config.params) {
      Object.entries(config.params).forEach(([key, paramConfig]: [string, any]) => {
        if (paramConfig.required) {
          switch (paramConfig.type) {
            case 'string':
              if (key === 'id') {
                params[key] = 'test-id-123';
              } else {
                params[key] = 'test-value';
              }
              break;
            case 'number':
              params[key] = key === 'limit' ? 10 : key === 'offset' ? 0 : 1;
              break;
            case 'boolean':
              params[key] = true;
              break;
            case 'array':
              params[key] = ['test-item'];
              break;
          }
        }
      });
    }

    return params;
  }

  /**
   * Generate test body for an endpoint
   */
  private generateTestBody(config: any): Record<string, any> | undefined {
    if (!config.body || !['POST', 'PUT', 'PATCH'].includes(config.method)) {
      return undefined;
    }

    const body: Record<string, any> = {};

    Object.entries(config.body).forEach(([key, bodyConfig]: [string, any]) => {
      if (bodyConfig.required) {
        switch (bodyConfig.type) {
          case 'string':
            body[key] = key === 'title' ? 'Test Product' : 'test-value';
            break;
          case 'number':
            body[key] = 1;
            break;
          case 'boolean':
            body[key] = true;
            break;
          case 'object':
            body[key] = { test: 'value' };
            break;
          case 'array':
            body[key] = ['test-item'];
            break;
        }
      }
    });

    return Object.keys(body).length > 0 ? body : undefined;
  }

  /**
   * Generate test report
   */
  generateReport(testSuites: TestSuite[]): string {
    let report = '# API Endpoint Test Report\n\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;

    // Overall summary
    const totalTests = testSuites.reduce((sum, suite) => sum + suite.summary.total, 0);
    const totalPassed = testSuites.reduce((sum, suite) => sum + suite.summary.passed, 0);
    const totalFailed = testSuites.reduce((sum, suite) => sum + suite.summary.failed, 0);
    const totalWarnings = testSuites.reduce((sum, suite) => sum + suite.summary.warnings, 0);

    report += '## Overall Summary\n\n';
    report += `- **Total Tests**: ${totalTests}\n`;
    report += `- **Passed**: ${totalPassed} (${((totalPassed / totalTests) * 100).toFixed(1)}%)\n`;
    report += `- **Failed**: ${totalFailed} (${((totalFailed / totalTests) * 100).toFixed(1)}%)\n`;
    report += `- **Warnings**: ${totalWarnings} (${((totalWarnings / totalTests) * 100).toFixed(1)}%)\n\n`;

    // Category details
    testSuites.forEach(suite => {
      report += `## ${suite.name} Category\n\n`;
      report += `- **Total**: ${suite.summary.total}\n`;
      report += `- **Passed**: ${suite.summary.passed}\n`;
      report += `- **Failed**: ${suite.summary.failed}\n`;
      report += `- **Warnings**: ${suite.summary.warnings}\n\n`;

      report += '### Test Results\n\n';
      report += '| Endpoint | Method | Status | Message | Response Time |\n';
      report += '|----------|--------|--------|---------|---------------|\n';

      suite.results.forEach(result => {
        const status = result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️';
        const responseTime = result.responseTime ? `${result.responseTime}ms` : 'N/A';
        report += `| ${result.endpoint} | ${result.method} | ${status} | ${result.message} | ${responseTime} |\n`;
      });

      report += '\n';
    });

    return report;
  }
}

/**
 * CLI utility for testing endpoints
 */
export async function runEndpointTests(config: {
  baseURL: string;
  publishableKey?: string;
  adminToken?: string;
  outputFile?: string;
}) {
  console.log('🚀 Starting endpoint tests...\n');

  const tester = new EndpointTester(config);
  const testSuites = await tester.testAllEndpoints();

  // Print summary
  console.log('📊 Test Summary:');
  testSuites.forEach(suite => {
    const passRate = ((suite.summary.passed / suite.summary.total) * 100).toFixed(1);
    console.log(`  ${suite.name}: ${suite.summary.passed}/${suite.summary.total} passed (${passRate}%)`);
  });

  // Generate report
  const report = tester.generateReport(testSuites);
  
  if (config.outputFile) {
    const fs = await import('fs');
    fs.writeFileSync(config.outputFile, report);
    console.log(`\n📄 Report saved to: ${config.outputFile}`);
  } else {
    console.log('\n📄 Test Report:\n');
    console.log(report);
  }

  return testSuites;
}
