/**
 * Shared API Endpoint Configuration
 * This file defines the bidirectional mapping between backend and frontend endpoints
 * Used by both backend routes and frontend API clients
 */

export interface EndpointConfig {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  description: string;
  params?: Record<string, {
    type: 'string' | 'number' | 'boolean' | 'array';
    required?: boolean;
    description?: string;
  }>;
  body?: Record<string, {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    required?: boolean;
    description?: string;
  }>;
  response?: {
    type: 'object' | 'array';
    description?: string;
  };
  auth?: 'none' | 'publishable' | 'admin' | 'bearer';
  version?: string;
}

export interface APIEndpoints {
  [category: string]: {
    [endpointName: string]: EndpointConfig;
  };
}

/**
 * Complete API Endpoint Mapping
 * This configuration is used by both backend and frontend
 */
export const API_ENDPOINTS: APIEndpoints = {
  // Health & System
  system: {
    health: {
      path: '/health',
      method: 'GET',
      description: 'Health check endpoint',
      auth: 'none',
      response: {
        type: 'object',
        description: 'System health status'
      }
    },
    version: {
      path: '/version',
      method: 'GET',
      description: 'API version information',
      auth: 'none',
      response: {
        type: 'object',
        description: 'Version details'
      }
    }
  },

  // Products (Store API)
  products: {
    list: {
      path: '/store/products',
      method: 'GET',
      description: 'Get all products from store',
      auth: 'publishable',
      params: {
        limit: { type: 'number', description: 'Number of products to return' },
        offset: { type: 'number', description: 'Number of products to skip' },
        region_id: { type: 'string', description: 'Filter by region' },
        collection_id: { type: 'string', description: 'Filter by collection' },
        category_id: { type: 'string', description: 'Filter by category' },
        tags: { type: 'array', description: 'Filter by tags' },
        q: { type: 'string', description: 'Search query' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of products'
      }
    },
    get: {
      path: '/store/products/:id',
      method: 'GET',
      description: 'Get single product by ID',
      auth: 'publishable',
      params: {
        id: { type: 'string', required: true, description: 'Product ID' },
        region_id: { type: 'string', description: 'Region ID for pricing' }
      },
      response: {
        type: 'object',
        description: 'Product details'
      }
    },
    featured: {
      path: '/products/featured',
      method: 'GET',
      description: 'Get featured products',
      auth: 'none',
      params: {
        limit: { type: 'number', description: 'Number of products to return' }
      },
      response: {
        type: 'array',
        description: 'List of featured products'
      }
    }
  },

  // Products (Admin API)
  adminProducts: {
    list: {
      path: '/admin/products',
      method: 'GET',
      description: 'Get all products (admin)',
      auth: 'admin',
      params: {
        limit: { type: 'number', description: 'Number of products to return' },
        offset: { type: 'number', description: 'Number of products to skip' },
        q: { type: 'string', description: 'Search query' },
        status: { type: 'array', description: 'Filter by status' },
        collection_id: { type: 'array', description: 'Filter by collection IDs' },
        tags: { type: 'array', description: 'Filter by tags' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of products'
      }
    },
    get: {
      path: '/admin/products/:id',
      method: 'GET',
      description: 'Get single product (admin)',
      auth: 'admin',
      params: {
        id: { type: 'string', required: true, description: 'Product ID' }
      },
      response: {
        type: 'object',
        description: 'Product details'
      }
    },
    create: {
      path: '/admin/products',
      method: 'POST',
      description: 'Create new product',
      auth: 'admin',
      body: {
        title: { type: 'string', required: true, description: 'Product title' },
        description: { type: 'string', description: 'Product description' },
        handle: { type: 'string', description: 'Product handle (URL slug)' },
        status: { type: 'string', description: 'Product status' },
        thumbnail: { type: 'string', description: 'Thumbnail image URL' },
        images: { type: 'array', description: 'Product images' },
        collection_id: { type: 'string', description: 'Collection ID' },
        tags: { type: 'array', description: 'Product tags' }
      },
      response: {
        type: 'object',
        description: 'Created product'
      }
    },
    update: {
      path: '/admin/products/:id',
      method: 'POST',
      description: 'Update product',
      auth: 'admin',
      params: {
        id: { type: 'string', required: true, description: 'Product ID' }
      },
      body: {
        title: { type: 'string', description: 'Product title' },
        description: { type: 'string', description: 'Product description' },
        handle: { type: 'string', description: 'Product handle (URL slug)' },
        status: { type: 'string', description: 'Product status' },
        thumbnail: { type: 'string', description: 'Thumbnail image URL' },
        images: { type: 'array', description: 'Product images' },
        collection_id: { type: 'string', description: 'Collection ID' },
        tags: { type: 'array', description: 'Product tags' }
      },
      response: {
        type: 'object',
        description: 'Updated product'
      }
    },
    delete: {
      path: '/admin/products/:id',
      method: 'DELETE',
      description: 'Delete product',
      auth: 'admin',
      params: {
        id: { type: 'string', required: true, description: 'Product ID' }
      },
      response: {
        type: 'object',
        description: 'Deletion confirmation'
      }
    }
  },

  // Orders (Store API)
  orders: {
    list: {
      path: '/store/orders',
      method: 'GET',
      description: 'Get customer orders',
      auth: 'bearer',
      params: {
        limit: { type: 'number', description: 'Number of orders to return' },
        offset: { type: 'number', description: 'Number of orders to skip' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of orders'
      }
    },
    get: {
      path: '/store/orders/:id',
      method: 'GET',
      description: 'Get single order',
      auth: 'bearer',
      params: {
        id: { type: 'string', required: true, description: 'Order ID' }
      },
      response: {
        type: 'object',
        description: 'Order details'
      }
    }
  },

  // Orders (Admin API)
  adminOrders: {
    list: {
      path: '/admin/orders',
      method: 'GET',
      description: 'Get all orders (admin)',
      auth: 'admin',
      params: {
        limit: { type: 'number', description: 'Number of orders to return' },
        offset: { type: 'number', description: 'Number of orders to skip' },
        status: { type: 'array', description: 'Filter by status' },
        fulfillment_status: { type: 'array', description: 'Filter by fulfillment status' },
        payment_status: { type: 'array', description: 'Filter by payment status' },
        q: { type: 'string', description: 'Search query' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of orders'
      }
    },
    get: {
      path: '/admin/orders/:id',
      method: 'GET',
      description: 'Get single order (admin)',
      auth: 'admin',
      params: {
        id: { type: 'string', required: true, description: 'Order ID' }
      },
      response: {
        type: 'object',
        description: 'Order details'
      }
    }
  },

  // Customers (Admin API)
  adminCustomers: {
    list: {
      path: '/admin/customers',
      method: 'GET',
      description: 'Get all customers (admin)',
      auth: 'admin',
      params: {
        limit: { type: 'number', description: 'Number of customers to return' },
        offset: { type: 'number', description: 'Number of customers to skip' },
        q: { type: 'string', description: 'Search query' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of customers'
      }
    },
    get: {
      path: '/admin/customers/:id',
      method: 'GET',
      description: 'Get single customer (admin)',
      auth: 'admin',
      params: {
        id: { type: 'string', required: true, description: 'Customer ID' }
      },
      response: {
        type: 'object',
        description: 'Customer details'
      }
    }
  },

  // Collections
  collections: {
    list: {
      path: '/store/collections',
      method: 'GET',
      description: 'Get all collections',
      auth: 'publishable',
      params: {
        limit: { type: 'number', description: 'Number of collections to return' },
        offset: { type: 'number', description: 'Number of collections to skip' }
      },
      response: {
        type: 'object',
        description: 'Paginated list of collections'
      }
    },
    get: {
      path: '/store/collections/:id',
      method: 'GET',
      description: 'Get single collection',
      auth: 'publishable',
      params: {
        id: { type: 'string', required: true, description: 'Collection ID' }
      },
      response: {
        type: 'object',
        description: 'Collection details'
      }
    }
  },

  // Regions
  regions: {
    list: {
      path: '/store/regions',
      method: 'GET',
      description: 'Get all regions',
      auth: 'none',
      response: {
        type: 'object',
        description: 'List of regions'
      }
    }
  },

  // Cart
  cart: {
    create: {
      path: '/store/carts',
      method: 'POST',
      description: 'Create new cart',
      auth: 'publishable',
      body: {
        region_id: { type: 'string', description: 'Region ID' }
      },
      response: {
        type: 'object',
        description: 'Created cart'
      }
    },
    get: {
      path: '/store/carts/:id',
      method: 'GET',
      description: 'Get cart by ID',
      auth: 'publishable',
      params: {
        id: { type: 'string', required: true, description: 'Cart ID' }
      },
      response: {
        type: 'object',
        description: 'Cart details'
      }
    },
    addItem: {
      path: '/store/carts/:id/line-items',
      method: 'POST',
      description: 'Add item to cart',
      auth: 'publishable',
      params: {
        id: { type: 'string', required: true, description: 'Cart ID' }
      },
      body: {
        variant_id: { type: 'string', required: true, description: 'Product variant ID' },
        quantity: { type: 'number', required: true, description: 'Quantity' }
      },
      response: {
        type: 'object',
        description: 'Updated cart'
      }
    }
  }
};

/**
 * Helper functions for endpoint management
 */
export class EndpointMapper {
  /**
   * Get endpoint configuration by category and name
   */
  static getEndpoint(category: string, name: string): EndpointConfig | null {
    return API_ENDPOINTS[category]?.[name] || null;
  }

  /**
   * Build URL with parameters
   */
  static buildUrl(path: string, params: Record<string, any> = {}): string {
    let url = path;
    
    // Replace path parameters
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}`, String(value));
    });
    
    return url;
  }

  /**
   * Build query string from parameters
   */
  static buildQueryString(params: Record<string, any> = {}): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  /**
   * Get all endpoints for a category
   */
  static getCategoryEndpoints(category: string): Record<string, EndpointConfig> {
    return API_ENDPOINTS[category] || {};
  }

  /**
   * Get all endpoint categories
   */
  static getCategories(): string[] {
    return Object.keys(API_ENDPOINTS);
  }

  /**
   * Validate endpoint parameters
   */
  static validateParams(
    config: EndpointConfig, 
    params: Record<string, any>
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.params) {
      Object.entries(config.params).forEach(([key, paramConfig]) => {
        if (paramConfig.required && (params[key] === undefined || params[key] === null)) {
          errors.push(`Required parameter '${key}' is missing`);
        }
      });
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate request body
   */
  static validateBody(
    config: EndpointConfig, 
    body: Record<string, any>
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.body) {
      Object.entries(config.body).forEach(([key, bodyConfig]) => {
        if (bodyConfig.required && (body[key] === undefined || body[key] === null)) {
          errors.push(`Required body field '${key}' is missing`);
        }
      });
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export default API_ENDPOINTS;
