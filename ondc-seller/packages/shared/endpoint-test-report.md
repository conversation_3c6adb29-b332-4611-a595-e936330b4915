# API Endpoint Test Report

Generated: 2025-06-06T07:11:05.373Z

## Overall Summary

- **Total Tests**: 22
- **Passed**: 0 (0.0%)
- **Failed**: 22 (100.0%)
- **Warnings**: 0 (0.0%)

## system Category

- **Total**: 2
- **Passed**: 0
- **Failed**: 2
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| system.health | GET | ❌ | Network error: Body is unusable: Body has already been read | 73ms |
| system.version | GET | ❌ | Network error: Body is unusable: Body has already been read | 7ms |

## products Category

- **Total**: 3
- **Passed**: 0
- **Failed**: 3
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| products.list | GET | ❌ | HTTP 400: Bad Request | 8ms |
| products.get | GET | ❌ | HTTP 400: Bad Request | 4ms |
| products.featured | GET | ❌ | Network error: Body is unusable: Body has already been read | 4ms |

## adminProducts Category

- **Total**: 5
- **Passed**: 0
- **Failed**: 5
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| adminProducts.list | GET | ❌ | HTTP 401: Unauthorized | 4ms |
| adminProducts.get | GET | ❌ | HTTP 401: Unauthorized | 2ms |
| adminProducts.create | POST | ❌ | HTTP 401: Unauthorized | 79ms |
| adminProducts.update | POST | ❌ | HTTP 401: Unauthorized | 4ms |
| adminProducts.delete | DELETE | ❌ | HTTP 401: Unauthorized | 3ms |

## orders Category

- **Total**: 2
- **Passed**: 0
- **Failed**: 2
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| orders.list | GET | ❌ | HTTP 400: Bad Request | 3ms |
| orders.get | GET | ❌ | HTTP 400: Bad Request | 4ms |

## adminOrders Category

- **Total**: 2
- **Passed**: 0
- **Failed**: 2
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| adminOrders.list | GET | ❌ | HTTP 401: Unauthorized | 2ms |
| adminOrders.get | GET | ❌ | HTTP 401: Unauthorized | 2ms |

## adminCustomers Category

- **Total**: 2
- **Passed**: 0
- **Failed**: 2
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| adminCustomers.list | GET | ❌ | HTTP 401: Unauthorized | 2ms |
| adminCustomers.get | GET | ❌ | HTTP 401: Unauthorized | 2ms |

## collections Category

- **Total**: 2
- **Passed**: 0
- **Failed**: 2
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| collections.list | GET | ❌ | HTTP 400: Bad Request | 2ms |
| collections.get | GET | ❌ | HTTP 400: Bad Request | 2ms |

## regions Category

- **Total**: 1
- **Passed**: 0
- **Failed**: 1
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| regions.list | GET | ❌ | HTTP 400: Bad Request | 4ms |

## cart Category

- **Total**: 3
- **Passed**: 0
- **Failed**: 3
- **Warnings**: 0

### Test Results

| Endpoint | Method | Status | Message | Response Time |
|----------|--------|--------|---------|---------------|
| cart.create | POST | ❌ | HTTP 400: Bad Request | 4ms |
| cart.get | GET | ❌ | HTTP 400: Bad Request | 4ms |
| cart.addItem | POST | ❌ | HTTP 400: Bad Request | 6ms |

