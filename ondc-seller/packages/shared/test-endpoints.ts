#!/usr/bin/env node

/**
 * Test script for endpoint mapping
 * Run this to verify bidirectional endpoint mapping between backend and frontend
 */

import { runEndpointTests } from './endpoint-tester';

async function main() {
  const config = {
    baseURL: process.env.MEDUSA_API_URL || 'http://localhost:9000',
    publishableKey: process.env.MEDUSA_PUBLISHABLE_KEY,
    adminToken: process.env.MEDUSA_ADMIN_TOKEN,
    outputFile: './endpoint-test-report.md'
  };

  console.log('🔧 Configuration:');
  console.log(`  Base URL: ${config.baseURL}`);
  console.log(`  Publishable Key: ${config.publishableKey ? 'Present' : 'Missing'}`);
  console.log(`  Admin Token: ${config.adminToken ? 'Present' : 'Missing'}`);
  console.log(`  Output File: ${config.outputFile}\n`);

  try {
    const testSuites = await runEndpointTests(config);
    
    // Calculate overall success rate
    const totalTests = testSuites.reduce((sum, suite) => sum + suite.summary.total, 0);
    const totalPassed = testSuites.reduce((sum, suite) => sum + suite.summary.passed, 0);
    const successRate = ((totalPassed / totalTests) * 100).toFixed(1);
    
    console.log(`\n🎯 Overall Success Rate: ${successRate}%`);
    
    if (totalPassed === totalTests) {
      console.log('🎉 All endpoints are working correctly!');
      process.exit(0);
    } else {
      console.log('⚠️  Some endpoints need attention. Check the report for details.');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error running endpoint tests:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
