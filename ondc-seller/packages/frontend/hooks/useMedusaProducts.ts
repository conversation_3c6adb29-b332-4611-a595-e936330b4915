/**
 * React hooks for Medusa product management
 * Updated to use the new API client with shared endpoint configuration
 */

import { useState, useEffect, useCallback } from 'react';
import { apiClient, APIClientError } from '../lib/api-client';
import { MedusaProduct, MedusaAPIError } from '../lib/medusa-api';

// Hook for fetching products list
export function useMedusaProducts(
  params: {
    limit?: number;
    offset?: number;
    region_id?: string;
    collection_id?: string;
    category_id?: string;
    tags?: string[];
    q?: string;
    autoFetch?: boolean;
  } = {}
) {
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const { autoFetch = true, ...apiParams } = params;

  const fetchProducts = useCallback(
    async (reset = false) => {
      setLoading(true);
      setError(null);

      try {
        const currentOffset = reset ? 0 : apiParams.offset || 0;
        const response = await medusaAPI.getProducts({
          ...apiParams,
          offset: currentOffset,
        });

        const newProducts = response.products || [];

        if (reset) {
          setProducts(newProducts);
        } else {
          setProducts(prev => [...prev, ...newProducts]);
        }

        setTotalCount(response.count || 0);
        setHasMore(newProducts.length === (apiParams.limit || 10));
      } catch (err) {
        const errorMessage =
          err instanceof MedusaAPIError ? err.message : 'Failed to fetch products';
        setError(errorMessage);
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    },
    [apiParams]
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchProducts(false);
    }
  }, [fetchProducts, loading, hasMore]);

  const refresh = useCallback(() => {
    fetchProducts(true);
  }, [fetchProducts]);

  useEffect(() => {
    if (autoFetch) {
      fetchProducts(true);
    }
  }, [fetchProducts, autoFetch]);

  return {
    products,
    loading,
    error,
    totalCount,
    hasMore,
    fetchProducts: () => fetchProducts(true),
    loadMore,
    refresh,
  };
}

// Hook for fetching a single product
export function useMedusaProduct(productId: string | null, regionId?: string) {
  const [product, setProduct] = useState<MedusaProduct | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!productId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await medusaAPI.getProduct(productId, regionId);
      setProduct(response.product);
    } catch (err) {
      const errorMessage = err instanceof MedusaAPIError ? err.message : 'Failed to fetch product';
      setError(errorMessage);
      console.error('Error fetching product:', err);
    } finally {
      setLoading(false);
    }
  }, [productId, regionId]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
}

// Hook for searching products
export function useMedusaProductSearch() {
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [query, setQuery] = useState('');

  const searchProducts = useCallback(
    async (
      searchQuery: string,
      params: {
        limit?: number;
        offset?: number;
        region_id?: string;
      } = {}
    ) => {
      if (!searchQuery.trim()) {
        setProducts([]);
        return;
      }

      setLoading(true);
      setError(null);
      setQuery(searchQuery);

      try {
        const response = await medusaAPI.searchProducts(searchQuery, params);
        setProducts(response.products || []);
      } catch (err) {
        const errorMessage =
          err instanceof MedusaAPIError ? err.message : 'Failed to search products';
        setError(errorMessage);
        console.error('Error searching products:', err);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const clearSearch = useCallback(() => {
    setProducts([]);
    setQuery('');
    setError(null);
  }, []);

  return {
    products,
    loading,
    error,
    query,
    searchProducts,
    clearSearch,
  };
}

// Hook for admin product management
export function useMedusaAdminProducts(
  params: {
    limit?: number;
    offset?: number;
    q?: string;
    status?: string[];
    collection_id?: string[];
    tags?: string[];
    autoFetch?: boolean;
  } = {}
) {
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const { autoFetch = true, ...apiParams } = params;

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await medusaAPI.getAdminProducts(apiParams);
      setProducts(response.products || []);
      setTotalCount(response.count || 0);
    } catch (err) {
      const errorMessage =
        err instanceof MedusaAPIError ? err.message : 'Failed to fetch admin products';
      setError(errorMessage);
      console.error('Error fetching admin products:', err);
    } finally {
      setLoading(false);
    }
  }, [apiParams]);

  const createProduct = useCallback(
    async (productData: {
      title: string;
      description?: string;
      handle?: string;
      status?: 'draft' | 'proposed' | 'published' | 'rejected';
      thumbnail?: string;
      images?: string[];
      collection_id?: string;
      tags?: Array<{ value: string }>;
    }) => {
      setLoading(true);
      setError(null);

      try {
        const response = await medusaAPI.createProduct(productData);
        await fetchProducts(); // Refresh the list
        return response.product;
      } catch (err) {
        const errorMessage =
          err instanceof MedusaAPIError ? err.message : 'Failed to create product';
        setError(errorMessage);
        console.error('Error creating product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  const updateProduct = useCallback(
    async (
      id: string,
      productData: Partial<{
        title: string;
        description: string;
        handle: string;
        status: 'draft' | 'proposed' | 'published' | 'rejected';
        thumbnail: string;
        images: string[];
        collection_id: string;
        tags: Array<{ value: string }>;
      }>
    ) => {
      setLoading(true);
      setError(null);

      try {
        const response = await medusaAPI.updateProduct(id, productData);
        await fetchProducts(); // Refresh the list
        return response.product;
      } catch (err) {
        const errorMessage =
          err instanceof MedusaAPIError ? err.message : 'Failed to update product';
        setError(errorMessage);
        console.error('Error updating product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  const deleteProduct = useCallback(
    async (id: string) => {
      setLoading(true);
      setError(null);

      try {
        await medusaAPI.deleteProduct(id);
        await fetchProducts(); // Refresh the list
      } catch (err) {
        const errorMessage =
          err instanceof MedusaAPIError ? err.message : 'Failed to delete product';
        setError(errorMessage);
        console.error('Error deleting product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  useEffect(() => {
    if (autoFetch) {
      fetchProducts();
    }
  }, [fetchProducts, autoFetch]);

  return {
    products,
    loading,
    error,
    totalCount,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    refresh: fetchProducts,
  };
}
