'use client';

import React, { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface BrandFilterProps {
  selectedBrands: string[];
  onChange: (brands: string[]) => void;
}

// Mock brand data - in real app, this would come from API
const availableBrands = [
  { name: 'Apple', count: 45 },
  { name: 'Samsung', count: 38 },
  { name: 'Sony', count: 32 },
  { name: 'Nike', count: 28 },
  { name: 'Adidas', count: 25 },
  { name: 'Canon', count: 22 },
  { name: 'Dell', count: 20 },
  { name: 'HP', count: 18 },
  { name: '<PERSON><PERSON>', count: 16 },
  { name: 'Microsoft', count: 15 },
  { name: 'Google', count: 12 },
  { name: 'Amazon', count: 10 },
  { name: 'Bose', count: 8 },
  { name: 'J<PERSON>', count: 7 },
  { name: 'Logitech', count: 6 },
];

export default function BrandFilter({ selectedBrands, onChange }: BrandFilterProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAll, setShowAll] = useState(false);

  const filteredBrands = availableBrands.filter(brand =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const displayedBrands = showAll ? filteredBrands : filteredBrands.slice(0, 8);

  const handleBrandToggle = (brandName: string) => {
    const newSelectedBrands = selectedBrands.includes(brandName)
      ? selectedBrands.filter(brand => brand !== brandName)
      : [...selectedBrands, brandName];
    
    onChange(newSelectedBrands);
  };

  const handleSelectAll = () => {
    if (selectedBrands.length === filteredBrands.length) {
      onChange([]);
    } else {
      onChange(filteredBrands.map(brand => brand.name));
    }
  };

  const clearSelection = () => {
    onChange([]);
  };

  return (
    <div className="space-y-3">
      {/* Search Input */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search brands..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Action Buttons */}
      {filteredBrands.length > 0 && (
        <div className="flex justify-between text-xs">
          <button
            onClick={handleSelectAll}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            {selectedBrands.length === filteredBrands.length ? 'Deselect All' : 'Select All'}
          </button>
          {selectedBrands.length > 0 && (
            <button
              onClick={clearSelection}
              className="text-gray-500 hover:text-gray-700"
            >
              Clear ({selectedBrands.length})
            </button>
          )}
        </div>
      )}

      {/* Brand List */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {displayedBrands.length > 0 ? (
          displayedBrands.map((brand) => (
            <label
              key={brand.name}
              className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 cursor-pointer group"
            >
              <div className="flex items-center flex-1 min-w-0">
                <input
                  type="checkbox"
                  checked={selectedBrands.includes(brand.name)}
                  onChange={() => handleBrandToggle(brand.name)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-3 text-sm text-gray-700 truncate group-hover:text-gray-900">
                  {brand.name}
                </span>
              </div>
              <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                ({brand.count})
              </span>
            </label>
          ))
        ) : (
          <div className="text-sm text-gray-500 text-center py-4">
            No brands found matching "{searchTerm}"
          </div>
        )}
      </div>

      {/* Show More/Less Button */}
      {filteredBrands.length > 8 && (
        <button
          onClick={() => setShowAll(!showAll)}
          className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium py-2 border-t border-gray-200"
        >
          {showAll ? 'Show Less' : `Show ${filteredBrands.length - 8} More`}
        </button>
      )}

      {/* Selected Brands Summary */}
      {selectedBrands.length > 0 && (
        <div className="pt-3 border-t border-gray-200">
          <div className="text-xs font-medium text-gray-700 mb-2">
            Selected Brands ({selectedBrands.length}):
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedBrands.map((brand) => (
              <span
                key={brand}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
              >
                {brand}
                <button
                  onClick={() => handleBrandToggle(brand)}
                  className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200"
                >
                  <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
