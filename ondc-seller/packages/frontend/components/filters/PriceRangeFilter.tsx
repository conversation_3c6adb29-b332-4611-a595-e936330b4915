'use client';

import React, { useState, useEffect } from 'react';

interface PriceRangeFilterProps {
  min: number;
  max: number;
  onChange: (min: number, max: number) => void;
  minLimit?: number;
  maxLimit?: number;
}

export default function PriceRangeFilter({
  min,
  max,
  onChange,
  minLimit = 0,
  maxLimit = 1000,
}: PriceRangeFilterProps) {
  const [localMin, setLocalMin] = useState(min);
  const [localMax, setLocalMax] = useState(max);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    setLocalMin(min);
    setLocalMax(max);
  }, [min, max]);

  const handleMinChange = (value: number) => {
    const newMin = Math.max(minLimit, Math.min(value, localMax - 1));
    setLocalMin(newMin);
    if (!isDragging) {
      onChange(newMin, localMax);
    }
  };

  const handleMaxChange = (value: number) => {
    const newMax = Math.min(maxLimit, Math.max(value, localMin + 1));
    setLocalMax(newMax);
    if (!isDragging) {
      onChange(localMin, newMax);
    }
  };

  const handleMinInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || minLimit;
    handleMinChange(value);
  };

  const handleMaxInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || maxLimit;
    handleMaxChange(value);
  };

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'min' | 'max') => {
    const value = parseInt(e.target.value);
    if (type === 'min') {
      handleMinChange(value);
    } else {
      handleMaxChange(value);
    }
  };

  const handleMouseDown = () => {
    setIsDragging(true);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    onChange(localMin, localMax);
  };

  const minPercent = ((localMin - minLimit) / (maxLimit - minLimit)) * 100;
  const maxPercent = ((localMax - minLimit) / (maxLimit - minLimit)) * 100;

  return (
    <div className="space-y-4">
      {/* Price Input Fields */}
      <div className="flex items-center space-x-3">
        <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">Min Price</label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
            <input
              type="number"
              value={localMin}
              onChange={handleMinInputChange}
              min={minLimit}
              max={localMax - 1}
              className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">Max Price</label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
            <input
              type="number"
              value={localMax}
              onChange={handleMaxInputChange}
              min={localMin + 1}
              max={maxLimit}
              className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Dual Range Slider */}
      <div className="relative">
        <div className="relative h-2 bg-gray-200 rounded-full">
          {/* Active range track */}
          <div
            className="absolute h-2 bg-blue-500 rounded-full"
            style={{
              left: `${minPercent}%`,
              width: `${maxPercent - minPercent}%`,
            }}
          />
        </div>

        {/* Min slider */}
        <input
          type="range"
          min={minLimit}
          max={maxLimit}
          value={localMin}
          onChange={(e) => handleSliderChange(e, 'min')}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onTouchStart={handleMouseDown}
          onTouchEnd={handleMouseUp}
          className="absolute top-0 w-full h-2 bg-transparent appearance-none cursor-pointer slider-thumb"
          style={{ zIndex: localMin > maxLimit - 100 ? 2 : 1 }}
        />

        {/* Max slider */}
        <input
          type="range"
          min={minLimit}
          max={maxLimit}
          value={localMax}
          onChange={(e) => handleSliderChange(e, 'max')}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onTouchStart={handleMouseDown}
          onTouchEnd={handleMouseUp}
          className="absolute top-0 w-full h-2 bg-transparent appearance-none cursor-pointer slider-thumb"
          style={{ zIndex: localMax < minLimit + 100 ? 2 : 1 }}
        />
      </div>

      {/* Price Range Display */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>${minLimit}</span>
        <span className="font-medium text-gray-700">
          ${localMin} - ${localMax}
        </span>
        <span>${maxLimit}</span>
      </div>

      {/* Quick Price Ranges */}
      <div className="grid grid-cols-2 gap-2">
        {[
          { label: 'Under $25', min: 0, max: 25 },
          { label: '$25 - $50', min: 25, max: 50 },
          { label: '$50 - $100', min: 50, max: 100 },
          { label: '$100+', min: 100, max: maxLimit },
        ].map((range) => (
          <button
            key={range.label}
            onClick={() => onChange(range.min, range.max)}
            className={`px-3 py-2 text-xs rounded-md border transition-colors ${
              localMin === range.min && localMax === range.max
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
            }`}
          >
            {range.label}
          </button>
        ))}
      </div>

      <style jsx>{`
        .slider-thumb::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slider-thumb::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  );
}
