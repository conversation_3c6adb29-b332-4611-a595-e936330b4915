'use client';

import React from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';

interface RatingFilterProps {
  selectedRating: number | null;
  onChange: (rating: number | null) => void;
}

const ratingOptions = [
  { value: 4, label: '4 Stars & Up', count: 156 },
  { value: 3, label: '3 Stars & Up', count: 289 },
  { value: 2, label: '2 Stars & Up', count: 412 },
  { value: 1, label: '1 Star & Up', count: 498 },
];

export default function RatingFilter({ selectedRating, onChange }: RatingFilterProps) {
  const renderStars = (rating: number, filled: boolean = true) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <div key={star} className="relative">
            {star <= rating ? (
              <StarIcon className="h-4 w-4 text-yellow-400" />
            ) : (
              <StarOutlineIcon className="h-4 w-4 text-gray-300" />
            )}
          </div>
        ))}
      </div>
    );
  };

  const handleRatingClick = (rating: number) => {
    if (selectedRating === rating) {
      onChange(null); // Deselect if already selected
    } else {
      onChange(rating);
    }
  };

  const clearRating = () => {
    onChange(null);
  };

  return (
    <div className="space-y-3">
      {/* Clear Selection */}
      {selectedRating !== null && (
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedRating}+ Stars Selected
          </span>
          <button
            onClick={clearRating}
            className="text-xs text-blue-600 hover:text-blue-700 font-medium"
          >
            Clear
          </button>
        </div>
      )}

      {/* Rating Options */}
      <div className="space-y-2">
        {ratingOptions.map((option) => (
          <label
            key={option.value}
            className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 cursor-pointer group"
          >
            <div className="flex items-center flex-1">
              <input
                type="radio"
                name="rating"
                checked={selectedRating === option.value}
                onChange={() => handleRatingClick(option.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="ml-3 flex items-center space-x-2">
                {renderStars(option.value)}
                <span className="text-sm text-gray-700 group-hover:text-gray-900">
                  & Up
                </span>
              </div>
            </div>
            <span className="text-xs text-gray-500 ml-2">
              ({option.count})
            </span>
          </label>
        ))}
      </div>

      {/* All Ratings Option */}
      <label className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 cursor-pointer group">
        <div className="flex items-center flex-1">
          <input
            type="radio"
            name="rating"
            checked={selectedRating === null}
            onChange={() => onChange(null)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <span className="ml-3 text-sm text-gray-700 group-hover:text-gray-900">
            All Ratings
          </span>
        </div>
        <span className="text-xs text-gray-500 ml-2">
          (498)
        </span>
      </label>

      {/* Rating Distribution Visual */}
      <div className="pt-3 border-t border-gray-200">
        <div className="text-xs font-medium text-gray-700 mb-3">Rating Distribution</div>
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((stars) => {
            const percentage = stars === 5 ? 65 : stars === 4 ? 20 : stars === 3 ? 10 : stars === 2 ? 3 : 2;
            return (
              <div key={stars} className="flex items-center space-x-2">
                <div className="flex items-center w-12">
                  <span className="text-xs text-gray-600 w-2">{stars}</span>
                  <StarIcon className="h-3 w-3 text-yellow-400 ml-1" />
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500 w-8">{percentage}%</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Interactive Star Rating */}
      <div className="pt-3 border-t border-gray-200">
        <div className="text-xs font-medium text-gray-700 mb-2">Quick Select</div>
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              onClick={() => handleRatingClick(star)}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
              title={`${star} stars & up`}
            >
              <StarIcon
                className={`h-5 w-5 transition-colors ${
                  selectedRating && star <= selectedRating
                    ? 'text-yellow-400'
                    : 'text-gray-300 hover:text-yellow-300'
                }`}
              />
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
