'use client';

import React, { useState } from 'react';
import { XMarkIcon, FunnelIcon } from '@heroicons/react/24/outline';
import PriceRangeFilter from './PriceRangeFilter';
import BrandFilter from './BrandFilter';
import RatingFilter from './RatingFilter';
import AvailabilityFilter from './AvailabilityFilter';

interface FilterSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
}

export interface FilterState {
  priceRange: {
    min: number;
    max: number;
  };
  brands: string[];
  rating: number | null;
  availability: 'all' | 'in-stock' | 'out-of-stock';
  categories: string[];
}

export const defaultFilters: FilterState = {
  priceRange: { min: 0, max: 1000 },
  brands: [],
  rating: null,
  availability: 'all',
  categories: [],
};

export default function FilterSidebar({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  className = '',
}: FilterSidebarProps) {
  const [localFilters, setLocalFilters] = useState<FilterState>(filters);

  const handlePriceRangeChange = (min: number, max: number) => {
    const newFilters = {
      ...localFilters,
      priceRange: { min, max },
    };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleBrandChange = (brands: string[]) => {
    const newFilters = {
      ...localFilters,
      brands,
    };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleRatingChange = (rating: number | null) => {
    const newFilters = {
      ...localFilters,
      rating,
    };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleAvailabilityChange = (availability: 'all' | 'in-stock' | 'out-of-stock') => {
    const newFilters = {
      ...localFilters,
      availability,
    };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const hasActiveFilters = () => {
    return (
      localFilters.brands.length > 0 ||
      localFilters.rating !== null ||
      localFilters.availability !== 'all' ||
      localFilters.priceRange.min > 0 ||
      localFilters.priceRange.max < 1000
    );
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } ${className}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <FunnelIcon className="h-5 w-5 text-gray-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            </div>
            <div className="flex items-center space-x-2">
              {hasActiveFilters() && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  Clear All
                </button>
              )}
              <button
                onClick={onClose}
                className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            {/* Price Range Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Price Range</h3>
              <PriceRangeFilter
                min={localFilters.priceRange.min}
                max={localFilters.priceRange.max}
                onChange={handlePriceRangeChange}
              />
            </div>

            {/* Brand Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Brands</h3>
              <BrandFilter selectedBrands={localFilters.brands} onChange={handleBrandChange} />
            </div>

            {/* Rating Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Customer Rating</h3>
              <RatingFilter selectedRating={localFilters.rating} onChange={handleRatingChange} />
            </div>

            {/* Availability Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Availability</h3>
              <AvailabilityFilter
                selectedAvailability={localFilters.availability}
                onChange={handleAvailabilityChange}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {hasActiveFilters() ? (
                <span>
                  {localFilters.brands.length > 0 && `${localFilters.brands.length} brands, `}
                  {localFilters.rating && `${localFilters.rating}+ stars, `}
                  {localFilters.availability !== 'all' && `${localFilters.availability}, `}$
                  {localFilters.priceRange.min} - ${localFilters.priceRange.max}
                </span>
              ) : (
                'No filters applied'
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
