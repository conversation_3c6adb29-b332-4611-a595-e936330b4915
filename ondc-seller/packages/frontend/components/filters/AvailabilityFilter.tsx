'use client';

import React from 'react';
import { CheckCircleIcon, XCircleIcon, Squares2X2Icon } from '@heroicons/react/24/outline';

interface AvailabilityFilterProps {
  selectedAvailability: 'all' | 'in-stock' | 'out-of-stock';
  onChange: (availability: 'all' | 'in-stock' | 'out-of-stock') => void;
}

const availabilityOptions = [
  {
    value: 'all' as const,
    label: 'All Products',
    description: 'Show all products regardless of stock status',
    icon: Squares2X2Icon,
    count: 1247,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
  },
  {
    value: 'in-stock' as const,
    label: 'In Stock',
    description: 'Products available for immediate shipping',
    icon: CheckCircleIcon,
    count: 1089,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
  },
  {
    value: 'out-of-stock' as const,
    label: 'Out of Stock',
    description: 'Products currently unavailable',
    icon: XCircleIcon,
    count: 158,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
  },
];

export default function AvailabilityFilter({ selectedAvailability, onChange }: AvailabilityFilterProps) {
  const handleAvailabilityChange = (availability: 'all' | 'in-stock' | 'out-of-stock') => {
    onChange(availability);
  };

  return (
    <div className="space-y-3">
      {/* Availability Options */}
      <div className="space-y-2">
        {availabilityOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = selectedAvailability === option.value;
          
          return (
            <label
              key={option.value}
              className={`flex items-start p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                isSelected
                  ? `${option.bgColor} ${option.borderColor} shadow-sm`
                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="availability"
                value={option.value}
                checked={isSelected}
                onChange={() => handleAvailabilityChange(option.value)}
                className="sr-only"
              />
              
              <div className="flex items-start w-full">
                <div className={`flex-shrink-0 ${isSelected ? option.color : 'text-gray-400'}`}>
                  <Icon className="h-5 w-5" />
                </div>
                
                <div className="ml-3 flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium ${
                      isSelected ? 'text-gray-900' : 'text-gray-700'
                    }`}>
                      {option.label}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      isSelected 
                        ? `${option.bgColor} ${option.color} font-medium`
                        : 'bg-gray-100 text-gray-500'
                    }`}>
                      {option.count}
                    </span>
                  </div>
                  <p className={`text-xs mt-1 ${
                    isSelected ? 'text-gray-600' : 'text-gray-500'
                  }`}>
                    {option.description}
                  </p>
                </div>
              </div>
            </label>
          );
        })}
      </div>

      {/* Stock Status Summary */}
      <div className="pt-3 border-t border-gray-200">
        <div className="text-xs font-medium text-gray-700 mb-3">Stock Summary</div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
              <span className="text-xs text-gray-600">Available</span>
            </div>
            <span className="text-xs font-medium text-gray-900">87%</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
              <span className="text-xs text-gray-600">Out of Stock</span>
            </div>
            <span className="text-xs font-medium text-gray-900">13%</span>
          </div>
        </div>
        
        {/* Visual Progress Bar */}
        <div className="mt-3">
          <div className="flex rounded-full overflow-hidden h-2">
            <div className="bg-green-400 flex-1" style={{ width: '87%' }}></div>
            <div className="bg-red-400" style={{ width: '13%' }}></div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="pt-3 border-t border-gray-200">
        <div className="text-xs font-medium text-gray-700 mb-2">Quick Actions</div>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => handleAvailabilityChange('in-stock')}
            className={`px-3 py-2 text-xs rounded-md border transition-colors ${
              selectedAvailability === 'in-stock'
                ? 'bg-green-50 border-green-200 text-green-700'
                : 'bg-white border-gray-200 text-gray-700 hover:bg-green-50'
            }`}
          >
            Available Only
          </button>
          <button
            onClick={() => handleAvailabilityChange('all')}
            className={`px-3 py-2 text-xs rounded-md border transition-colors ${
              selectedAvailability === 'all'
                ? 'bg-gray-50 border-gray-200 text-gray-700'
                : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Show All
          </button>
        </div>
      </div>
    </div>
  );
}
