import React, { useState } from 'react';
import { PaymentInfo } from '@/app/checkout/page';

interface PaymentMethodProps {
  paymentInfo: PaymentInfo;
  setPaymentInfo: (info: PaymentInfo) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export default function PaymentMethod({
  paymentInfo,
  setPaymentInfo,
  onNext,
  onPrevious,
}: PaymentMethodProps) {
  const [errors, setErrors] = useState<Partial<PaymentInfo>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<PaymentInfo> = {};

    if (paymentInfo.method === 'card') {
      if (!paymentInfo.cardNumber?.trim()) {
        newErrors.cardNumber = 'Card number is required';
      } else if (!/^\d{16}$/.test(paymentInfo.cardNumber.replace(/\s/g, ''))) {
        newErrors.cardNumber = 'Please enter a valid 16-digit card number';
      }

      if (!paymentInfo.expiryDate?.trim()) {
        newErrors.expiryDate = 'Expiry date is required';
      } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(paymentInfo.expiryDate)) {
        newErrors.expiryDate = 'Please enter a valid expiry date (MM/YY)';
      }

      if (!paymentInfo.cvv?.trim()) {
        newErrors.cvv = 'CVV is required';
      } else if (!/^\d{3,4}$/.test(paymentInfo.cvv)) {
        newErrors.cvv = 'Please enter a valid CVV';
      }

      if (!paymentInfo.cardholderName?.trim()) {
        newErrors.cardholderName = 'Cardholder name is required';
      }
    } else if (paymentInfo.method === 'upi') {
      if (!paymentInfo.upiId?.trim()) {
        newErrors.upiId = 'UPI ID is required';
      } else if (!/^[\w.-]+@[\w.-]+$/.test(paymentInfo.upiId)) {
        newErrors.upiId = 'Please enter a valid UPI ID';
      }
    } else if (paymentInfo.method === 'netbanking') {
      if (!paymentInfo.bankName?.trim()) {
        newErrors.bankName = 'Please select a bank';
      }
    } else if (paymentInfo.method === 'wallet') {
      if (!paymentInfo.walletType?.trim()) {
        newErrors.walletType = 'Please select a wallet';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onNext();
    }
  };

  const handleMethodChange = (method: PaymentInfo['method']) => {
    setPaymentInfo({ method });
    setErrors({});
  };

  const handleInputChange = (field: keyof PaymentInfo, value: string) => {
    setPaymentInfo({ ...paymentInfo, [field]: value });
    if (errors[field]) {
      setErrors({ ...errors, [field]: undefined });
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Payment Method</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Payment Method Selection */}
        <div className="space-y-4">
          {/* Credit/Debit Card */}
          <div className="border rounded-lg p-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="card"
                checked={paymentInfo.method === 'card'}
                onChange={() => handleMethodChange('card')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="ml-3 flex items-center">
                <span className="text-sm font-medium text-gray-900">Credit/Debit Card</span>
                <div className="ml-2 flex space-x-1">
                  <img src="/images/payment/visa.svg" alt="Visa" className="h-6 w-auto" />
                  <img src="/images/payment/mastercard.svg" alt="Mastercard" className="h-6 w-auto" />
                  <img src="/images/payment/rupay.svg" alt="RuPay" className="h-6 w-auto" />
                </div>
              </div>
            </label>

            {paymentInfo.method === 'card' && (
              <div className="mt-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Card Number *</label>
                  <input
                    type="text"
                    value={paymentInfo.cardNumber || ''}
                    onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.cardNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                  />
                  {errors.cardNumber && <p className="mt-1 text-sm text-red-600">{errors.cardNumber}</p>}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Expiry Date *</label>
                    <input
                      type="text"
                      value={paymentInfo.expiryDate || ''}
                      onChange={(e) => handleInputChange('expiryDate', formatExpiryDate(e.target.value))}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.expiryDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="MM/YY"
                      maxLength={5}
                    />
                    {errors.expiryDate && <p className="mt-1 text-sm text-red-600">{errors.expiryDate}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">CVV *</label>
                    <input
                      type="text"
                      value={paymentInfo.cvv || ''}
                      onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, ''))}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.cvv ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="123"
                      maxLength={4}
                    />
                    {errors.cvv && <p className="mt-1 text-sm text-red-600">{errors.cvv}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Cardholder Name *</label>
                  <input
                    type="text"
                    value={paymentInfo.cardholderName || ''}
                    onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.cardholderName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Name as on card"
                  />
                  {errors.cardholderName && <p className="mt-1 text-sm text-red-600">{errors.cardholderName}</p>}
                </div>
              </div>
            )}
          </div>

          {/* UPI */}
          <div className="border rounded-lg p-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="upi"
                checked={paymentInfo.method === 'upi'}
                onChange={() => handleMethodChange('upi')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="ml-3 flex items-center">
                <span className="text-sm font-medium text-gray-900">UPI</span>
                <div className="ml-2 flex space-x-1">
                  <img src="/images/payment/upi.svg" alt="UPI" className="h-6 w-auto" />
                  <img src="/images/payment/gpay.svg" alt="Google Pay" className="h-6 w-auto" />
                  <img src="/images/payment/phonepe.svg" alt="PhonePe" className="h-6 w-auto" />
                </div>
              </div>
            </label>

            {paymentInfo.method === 'upi' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">UPI ID *</label>
                <input
                  type="text"
                  value={paymentInfo.upiId || ''}
                  onChange={(e) => handleInputChange('upiId', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.upiId ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="yourname@upi"
                />
                {errors.upiId && <p className="mt-1 text-sm text-red-600">{errors.upiId}</p>}
              </div>
            )}
          </div>

          {/* Net Banking */}
          <div className="border rounded-lg p-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="netbanking"
                checked={paymentInfo.method === 'netbanking'}
                onChange={() => handleMethodChange('netbanking')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-3 text-sm font-medium text-gray-900">Net Banking</span>
            </label>

            {paymentInfo.method === 'netbanking' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Bank *</label>
                <select
                  value={paymentInfo.bankName || ''}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.bankName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                >
                  <option value="">Choose your bank</option>
                  <option value="sbi">State Bank of India</option>
                  <option value="hdfc">HDFC Bank</option>
                  <option value="icici">ICICI Bank</option>
                  <option value="axis">Axis Bank</option>
                  <option value="kotak">Kotak Mahindra Bank</option>
                  <option value="pnb">Punjab National Bank</option>
                  <option value="other">Other</option>
                </select>
                {errors.bankName && <p className="mt-1 text-sm text-red-600">{errors.bankName}</p>}
              </div>
            )}
          </div>

          {/* Cash on Delivery */}
          <div className="border rounded-lg p-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="cod"
                checked={paymentInfo.method === 'cod'}
                onChange={() => handleMethodChange('cod')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="ml-3">
                <span className="text-sm font-medium text-gray-900">Cash on Delivery</span>
                <p className="text-xs text-gray-500 mt-1">Pay when you receive your order</p>
              </div>
            </label>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm text-green-800 font-medium">Secure Payment</p>
              <p className="text-xs text-green-700">Your payment information is encrypted and secure</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={onPrevious}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>
          <button
            type="submit"
            className="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center"
          >
            Review Order
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
}
