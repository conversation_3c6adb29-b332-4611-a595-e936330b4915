import React from 'react';
import { CheckIcon } from '@heroicons/react/24/solid';

interface Step {
  id: number;
  name: string;
  completed: boolean;
}

interface CheckoutStepsProps {
  steps: Step[];
  currentStep: number;
}

export default function CheckoutSteps({ steps, currentStep }: CheckoutStepsProps) {
  return (
    <nav aria-label="Progress" className="mb-16">
      <ol className="flex items-center justify-between">
        {steps.map((step, stepIdx) => (
          <li
            key={step.id}
            className={`relative ${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`}
          >
            {/* Connector line */}
            {stepIdx !== steps.length - 1 && (
              <div className="absolute inset-0 flex items-center" aria-hidden="true">
                <div className={`h-0.5 w-full ${step.completed ? 'bg-blue-600' : 'bg-gray-200'}`} />
              </div>
            )}

            {/* Step indicator */}
            <div className="relative flex items-center justify-center">
              {step.completed ? (
                <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center shadow-lg">
                  <CheckIcon className="h-6 w-6 text-white" />
                </div>
              ) : currentStep === step.id ? (
                <div className="h-10 w-10 rounded-full border-2 border-blue-600 bg-white flex items-center justify-center shadow-lg">
                  <div className="h-3 w-3 rounded-full bg-blue-600" />
                </div>
              ) : (
                <div className="h-10 w-10 rounded-full border-2 border-gray-300 bg-white flex items-center justify-center shadow-sm">
                  <div className="h-3 w-3 rounded-full bg-gray-300" />
                </div>
              )}
            </div>

            {/* Step label */}
            <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-36 text-center">
              <span
                className={`text-sm font-medium leading-tight ${
                  step.completed || currentStep === step.id ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                {step.name}
              </span>
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
}
