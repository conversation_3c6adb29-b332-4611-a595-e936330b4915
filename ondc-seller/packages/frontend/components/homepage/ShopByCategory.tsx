'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { getCategories } from '@/lib/strapi-api';

interface Category {
  id: number;
  name: string;
  slug: string;
  image: string;
  productCount: number;
  description: string;
}

// Helper function to extract text from rich text content
function extractTextFromRichText(richText: any): string {
  if (!richText) return '';

  if (Array.isArray(richText)) {
    return richText
      .map((block: any) => {
        if (block.children && Array.isArray(block.children)) {
          return block.children.map((child: any) => child.text || '').join('');
        }
        return '';
      })
      .join(' ');
  }

  if (typeof richText === 'string') {
    return richText;
  }

  return '';
}

// Helper function to transform Strapi category data
function transformStrapiCategory(strapiCategory: any): Category {
  const attributes = strapiCategory.attributes || strapiCategory;
  const name = attributes.name || strapiCategory.name || 'Unknown Category';
  const slug = attributes.slug || strapiCategory.slug || name.toLowerCase().replace(/\s+/g, '-');
  const description =
    extractTextFromRichText(attributes.description || strapiCategory.description) ||
    'Explore our products';

  // Generate image URL based on category name
  const imageMap: Record<string, string> = {
    electronics: '/images/categories/electronics.jpg',
    fashion: '/images/categories/fashion.jpg',
    'home-garden': '/images/categories/home-garden.jpg',
    'sports-fitness': '/images/categories/sports.jpg',
    'books-media': '/images/categories/books.jpg',
    'beauty-health': '/images/categories/beauty.jpg',
    automotive: '/images/categories/automotive.jpg',
    'toys-games': '/images/categories/toys.jpg',
    'organic-food': '/images/categories/organic-food.jpg',
    smartphones: '/images/categories/smartphones.jpg',
  };

  const image =
    imageMap[slug] ||
    imageMap[name.toLowerCase().replace(/\s+/g, '-')] ||
    '/images/categories/default.jpg';

  return {
    id: strapiCategory.id,
    name,
    slug,
    image,
    productCount: Math.floor(Math.random() * 2000) + 100, // Random count for now
    description: description.substring(0, 50) + (description.length > 50 ? '...' : ''),
  };
}

const defaultCategories: Category[] = [
  {
    id: 1,
    name: 'Electronics',
    slug: 'electronics',
    image: '/images/categories/electronics.jpg',
    productCount: 1250,
    description: 'Latest gadgets and tech',
  },
  {
    id: 2,
    name: 'Fashion',
    slug: 'fashion',
    image: '/images/categories/fashion.jpg',
    productCount: 2100,
    description: 'Trendy clothing & accessories',
  },
  {
    id: 3,
    name: 'Home & Garden',
    slug: 'home-garden',
    image: '/images/categories/home-garden.jpg',
    productCount: 890,
    description: 'Furniture & home decor',
  },
  {
    id: 4,
    name: 'Sports & Fitness',
    slug: 'sports-fitness',
    image: '/images/categories/sports.jpg',
    productCount: 650,
    description: 'Athletic gear & equipment',
  },
  {
    id: 5,
    name: 'Books & Media',
    slug: 'books-media',
    image: '/images/categories/books.jpg',
    productCount: 1800,
    description: 'Books, movies & music',
  },
  {
    id: 6,
    name: 'Beauty & Health',
    slug: 'beauty-health',
    image: '/images/categories/beauty.jpg',
    productCount: 750,
    description: 'Skincare & wellness',
  },
  {
    id: 7,
    name: 'Automotive',
    slug: 'automotive',
    image: '/images/categories/automotive.jpg',
    productCount: 420,
    description: 'Car parts & accessories',
  },
  {
    id: 8,
    name: 'Toys & Games',
    slug: 'toys-games',
    image: '/images/categories/toys.jpg',
    productCount: 980,
    description: 'Fun for all ages',
  },
];

export default function ShopByCategory() {
  const [categories, setCategories] = useState<Category[]>(defaultCategories);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Progressive disclosure: show first 4 categories by default
  const visibleCategories = isExpanded ? categories : categories.slice(0, 4);
  const hasMoreCategories = categories.length > 4;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Fetch categories from Strapi CMS via Next.js API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('🚀 ShopByCategory: useEffect triggered, fetching categories...');
        console.log('🚀 ShopByCategory: Current loading state:', isLoading);
        setIsLoading(true);

        // Add a small delay to ensure the component is fully mounted
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('🚀 ShopByCategory: Making API call to /api/categories');
        const response = await fetch('/api/categories?isSubcategory=false&pageSize=20');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ ShopByCategory: API response received:', result);

        if (result.success && result.data && result.data.length > 0) {
          console.log('✅ ShopByCategory: Categories received from API:', result.data);
          setCategories(result.data);
          console.log('✅ ShopByCategory: Categories state updated');
        } else {
          console.log('⚠️ ShopByCategory: No categories found, using default categories');
          setCategories(defaultCategories);
        }
      } catch (error) {
        console.error('❌ ShopByCategory: Error fetching categories:', error);
        console.log('🔄 ShopByCategory: Using default categories due to error');
        setCategories(defaultCategories);
      } finally {
        console.log('🏁 ShopByCategory: Setting loading to false');
        setIsLoading(false);
      }
    };

    // Add a small delay before starting the fetch
    const timer = setTimeout(() => {
      fetchCategories();
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  console.log(
    'ShopByCategory component rendered with categories:',
    categories.length,
    'loading:',
    isLoading
  );

  return (
    <section className="py-16 bg-white" data-testid="shop-by-category">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our wide range of products across different categories
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="aspect-square rounded-lg bg-gray-200 animate-pulse">
                <div className="h-full flex flex-col justify-end p-4">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Categories Grid */}
        {!isLoading && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 transition-all duration-500 ease-in-out">
            {visibleCategories.map(category => (
              <Link
                key={category.id}
                href={`/categories/${category.slug}`}
                className="group relative overflow-hidden rounded-lg bg-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                data-testid="category-link"
              >
                {/* Category Image */}
                <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                    onError={e => {
                      console.error(`Failed to load image: ${category.image}`);
                      // Fallback to a solid color background
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />

                  {/* Content */}
                  <div className="absolute inset-0 flex flex-col justify-end p-4">
                    <div className="text-white">
                      <h3 className="font-semibold text-lg mb-1 group-hover:text-yellow-300 transition-colors duration-200">
                        {category.name}
                      </h3>
                      <p className="text-sm text-white/90 mb-2">{category.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-white/80">
                          {(category.productCount || 0).toLocaleString()} items
                        </span>
                        <ArrowRightIcon className="h-4 w-4 text-white/80 group-hover:text-yellow-300 group-hover:translate-x-1 transition-all duration-200" />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Toggle Button for Progressive Disclosure */}
        {hasMoreCategories && (
          <div className="text-center mt-12">
            <button
              onClick={toggleExpanded}
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
              // className="inline-flex items-center px-8 py-3 bg-blue-600 text-white text-base font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              aria-expanded={isExpanded}
              aria-label={isExpanded ? 'Show fewer categories' : 'Show more categories'}
            >
              {isExpanded ? 'View Less' : 'View More'}
              <ChevronDownIcon
                className={`ml-2 h-5 w-5 transition-transform duration-300 ${
                  isExpanded ? 'rotate-180' : ''
                }`}
              />
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
