'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useLoading, usePageNavigation } from '@/contexts/LoadingContext';
import { LoadingSpinner } from '@/components/skeletons/SkeletonBase';

interface AdminNavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
  showLoadingIndicator?: boolean;
}

/**
 * Enhanced navigation link with loading states for admin interface
 */
export default function AdminNavLink({
  href,
  children,
  className = '',
  activeClassName = '',
  icon: Icon,
  onClick,
  showLoadingIndicator = true
}: AdminNavLinkProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { loading } = useLoading();
  const { navigateWithLoading } = usePageNavigation();
  const [isNavigating, setIsNavigating] = useState(false);

  const isActive = pathname === href;
  const isLoading = loading.isPageLoading || isNavigating;

  const handleClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    
    // Call custom onClick if provided
    onClick?.();

    // Don't navigate if already on the same page
    if (isActive) return;

    // Don't navigate if already loading
    if (isLoading) return;

    setIsNavigating(true);

    try {
      await navigateWithLoading(async () => {
        router.push(href);
      }, `Loading ${typeof children === 'string' ? children : 'page'}...`);
    } finally {
      setIsNavigating(false);
    }
  };

  const linkClassName = `
    ${className}
    ${isActive ? activeClassName : ''}
    ${isLoading ? 'opacity-75 cursor-wait' : 'cursor-pointer'}
    transition-all duration-200 ease-in-out
    relative
  `.trim();

  return (
    <Link
      href={href}
      className={linkClassName}
      onClick={handleClick}
      aria-current={isActive ? 'page' : undefined}
    >
      <div className="flex items-center">
        {Icon && (
          <div className="relative">
            <Icon className={`transition-colors duration-200 ${isLoading ? 'opacity-50' : ''}`} />
            {isLoading && showLoadingIndicator && (
              <div className="absolute inset-0 flex items-center justify-center">
                <LoadingSpinner size="sm" className="h-3 w-3 border-current border-t-transparent" />
              </div>
            )}
          </div>
        )}
        <span className={`${Icon ? 'ml-3' : ''} ${isLoading ? 'opacity-75' : ''}`}>
          {children}
        </span>
        {isLoading && showLoadingIndicator && !Icon && (
          <LoadingSpinner size="sm" className="ml-2 h-3 w-3 border-current border-t-transparent" />
        )}
      </div>
      
      {/* Loading progress bar */}
      {isLoading && loading.loadingProgress > 0 && (
        <div className="absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 ease-out"
             style={{ width: `${loading.loadingProgress}%` }} />
      )}
    </Link>
  );
}

/**
 * Enhanced navigation link for sidebar items
 */
export function SidebarNavLink({
  href,
  children,
  icon: Icon,
  isCollapsed = false,
  onClick
}: {
  href: string;
  children: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  isCollapsed?: boolean;
  onClick?: () => void;
}) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <AdminNavLink
      href={href}
      onClick={onClick}
      className={`
        group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
        ${isActive
          ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500 font-semibold shadow-sm'
          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
        }
        ${isCollapsed ? 'justify-center px-2' : ''}
      `}
      activeClassName="bg-blue-50 text-blue-900"
      icon={Icon}
    >
      {!isCollapsed && children}
    </AdminNavLink>
  );
}

/**
 * Enhanced navigation link for dropdown/submenu items
 */
export function DropdownNavLink({
  href,
  children,
  icon: Icon,
  onClick
}: {
  href: string;
  children: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
}) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <AdminNavLink
      href={href}
      onClick={onClick}
      className={`
        group flex items-center px-4 py-2.5 text-sm font-medium transition-colors duration-150
        ${isActive
          ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500 font-semibold'
          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        }
      `}
      activeClassName="bg-blue-50 text-blue-900"
      icon={Icon}
    >
      {children}
    </AdminNavLink>
  );
}

/**
 * Enhanced navigation link for breadcrumbs
 */
export function BreadcrumbNavLink({
  href,
  children,
  isLast = false
}: {
  href: string;
  children: React.ReactNode;
  isLast?: boolean;
}) {
  return (
    <AdminNavLink
      href={href}
      className={`
        text-sm transition-colors duration-200
        ${isLast
          ? 'text-gray-900 font-medium cursor-default'
          : 'text-gray-500 hover:text-gray-700'
        }
      `}
      showLoadingIndicator={false}
    >
      {children}
    </AdminNavLink>
  );
}

/**
 * Enhanced navigation link for action buttons
 */
export function ActionNavLink({
  href,
  children,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  onClick
}: {
  href: string;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
}) {
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-900',
    danger: 'bg-red-600 hover:bg-red-700 text-white'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <AdminNavLink
      href={href}
      onClick={onClick}
      className={`
        inline-flex items-center font-medium rounded-md transition-all duration-200 ease-in-out
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
      `}
      icon={Icon}
    >
      {children}
    </AdminNavLink>
  );
}
