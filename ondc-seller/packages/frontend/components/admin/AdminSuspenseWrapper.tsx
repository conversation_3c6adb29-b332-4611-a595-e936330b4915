'use client';

import React, { Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';
import TableSkeleton from '@/components/skeletons/TableSkeleton';
import FormSkeleton from '@/components/skeletons/FormSkeleton';
import DetailViewSkeleton from '@/components/skeletons/DetailViewSkeleton';
import { LoadingSpinner } from '@/components/skeletons/SkeletonBase';

interface AdminSuspenseWrapperProps {
  children: React.ReactNode;
  fallbackType?: 'dashboard' | 'table' | 'form' | 'detail' | 'default';
  className?: string;
}

/**
 * Error fallback component for admin screens
 */
function AdminErrorFallback({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg 
            className="w-8 h-8 text-red-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
            />
          </svg>
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Something went wrong
        </h2>
        
        <p className="text-gray-600 mb-4">
          {error.message || 'An unexpected error occurred while loading this page.'}
        </p>
        
        <div className="space-y-3">
          <button
            onClick={resetErrorBoundary}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
          >
            Try Again
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200"
          >
            Reload Page
          </button>
        </div>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

/**
 * Get appropriate skeleton component based on type
 */
function getSkeleton(type: string, className?: string) {
  switch (type) {
    case 'dashboard':
      return <DashboardSkeleton className={className} />;
    case 'table':
      return <TableSkeleton className={className} />;
    case 'form':
      return <FormSkeleton className={className} />;
    case 'detail':
      return <DetailViewSkeleton className={className} />;
    default:
      return (
        <div className={`flex items-center justify-center min-h-screen ${className || ''}`}>
          <div className="flex flex-col items-center space-y-4">
            <LoadingSpinner size="lg" />
            <div className="text-sm text-gray-600 font-medium">Loading...</div>
            <div className="text-xs text-gray-500">Please wait while we load your content</div>
          </div>
        </div>
      );
  }
}

/**
 * Admin Suspense Wrapper with error boundaries and appropriate loading states
 */
export default function AdminSuspenseWrapper({
  children,
  fallbackType = 'default',
  className
}: AdminSuspenseWrapperProps) {
  return (
    <ErrorBoundary
      FallbackComponent={AdminErrorFallback}
      onError={(error, errorInfo) => {
        // Log error for monitoring
        console.error('Admin Error:', error, errorInfo);
        
        // In production, you might want to send this to an error tracking service
        if (process.env.NODE_ENV === 'production') {
          // Example: Sentry.captureException(error, { extra: errorInfo });
        }
      }}
      onReset={() => {
        // Clear any cached data or reset state if needed
        window.location.reload();
      }}
    >
      <Suspense fallback={getSkeleton(fallbackType, className)}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Specialized wrappers for different admin screen types
 */
export function AdminDashboardWrapper({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <AdminSuspenseWrapper fallbackType="dashboard" className={className}>
      {children}
    </AdminSuspenseWrapper>
  );
}

export function AdminTableWrapper({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <AdminSuspenseWrapper fallbackType="table" className={className}>
      {children}
    </AdminSuspenseWrapper>
  );
}

export function AdminFormWrapper({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <AdminSuspenseWrapper fallbackType="form" className={className}>
      {children}
    </AdminSuspenseWrapper>
  );
}

export function AdminDetailWrapper({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <AdminSuspenseWrapper fallbackType="detail" className={className}>
      {children}
    </AdminSuspenseWrapper>
  );
}

/**
 * Hook for handling admin loading states
 */
export function useAdminLoading() {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const executeWithLoading = React.useCallback(async <T>(
    operation: () => Promise<T>,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await operation();
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      onError?.(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    executeWithLoading,
    clearError: () => setError(null)
  };
}

/**
 * Component for displaying loading states in admin sections
 */
export function AdminLoadingSection({ 
  isLoading, 
  children, 
  fallbackType = 'default',
  className = ''
}: {
  isLoading: boolean;
  children: React.ReactNode;
  fallbackType?: 'dashboard' | 'table' | 'form' | 'detail' | 'default';
  className?: string;
}) {
  if (isLoading) {
    return getSkeleton(fallbackType, className);
  }

  return <>{children}</>;
}

/**
 * Progressive loading component for admin data
 */
export function AdminProgressiveLoader({
  stages,
  currentStage,
  className = ''
}: {
  stages: string[];
  currentStage: number;
  className?: string;
}) {
  const progress = ((currentStage + 1) / stages.length) * 100;

  return (
    <div className={`flex flex-col items-center justify-center min-h-64 ${className}`}>
      <div className="w-16 h-16 mb-4 relative">
        <LoadingSpinner size="lg" />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-medium text-blue-600">
            {Math.round(progress)}%
          </span>
        </div>
      </div>
      
      <div className="text-center space-y-2">
        <div className="text-sm font-medium text-gray-900">
          {stages[currentStage] || 'Loading...'}
        </div>
        
        <div className="w-64 bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
        
        <div className="text-xs text-gray-500">
          Step {currentStage + 1} of {stages.length}
        </div>
      </div>
    </div>
  );
}
