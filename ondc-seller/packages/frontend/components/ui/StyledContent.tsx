import React from 'react';

interface StyledContentProps {
  content: string;
  className?: string;
}

const StyledContent: React.FC<StyledContentProps> = ({ content, className = '' }) => {
  // Process content to ensure proper line breaks and formatting
  const processedContent = content
    .replace(/\n\n/g, '</p><p>') // Convert double line breaks to paragraph breaks
    .replace(/\n/g, '<br />'); // Convert single line breaks to <br> tags

  return (
    <div
      className={`
        prose prose-lg max-w-none
        prose-headings:text-gray-900 prose-headings:font-semibold
        prose-h1:text-4xl prose-h1:font-bold prose-h1:mb-8 prose-h1:mt-0 prose-h1:text-blue-900
        prose-h2:text-3xl prose-h2:font-bold prose-h2:mb-6 prose-h2:mt-10 prose-h2:text-blue-800 prose-h2:border-b prose-h2:border-blue-200 prose-h2:pb-3
        prose-h3:text-2xl prose-h3:font-semibold prose-h3:mb-4 prose-h3:mt-8 prose-h3:text-blue-700
        prose-h4:text-xl prose-h4:font-medium prose-h4:mb-3 prose-h4:mt-6 prose-h4:text-blue-600
        prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-p:mt-0
        prose-strong:text-gray-900 prose-strong:font-semibold
        prose-ul:my-6 prose-ul:space-y-2 prose-ul:pl-6
        prose-ol:my-6 prose-ol:space-y-2 prose-ol:pl-6
        prose-li:text-gray-700 prose-li:leading-relaxed prose-li:my-1
        prose-li:marker:text-blue-600 prose-li:marker:font-medium
        prose-a:text-blue-600 prose-a:no-underline prose-a:font-medium hover:prose-a:text-blue-800 hover:prose-a:underline
        prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:bg-blue-50 prose-blockquote:p-4 prose-blockquote:rounded-r-lg prose-blockquote:my-6
        prose-code:bg-gray-100 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm prose-code:text-gray-800 prose-code:font-mono
        prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-pre:rounded-lg prose-pre:p-4 prose-pre:my-6
        prose-table:border-collapse prose-table:border prose-table:border-gray-300 prose-table:my-6
        prose-th:bg-gray-100 prose-th:border prose-th:border-gray-300 prose-th:px-4 prose-th:py-3 prose-th:text-left prose-th:font-semibold prose-th:text-gray-900
        prose-td:border prose-td:border-gray-300 prose-td:px-4 prose-td:py-3 prose-td:text-gray-700
        prose-tr:hover:bg-gray-50
        prose-hr:border-gray-300 prose-hr:my-8
        ${className}
      `}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default StyledContent;
