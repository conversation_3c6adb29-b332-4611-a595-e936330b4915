import React from 'react';

interface PageHeroProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  gradient?: 'blue' | 'green' | 'purple' | 'indigo';
}

const PageHero: React.FC<PageHeroProps> = ({ title, description, icon, gradient = 'blue' }) => {
  const gradientClasses = {
    blue: 'from-blue-600 via-blue-700 to-blue-800',
    green: 'from-green-600 via-green-700 to-green-800',
    purple: 'from-purple-600 via-purple-700 to-purple-800',
    indigo: 'from-indigo-600 via-indigo-700 to-indigo-800',
  };

  return (
    <div
      className={`relative bg-gradient-to-br ${gradientClasses[gradient]} text-white overflow-hidden`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/10">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-14 lg:py-16">
        <div className="text-center">
          {icon && (
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-white/10 rounded-full backdrop-blur-sm">{icon}</div>
            </div>
          )}

          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 tracking-tight">
            {title}
          </h1>

          {description && (
            <p className="text-lg sm:text-xl text-blue-100 max-w-2xl mx-auto leading-relaxed">
              {description}
            </p>
          )}

          {/* Decorative Elements */}
          <div className="mt-6 flex justify-center space-x-2">
            <div className="w-1.5 h-1.5 bg-white/60 rounded-full animate-pulse"></div>
            <div className="w-1.5 h-1.5 bg-white/40 rounded-full animate-pulse delay-75"></div>
            <div className="w-1.5 h-1.5 bg-white/60 rounded-full animate-pulse delay-150"></div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg
          className="w-full h-4 sm:h-6 lg:h-8 text-gray-50"
          fill="currentColor"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
          ></path>
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
          ></path>
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
        </svg>
      </div>
    </div>
  );
};

export default PageHero;
