'use client';

import React from 'react';

interface SkeletonBaseProps {
  className?: string;
  children?: React.ReactNode;
}

// Base shimmer animation component with ONDC branding
export function SkeletonShimmer({ className = '', children }: SkeletonBaseProps) {
  return <div className={`animate-pulse ${className}`}>{children}</div>;
}

// Enhanced shimmer with gradient effect
export function ShimmerEffect({ className = '', children }: SkeletonBaseProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {children}
      <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent" />
    </div>
  );
}

// Basic skeleton line
export function SkeletonLine({ className = '' }: { className?: string }) {
  return <div className={`bg-gray-200 rounded ${className}`} />;
}

// Skeleton circle (for avatars, icons)
export function SkeletonCircle({ className = '' }: { className?: string }) {
  return <div className={`bg-gray-200 rounded-full ${className}`} />;
}

// Skeleton rectangle
export function SkeletonRect({ className = '' }: { className?: string }) {
  return <div className={`bg-gray-200 rounded-md ${className}`} />;
}

// Skeleton card container
export function SkeletonCard({ className = '', children }: SkeletonBaseProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <SkeletonShimmer>{children}</SkeletonShimmer>
    </div>
  );
}

// Skeleton text with multiple lines
interface SkeletonTextProps {
  lines?: number;
  className?: string;
}

export function SkeletonText({ lines = 3, className = '' }: SkeletonTextProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonLine key={index} className={`h-4 ${index === lines - 1 ? 'w-3/4' : 'w-full'}`} />
      ))}
    </div>
  );
}

// Loading spinner component
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <div
      className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}
      role="status"
      aria-label="Loading..."
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

// Page loading overlay
export function PageLoadingOverlay() {
  return (
    <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-2 text-sm text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

// Inline loading state
export function InlineLoading({ message = 'Loading...' }: { message?: string }) {
  return (
    <div className="flex items-center justify-center py-8">
      <LoadingSpinner className="mr-2" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
}

// Page transition loader with ONDC branding
export function PageTransitionLoader({ isLoading }: { isLoading: boolean }) {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <LoadingSpinner size="lg" />
          <div className="absolute inset-0 animate-ping rounded-full border border-blue-600/20"></div>
        </div>
        <div className="text-sm text-gray-600 font-medium">Loading...</div>
        <div className="text-xs text-gray-500">Please wait while we load your content</div>
      </div>
    </div>
  );
}

// Content loading overlay for specific sections
export function ContentLoader({
  isLoading,
  children,
  className = '',
  message = 'Loading...',
}: {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  message?: string;
}) {
  return (
    <div className={`relative ${className}`}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg z-10">
          <div className="flex flex-col items-center space-y-2">
            <LoadingSpinner />
            <span className="text-sm text-gray-600">{message}</span>
          </div>
        </div>
      )}
    </div>
  );
}
