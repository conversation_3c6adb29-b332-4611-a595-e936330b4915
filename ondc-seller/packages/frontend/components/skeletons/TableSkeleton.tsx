'use client';

import React from 'react';
import { SkeletonShimmer, SkeletonLine, SkeletonRect, SkeletonCard, SkeletonCircle } from './SkeletonBase';

interface TableSkeletonProps {
  columns?: number;
  rows?: number;
  showHeader?: boolean;
  showSearch?: boolean;
  showPagination?: boolean;
  showActions?: boolean;
}

// Table header skeleton
function TableHeaderSkeleton({ columns, showActions }: { columns: number; showActions: boolean }) {
  return (
    <thead className="bg-gray-50">
      <tr>
        {Array.from({ length: columns }).map((_, index) => (
          <th key={index} className="px-6 py-3 text-left">
            <SkeletonLine className="h-4 w-20" />
          </th>
        ))}
        {showActions && (
          <th className="px-6 py-3 text-right">
            <SkeletonLine className="h-4 w-16" />
          </th>
        )}
      </tr>
    </thead>
  );
}

// Table row skeleton
function TableRowSkeleton({ columns, showActions }: { columns: number; showActions: boolean }) {
  return (
    <tr className="hover:bg-gray-50">
      {Array.from({ length: columns }).map((_, index) => (
        <td key={index} className="px-6 py-4 whitespace-nowrap">
          {index === 0 ? (
            // First column often has image + text
            <div className="flex items-center">
              <SkeletonCircle className="h-10 w-10 mr-3" />
              <div>
                <SkeletonLine className="h-4 w-24 mb-1" />
                <SkeletonLine className="h-3 w-16" />
              </div>
            </div>
          ) : index === columns - 1 ? (
            // Last column might be status or date
            <SkeletonRect className="h-6 w-16" />
          ) : (
            // Regular text columns
            <SkeletonLine className="h-4 w-20" />
          )}
        </td>
      ))}
      {showActions && (
        <td className="px-6 py-4 whitespace-nowrap text-right">
          <div className="flex items-center justify-end space-x-2">
            <SkeletonCircle className="h-4 w-4" />
            <SkeletonCircle className="h-4 w-4" />
            <SkeletonCircle className="h-4 w-4" />
          </div>
        </td>
      )}
    </tr>
  );
}

// Search and filters skeleton
function SearchFiltersSkeleton() {
  return (
    <div className="px-6 py-4 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex-1 max-w-lg">
          <SkeletonRect className="h-10 w-full" />
        </div>
        <SkeletonRect className="h-10 w-20 ml-4" />
      </div>
    </div>
  );
}

// Pagination skeleton
function PaginationSkeleton() {
  return (
    <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
      <SkeletonLine className="h-4 w-48" />
      <div className="flex items-center space-x-2">
        <SkeletonRect className="h-8 w-8" />
        <SkeletonLine className="h-4 w-20" />
        <SkeletonRect className="h-8 w-8" />
      </div>
    </div>
  );
}

// Main table skeleton component
export default function TableSkeleton({
  columns = 6,
  rows = 10,
  showHeader = true,
  showSearch = true,
  showPagination = true,
  showActions = true
}: TableSkeletonProps) {
  return (
    <SkeletonCard>
      <SkeletonShimmer>
        {/* Search and filters */}
        {showSearch && <SearchFiltersSkeleton />}

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            {/* Header */}
            {showHeader && (
              <TableHeaderSkeleton columns={columns} showActions={showActions} />
            )}
            
            {/* Body */}
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.from({ length: rows }).map((_, index) => (
                <TableRowSkeleton
                  key={index}
                  columns={columns}
                  showActions={showActions}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {showPagination && <PaginationSkeleton />}
      </SkeletonShimmer>
    </SkeletonCard>
  );
}

// Specialized table skeletons for different admin screens
export function ProductsTableSkeleton() {
  return (
    <TableSkeleton
      columns={7}
      rows={10}
      showHeader={true}
      showSearch={true}
      showPagination={true}
      showActions={true}
    />
  );
}

export function OrdersTableSkeleton() {
  return (
    <TableSkeleton
      columns={6}
      rows={8}
      showHeader={true}
      showSearch={true}
      showPagination={true}
      showActions={true}
    />
  );
}

export function CustomersTableSkeleton() {
  return (
    <TableSkeleton
      columns={5}
      rows={10}
      showHeader={true}
      showSearch={true}
      showPagination={true}
      showActions={true}
    />
  );
}

export function CategoriesTableSkeleton() {
  return (
    <TableSkeleton
      columns={4}
      rows={6}
      showHeader={true}
      showSearch={true}
      showPagination={false}
      showActions={true}
    />
  );
}
