'use client';

import React from 'react';
import { SkeletonShimmer, SkeletonLine, SkeletonRect, SkeletonCard, SkeletonCircle } from './SkeletonBase';

// Stats card skeleton
function StatsCardSkeleton() {
  return (
    <SkeletonCard className="p-5">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <SkeletonCircle className="h-6 w-6" />
        </div>
        <div className="ml-5 w-0 flex-1">
          <SkeletonLine className="h-4 w-20 mb-2" />
          <div className="flex items-baseline">
            <SkeletonLine className="h-8 w-24" />
            <SkeletonLine className="h-4 w-12 ml-2" />
          </div>
        </div>
      </div>
    </SkeletonCard>
  );
}

// Chart card skeleton
function ChartCardSkeleton({ className = '' }: { className?: string }) {
  return (
    <SkeletonCard className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <SkeletonLine className="h-5 w-32 mb-2" />
          <SkeletonLine className="h-4 w-48" />
        </div>
        <SkeletonRect className="h-8 w-24" />
      </div>
      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
        <div className="text-center">
          <SkeletonCircle className="h-8 w-8 mx-auto mb-2" />
          <SkeletonLine className="h-3 w-20" />
        </div>
      </div>
    </SkeletonCard>
  );
}

// Recent orders/products list skeleton
function ListCardSkeleton({ title }: { title: string }) {
  return (
    <SkeletonCard className="p-6">
      <div className="flex items-center justify-between mb-4">
        <SkeletonLine className="h-5 w-32" />
        <SkeletonLine className="h-4 w-16" />
      </div>
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <SkeletonCircle className="h-10 w-10" />
            <div className="flex-1 min-w-0">
              <SkeletonLine className="h-4 w-3/4 mb-1" />
              <SkeletonLine className="h-3 w-1/2" />
            </div>
            <div className="text-right">
              <SkeletonLine className="h-4 w-16 mb-1" />
              <SkeletonLine className="h-3 w-12" />
            </div>
            <SkeletonCircle className="h-5 w-5" />
          </div>
        ))}
      </div>
    </SkeletonCard>
  );
}

// Main dashboard skeleton component
export default function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Page header skeleton */}
      <div className="md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8">
        <div className="flex-1 min-w-0">
          <SkeletonShimmer>
            <SkeletonLine className="h-8 w-48 mb-2" />
            <SkeletonLine className="h-4 w-96" />
          </SkeletonShimmer>
        </div>
        <div className="mt-4 md:mt-0 md:ml-4">
          <SkeletonShimmer>
            <SkeletonRect className="h-10 w-32" />
          </SkeletonShimmer>
        </div>
      </div>

      {/* Stats cards skeleton */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-6 md:px-8">
        <SkeletonShimmer>
          {Array.from({ length: 4 }).map((_, index) => (
            <StatsCardSkeleton key={index} />
          ))}
        </SkeletonShimmer>
      </div>

      {/* Main charts skeleton */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8">
        <SkeletonShimmer>
          <div className="xl:col-span-1 2xl:col-span-2">
            <ChartCardSkeleton />
          </div>
          <div className="xl:col-span-1 2xl:col-span-2">
            <ChartCardSkeleton />
          </div>
        </SkeletonShimmer>
      </div>

      {/* Enhanced charts skeleton */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8">
        <SkeletonShimmer>
          <div className="xl:col-span-1 2xl:col-span-2">
            <ChartCardSkeleton className="h-96" />
          </div>
          <div className="xl:col-span-1 2xl:col-span-2">
            <ChartCardSkeleton className="h-96" />
          </div>
        </SkeletonShimmer>
      </div>

      {/* Recent orders and top products skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 px-4 sm:px-6 md:px-8">
        <SkeletonShimmer>
          <ListCardSkeleton title="Recent Orders" />
        </SkeletonShimmer>
        <SkeletonShimmer>
          <ListCardSkeleton title="Top Products" />
        </SkeletonShimmer>
      </div>
    </div>
  );
}
