'use client';

import React from 'react';
import { SkeletonShimmer, SkeletonLine, SkeletonRect, SkeletonCard } from './SkeletonBase';

// Form field skeleton
function FormFieldSkeleton({ type = 'input' }: { type?: 'input' | 'textarea' | 'select' | 'checkbox' | 'file' }) {
  return (
    <div className="space-y-2">
      <SkeletonLine className="h-4 w-24" />
      {type === 'textarea' ? (
        <SkeletonRect className="h-24 w-full" />
      ) : type === 'select' ? (
        <SkeletonRect className="h-10 w-full" />
      ) : type === 'checkbox' ? (
        <div className="flex items-center space-x-2">
          <SkeletonRect className="h-4 w-4" />
          <SkeletonLine className="h-4 w-32" />
        </div>
      ) : type === 'file' ? (
        <SkeletonRect className="h-32 w-full border-2 border-dashed border-gray-300" />
      ) : (
        <SkeletonRect className="h-10 w-full" />
      )}
    </div>
  );
}

// Form section skeleton
function FormSectionSkeleton({ title, fields }: { title?: string; fields: Array<{ type?: string; width?: string }> }) {
  return (
    <SkeletonCard className="p-6">
      {title && (
        <div className="mb-6">
          <SkeletonLine className="h-6 w-48 mb-2" />
          <SkeletonLine className="h-4 w-96" />
        </div>
      )}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        {fields.map((field, index) => (
          <div key={index} className={field.width || 'col-span-1'}>
            <FormFieldSkeleton type={field.type as any} />
          </div>
        ))}
      </div>
    </SkeletonCard>
  );
}

// Page header skeleton for forms
function FormPageHeaderSkeleton() {
  return (
    <div className="md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8">
      <div className="flex-1 min-w-0">
        <SkeletonShimmer>
          <SkeletonLine className="h-8 w-64 mb-2" />
          <SkeletonLine className="h-4 w-96" />
        </SkeletonShimmer>
      </div>
      <div className="mt-4 md:mt-0 md:ml-4 flex space-x-3">
        <SkeletonShimmer>
          <SkeletonRect className="h-10 w-20" />
          <SkeletonRect className="h-10 w-24" />
        </SkeletonShimmer>
      </div>
    </div>
  );
}

// Form actions skeleton
function FormActionsSkeleton() {
  return (
    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <SkeletonShimmer>
        <SkeletonRect className="h-10 w-20" />
        <SkeletonRect className="h-10 w-32" />
      </SkeletonShimmer>
    </div>
  );
}

// Main form skeleton component
interface FormSkeletonProps {
  sections?: Array<{
    title?: string;
    fields: Array<{ type?: string; width?: string }>;
  }>;
  showHeader?: boolean;
  showActions?: boolean;
}

export default function FormSkeleton({
  sections = [
    {
      title: 'Basic Information',
      fields: [
        { type: 'input', width: 'col-span-2' },
        { type: 'input' },
        { type: 'select' },
        { type: 'textarea', width: 'col-span-2' }
      ]
    }
  ],
  showHeader = true,
  showActions = true
}: FormSkeletonProps) {
  return (
    <div className="space-y-6">
      {/* Page header */}
      {showHeader && <FormPageHeaderSkeleton />}

      {/* Form sections */}
      <div className="space-y-6 px-4 sm:px-6 md:px-8">
        <SkeletonShimmer>
          {sections.map((section, index) => (
            <FormSectionSkeleton
              key={index}
              title={section.title}
              fields={section.fields}
            />
          ))}
        </SkeletonShimmer>

        {/* Form actions */}
        {showActions && (
          <SkeletonCard className="p-6">
            <FormActionsSkeleton />
          </SkeletonCard>
        )}
      </div>
    </div>
  );
}

// Specialized form skeletons for different admin screens
export function ProductFormSkeleton() {
  return (
    <FormSkeleton
      sections={[
        {
          title: 'Basic Information',
          fields: [
            { type: 'input', width: 'col-span-2' },
            { type: 'input' },
            { type: 'input' },
            { type: 'textarea', width: 'col-span-2' }
          ]
        },
        {
          title: 'Pricing & Inventory',
          fields: [
            { type: 'input' },
            { type: 'input' },
            { type: 'input' },
            { type: 'select' }
          ]
        },
        {
          title: 'Media',
          fields: [
            { type: 'file', width: 'col-span-2' }
          ]
        },
        {
          title: 'SEO & Visibility',
          fields: [
            { type: 'select' },
            { type: 'checkbox' },
            { type: 'input', width: 'col-span-2' }
          ]
        }
      ]}
    />
  );
}

export function CouponFormSkeleton() {
  return (
    <FormSkeleton
      sections={[
        {
          title: 'Coupon Details',
          fields: [
            { type: 'input' },
            { type: 'input' },
            { type: 'select' },
            { type: 'input' },
            { type: 'textarea', width: 'col-span-2' }
          ]
        },
        {
          title: 'Usage Limits',
          fields: [
            { type: 'input' },
            { type: 'input' },
            { type: 'input' },
            { type: 'input' }
          ]
        },
        {
          title: 'Validity',
          fields: [
            { type: 'input' },
            { type: 'input' },
            { type: 'checkbox', width: 'col-span-2' }
          ]
        }
      ]}
    />
  );
}

export function CategoryFormSkeleton() {
  return (
    <FormSkeleton
      sections={[
        {
          title: 'Category Information',
          fields: [
            { type: 'input', width: 'col-span-2' },
            { type: 'select' },
            { type: 'input' },
            { type: 'textarea', width: 'col-span-2' },
            { type: 'file', width: 'col-span-2' }
          ]
        }
      ]}
    />
  );
}
