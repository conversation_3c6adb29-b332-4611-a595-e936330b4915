'use client';

import React from 'react';
import { SkeletonShimmer, SkeletonLine, SkeletonRect, SkeletonCard, SkeletonCircle, SkeletonText } from './SkeletonBase';

// Detail page header skeleton
function DetailHeaderSkeleton() {
  return (
    <div className="md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8">
      <div className="flex-1 min-w-0">
        <SkeletonShimmer>
          <SkeletonLine className="h-8 w-64 mb-2" />
          <SkeletonLine className="h-4 w-96" />
        </SkeletonShimmer>
      </div>
      <div className="mt-4 md:mt-0 md:ml-4 flex space-x-3">
        <SkeletonShimmer>
          <SkeletonRect className="h-10 w-20" />
          <SkeletonRect className="h-10 w-24" />
          <SkeletonRect className="h-10 w-20" />
        </SkeletonShimmer>
      </div>
    </div>
  );
}

// Info section skeleton
function InfoSectionSkeleton({ title, rows = 4 }: { title?: string; rows?: number }) {
  return (
    <SkeletonCard className="p-6">
      {title && (
        <div className="mb-4">
          <SkeletonLine className="h-5 w-32" />
        </div>
      )}
      <div className="space-y-4">
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="flex justify-between">
            <SkeletonLine className="h-4 w-24" />
            <SkeletonLine className="h-4 w-32" />
          </div>
        ))}
      </div>
    </SkeletonCard>
  );
}

// Image gallery skeleton
function ImageGallerySkeleton() {
  return (
    <SkeletonCard className="p-6">
      <SkeletonLine className="h-5 w-24 mb-4" />
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <SkeletonRect key={index} className="aspect-square" />
        ))}
      </div>
    </SkeletonCard>
  );
}

// Activity timeline skeleton
function ActivityTimelineSkeleton() {
  return (
    <SkeletonCard className="p-6">
      <SkeletonLine className="h-5 w-32 mb-4" />
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-start space-x-3">
            <SkeletonCircle className="h-8 w-8 mt-1" />
            <div className="flex-1">
              <SkeletonLine className="h-4 w-48 mb-1" />
              <SkeletonLine className="h-3 w-24" />
            </div>
          </div>
        ))}
      </div>
    </SkeletonCard>
  );
}

// Related items skeleton
function RelatedItemsSkeleton({ title = 'Related Items' }: { title?: string }) {
  return (
    <SkeletonCard className="p-6">
      <SkeletonLine className="h-5 w-32 mb-4" />
      <div className="space-y-3">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-3">
            <SkeletonRect className="h-12 w-12" />
            <div className="flex-1">
              <SkeletonLine className="h-4 w-32 mb-1" />
              <SkeletonLine className="h-3 w-20" />
            </div>
            <SkeletonLine className="h-4 w-16" />
          </div>
        ))}
      </div>
    </SkeletonCard>
  );
}

// Main detail view skeleton
interface DetailViewSkeletonProps {
  layout?: 'single' | 'sidebar' | 'tabs';
  showImages?: boolean;
  showActivity?: boolean;
  showRelated?: boolean;
}

export default function DetailViewSkeleton({
  layout = 'sidebar',
  showImages = true,
  showActivity = true,
  showRelated = true
}: DetailViewSkeletonProps) {
  if (layout === 'single') {
    return (
      <div className="space-y-6">
        <DetailHeaderSkeleton />
        <div className="px-4 sm:px-6 md:px-8">
          <SkeletonShimmer>
            <InfoSectionSkeleton rows={8} />
            {showImages && <ImageGallerySkeleton />}
            {showActivity && <ActivityTimelineSkeleton />}
          </SkeletonShimmer>
        </div>
      </div>
    );
  }

  if (layout === 'tabs') {
    return (
      <div className="space-y-6">
        <DetailHeaderSkeleton />
        <div className="px-4 sm:px-6 md:px-8">
          {/* Tab navigation skeleton */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {Array.from({ length: 4 }).map((_, index) => (
                <SkeletonLine key={index} className="h-4 w-20" />
              ))}
            </nav>
          </div>
          
          <SkeletonShimmer>
            <InfoSectionSkeleton rows={6} />
          </SkeletonShimmer>
        </div>
      </div>
    );
  }

  // Default sidebar layout
  return (
    <div className="space-y-6">
      <DetailHeaderSkeleton />
      <div className="px-4 sm:px-6 md:px-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-6">
            <SkeletonShimmer>
              <InfoSectionSkeleton title="Basic Information" rows={6} />
              {showImages && <ImageGallerySkeleton />}
              {showActivity && <ActivityTimelineSkeleton />}
            </SkeletonShimmer>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <SkeletonShimmer>
              <InfoSectionSkeleton title="Summary" rows={4} />
              {showRelated && <RelatedItemsSkeleton />}
            </SkeletonShimmer>
          </div>
        </div>
      </div>
    </div>
  );
}

// Specialized detail view skeletons
export function ProductDetailSkeleton() {
  return (
    <DetailViewSkeleton
      layout="sidebar"
      showImages={true}
      showActivity={true}
      showRelated={true}
    />
  );
}

export function OrderDetailSkeleton() {
  return (
    <DetailViewSkeleton
      layout="sidebar"
      showImages={false}
      showActivity={true}
      showRelated={false}
    />
  );
}

export function CustomerDetailSkeleton() {
  return (
    <DetailViewSkeleton
      layout="tabs"
      showImages={false}
      showActivity={true}
      showRelated={true}
    />
  );
}
