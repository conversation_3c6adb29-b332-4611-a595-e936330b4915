/**
 * Mock data for MSW handlers
 * This provides realistic test data for all admin API endpoints
 */

export const mockProducts = [
  {
    id: "prod_01",
    title: "Premium Wireless Headphones",
    description: "High-quality wireless headphones with noise cancellation",
    handle: "premium-wireless-headphones",
    status: "published",
    thumbnail: "/images/products/headphones.jpg",
    images: ["/images/products/headphones.jpg", "/images/products/headphones-2.jpg"],
    collection_id: "coll_01",
    collection: {
      id: "coll_01",
      title: "Electronics",
      handle: "electronics"
    },
    tags: [
      { id: "tag_01", value: "electronics" },
      { id: "tag_02", value: "audio" },
      { id: "tag_03", value: "wireless" }
    ],
    variants: [
      {
        id: "var_01",
        title: "Default Variant",
        sku: "PWH-001",
        inventory_quantity: 50,
        prices: [{ id: "price_01", currency_code: "INR", amount: 15999 }]
      }
    ],
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z"
  },
  {
    id: "prod_02",
    title: "Smart Fitness Watch",
    description: "Advanced fitness tracking with heart rate monitoring",
    handle: "smart-fitness-watch",
    status: "published",
    thumbnail: "/images/products/watch.jpg",
    images: ["/images/products/watch.jpg"],
    collection_id: "coll_01",
    collection: {
      id: "coll_01",
      title: "Electronics",
      handle: "electronics"
    },
    tags: [
      { id: "tag_04", value: "fitness" },
      { id: "tag_05", value: "wearable" }
    ],
    variants: [
      {
        id: "var_02",
        title: "Default Variant",
        sku: "SFW-001",
        inventory_quantity: 25,
        prices: [{ id: "price_02", currency_code: "INR", amount: 12999 }]
      }
    ],
    created_at: "2024-01-10T09:00:00Z",
    updated_at: "2024-01-18T16:45:00Z"
  },
  {
    id: "prod_03",
    title: "Organic Cotton T-Shirt",
    description: "Comfortable organic cotton t-shirt in multiple colors",
    handle: "organic-cotton-tshirt",
    status: "published",
    thumbnail: "/images/products/tshirt.jpg",
    images: ["/images/products/tshirt.jpg", "/images/products/tshirt-blue.jpg"],
    collection_id: "coll_02",
    collection: {
      id: "coll_02",
      title: "Clothing",
      handle: "clothing"
    },
    tags: [
      { id: "tag_06", value: "clothing" },
      { id: "tag_07", value: "organic" },
      { id: "tag_08", value: "cotton" }
    ],
    variants: [
      {
        id: "var_03",
        title: "Medium - White",
        sku: "OCT-M-WHT",
        inventory_quantity: 100,
        prices: [{ id: "price_03", currency_code: "INR", amount: 899 }]
      }
    ],
    created_at: "2024-01-05T11:30:00Z",
    updated_at: "2024-01-12T13:20:00Z"
  },
  {
    id: "prod_04",
    title: "Eco-Friendly Water Bottle",
    description: "Sustainable stainless steel water bottle",
    handle: "eco-friendly-water-bottle",
    status: "draft",
    thumbnail: "/images/products/bottle.jpg",
    images: ["/images/products/bottle.jpg"],
    collection_id: "coll_03",
    collection: {
      id: "coll_03",
      title: "Lifestyle",
      handle: "lifestyle"
    },
    tags: [
      { id: "tag_09", value: "eco-friendly" },
      { id: "tag_10", value: "sustainable" }
    ],
    variants: [
      {
        id: "var_04",
        title: "500ml",
        sku: "EWB-500",
        inventory_quantity: 75,
        prices: [{ id: "price_04", currency_code: "INR", amount: 1299 }]
      }
    ],
    created_at: "2024-01-08T14:15:00Z",
    updated_at: "2024-01-16T10:30:00Z"
  },
  {
    id: "prod_05",
    title: "Bluetooth Speaker",
    description: "Portable Bluetooth speaker with excellent sound quality",
    handle: "bluetooth-speaker",
    status: "published",
    thumbnail: "/images/products/speaker.jpg",
    images: ["/images/products/speaker.jpg"],
    collection_id: "coll_01",
    collection: {
      id: "coll_01",
      title: "Electronics",
      handle: "electronics"
    },
    tags: [
      { id: "tag_11", value: "audio" },
      { id: "tag_12", value: "portable" }
    ],
    variants: [
      {
        id: "var_05",
        title: "Default Variant",
        sku: "BTS-001",
        inventory_quantity: 40,
        prices: [{ id: "price_05", currency_code: "INR", amount: 3999 }]
      }
    ],
    created_at: "2024-01-12T16:00:00Z",
    updated_at: "2024-01-19T11:15:00Z"
  }
];

export const mockOrders = [
  {
    id: "order_01",
    display_id: 10001,
    status: "pending",
    fulfillment_status: "not_fulfilled",
    payment_status: "awaiting",
    total: 15999,
    currency_code: "INR",
    customer: {
      id: "cust_01",
      first_name: "Rajesh",
      last_name: "Kumar",
      email: "<EMAIL>"
    },
    items: [
      {
        id: "item_01",
        title: "Premium Wireless Headphones",
        quantity: 1,
        unit_price: 15999,
        total: 15999
      }
    ],
    shipping_address: {
      first_name: "Rajesh",
      last_name: "Kumar",
      address_1: "123 MG Road",
      city: "Bangalore",
      postal_code: "560001",
      country_code: "IN"
    },
    created_at: "2024-01-20T10:30:00Z",
    updated_at: "2024-01-20T10:30:00Z"
  },
  {
    id: "order_02",
    display_id: 10002,
    status: "completed",
    fulfillment_status: "fulfilled",
    payment_status: "captured",
    total: 13898,
    currency_code: "INR",
    customer: {
      id: "cust_02",
      first_name: "Priya",
      last_name: "Sharma",
      email: "<EMAIL>"
    },
    items: [
      {
        id: "item_02",
        title: "Smart Fitness Watch",
        quantity: 1,
        unit_price: 12999,
        total: 12999
      },
      {
        id: "item_03",
        title: "Organic Cotton T-Shirt",
        quantity: 1,
        unit_price: 899,
        total: 899
      }
    ],
    shipping_address: {
      first_name: "Priya",
      last_name: "Sharma",
      address_1: "456 Park Street",
      city: "Mumbai",
      postal_code: "400001",
      country_code: "IN"
    },
    created_at: "2024-01-18T14:20:00Z",
    updated_at: "2024-01-19T16:45:00Z"
  },
  {
    id: "order_03",
    display_id: 10003,
    status: "pending",
    fulfillment_status: "partially_fulfilled",
    payment_status: "captured",
    total: 5298,
    currency_code: "INR",
    customer: {
      id: "cust_03",
      first_name: "Amit",
      last_name: "Patel",
      email: "<EMAIL>"
    },
    items: [
      {
        id: "item_04",
        title: "Bluetooth Speaker",
        quantity: 1,
        unit_price: 3999,
        total: 3999
      },
      {
        id: "item_05",
        title: "Eco-Friendly Water Bottle",
        quantity: 1,
        unit_price: 1299,
        total: 1299
      }
    ],
    shipping_address: {
      first_name: "Amit",
      last_name: "Patel",
      address_1: "789 Commercial Street",
      city: "Delhi",
      postal_code: "110001",
      country_code: "IN"
    },
    created_at: "2024-01-17T09:15:00Z",
    updated_at: "2024-01-18T12:30:00Z"
  }
];

export const mockCustomers = [
  {
    id: "cust_01",
    first_name: "Rajesh",
    last_name: "Kumar",
    email: "<EMAIL>",
    phone: "+91-**********",
    has_account: true,
    orders_count: 3,
    total_spent: 45997,
    created_at: "2024-01-01T08:00:00Z",
    updated_at: "2024-01-20T10:30:00Z"
  },
  {
    id: "cust_02",
    first_name: "Priya",
    last_name: "Sharma",
    email: "<EMAIL>",
    phone: "+91-**********",
    has_account: true,
    orders_count: 2,
    total_spent: 25898,
    created_at: "2024-01-05T12:30:00Z",
    updated_at: "2024-01-19T16:45:00Z"
  },
  {
    id: "cust_03",
    first_name: "Amit",
    last_name: "Patel",
    email: "<EMAIL>",
    phone: "+91-**********",
    has_account: false,
    orders_count: 1,
    total_spent: 5298,
    created_at: "2024-01-10T15:45:00Z",
    updated_at: "2024-01-18T12:30:00Z"
  },
  {
    id: "cust_04",
    first_name: "Sneha",
    last_name: "Reddy",
    email: "<EMAIL>",
    phone: "+91-**********",
    has_account: true,
    orders_count: 5,
    total_spent: 67890,
    created_at: "2023-12-15T10:20:00Z",
    updated_at: "2024-01-15T14:10:00Z"
  },
  {
    id: "cust_05",
    first_name: "Vikram",
    last_name: "Singh",
    email: "<EMAIL>",
    phone: "+91-**********",
    has_account: true,
    orders_count: 1,
    total_spent: 12999,
    created_at: "2024-01-12T11:00:00Z",
    updated_at: "2024-01-12T11:00:00Z"
  }
];

export const mockCollections = [
  {
    id: "coll_01",
    title: "Electronics",
    handle: "electronics",
    description: "Latest electronic gadgets and accessories",
    products_count: 3,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T10:30:00Z"
  },
  {
    id: "coll_02",
    title: "Clothing",
    handle: "clothing",
    description: "Comfortable and stylish clothing for all occasions",
    products_count: 1,
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-10T14:20:00Z"
  },
  {
    id: "coll_03",
    title: "Lifestyle",
    handle: "lifestyle",
    description: "Products for a better lifestyle",
    products_count: 1,
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-08T16:45:00Z"
  },
  {
    id: "coll_04",
    title: "Home & Garden",
    handle: "home-garden",
    description: "Everything for your home and garden",
    products_count: 0,
    created_at: "2024-01-04T00:00:00Z",
    updated_at: "2024-01-04T00:00:00Z"
  }
];
