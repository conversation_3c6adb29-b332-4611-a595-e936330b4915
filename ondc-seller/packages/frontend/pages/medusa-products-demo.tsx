/**
 * Demo page showing how to use Medusa products in your frontend
 */

import React, { useState } from 'react';
import Head from 'next/head';
import MedusaProductList from '../components/MedusaProductList';
import { useMedusaProduct } from '../hooks/useMedusaProducts';
import { getProductPrice, getProductImage } from '../lib/medusa-api';

const ProductModal: React.FC<{
  product: any;
  isOpen: boolean;
  onClose: () => void;
}> = ({ product, isOpen, onClose }) => {
  if (!isOpen || !product) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-2xl font-bold text-gray-900">{product.title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="mb-4">
            <img
              src={getProductImage(product)}
              alt={product.title}
              className="w-full h-64 object-cover rounded-lg"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/images/placeholder.svg';
              }}
            />
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600">{product.description || 'No description available'}</p>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Price</h3>
                <p className="text-2xl font-bold text-blue-600">{getProductPrice(product)}</p>
              </div>
              <div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  product.status === 'published' 
                    ? 'bg-green-100 text-green-800'
                    : product.status === 'draft'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {product.status}
                </span>
              </div>
            </div>

            {product.variants && product.variants.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Variants</h3>
                <div className="space-y-2">
                  {product.variants.map((variant: any) => (
                    <div key={variant.id} className="border rounded-lg p-3">
                      <p className="font-medium">{variant.title}</p>
                      {variant.prices && variant.prices.length > 0 && (
                        <p className="text-sm text-gray-600">
                          {variant.prices.map((price: any) => 
                            `${price.currency_code.toUpperCase()}: ${(price.amount / 100).toFixed(2)}`
                          ).join(', ')}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {product.tags && product.tags.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag: any) => (
                    <span
                      key={tag.id}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                    >
                      {tag.value}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="text-sm text-gray-500">
              <p>Product ID: {product.id}</p>
              <p>Handle: {product.handle}</p>
              <p>Created: {new Date(product.created_at).toLocaleDateString()}</p>
              <p>Updated: {new Date(product.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const MedusaProductsDemo: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);

  const handleProductClick = (product: any) => {
    setSelectedProduct(product);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedProduct(null);
  };

  return (
    <>
      <Head>
        <title>Medusa Products Demo - ONDC Seller</title>
        <meta name="description" content="Demo page showing Medusa products integration" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <h1 className="text-3xl font-bold text-gray-900">Medusa Products Demo</h1>
              <p className="mt-2 text-gray-600">
                This page demonstrates how to fetch and display products from your Medusa backend.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Connection Status */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">Medusa Backend Connection</h3>
                <div className="text-sm text-blue-700 mt-1">
                  <p>Backend URL: {process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000'}</p>
                  <p>Publishable Key: {process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY ? 'Configured' : 'Not configured'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">How to Use Medusa Products</h2>
            <div className="space-y-4 text-gray-600">
              <div>
                <h3 className="font-medium text-gray-900">1. Import the hooks:</h3>
                <pre className="mt-1 bg-gray-100 p-2 rounded text-sm overflow-x-auto">
{`import { useMedusaProducts, useMedusaProduct } from '../hooks/useMedusaProducts';`}
                </pre>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">2. Use in your component:</h3>
                <pre className="mt-1 bg-gray-100 p-2 rounded text-sm overflow-x-auto">
{`const { products, loading, error } = useMedusaProducts({
  limit: 12,
  autoFetch: true
});`}
                </pre>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">3. Display the products:</h3>
                <pre className="mt-1 bg-gray-100 p-2 rounded text-sm overflow-x-auto">
{`{products.map(product => (
  <div key={product.id}>
    <h3>{product.title}</h3>
    <p>{getProductPrice(product)}</p>
  </div>
))}`}
                </pre>
              </div>
            </div>
          </div>

          {/* Products List */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Products from Medusa Backend</h2>
            <MedusaProductList
              limit={12}
              showSearch={true}
              onProductClick={handleProductClick}
            />
          </div>
        </div>

        {/* Product Modal */}
        <ProductModal
          product={selectedProduct}
          isOpen={showModal}
          onClose={handleCloseModal}
        />
      </div>
    </>
  );
};

export default MedusaProductsDemo;
