/**
 * Real-time test page for Medusa-MCP integration
 * This page tests the actual connection to medusa-mcp server and Medusa backend
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';

const TestMedusaMCP: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    medusaBackend: 'unknown' | 'connected' | 'failed';
    medusaMcp: 'unknown' | 'connected' | 'failed';
  }>({
    medusaBackend: 'unknown',
    medusaMcp: 'unknown',
  });

  // State for manual testing
  const [products, setProducts] = useState<any[]>([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [productsError, setProductsError] = useState<string | null>(null);

  // Test individual API calls
  const runAPITests = async () => {
    setIsLoading(true);
    const results: any[] = [];

    // Test 1: Health Check
    try {
      const healthResponse = await apiClient.getHealth();
      results.push({
        test: 'Health Check',
        status: 'success',
        data: healthResponse.data,
        timestamp: new Date().toISOString(),
      });
      setConnectionStatus(prev => ({ ...prev, medusaBackend: 'connected' }));
    } catch (error) {
      results.push({
        test: 'Health Check',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      setConnectionStatus(prev => ({ ...prev, medusaBackend: 'failed' }));
    }

    // Test 2: Version Check
    try {
      const versionResponse = await apiClient.getVersion();
      results.push({
        test: 'Version Check',
        status: 'success',
        data: versionResponse.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      results.push({
        test: 'Version Check',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    // Test 3: Products API
    try {
      const productsResponse = await apiClient.getProducts({ limit: 3 });
      results.push({
        test: 'Products API',
        status: 'success',
        data: {
          count: productsResponse.data?.products?.length || 0,
          products: productsResponse.data?.products?.slice(0, 2) || [],
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      results.push({
        test: 'Products API',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    // Test 4: Featured Products
    try {
      const featuredResponse = await apiClient.getFeaturedProducts(3);
      results.push({
        test: 'Featured Products',
        status: 'success',
        data: {
          count: featuredResponse.data?.products?.length || 0,
          products: featuredResponse.data?.products?.slice(0, 2) || [],
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      results.push({
        test: 'Featured Products',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    // Test 5: Direct Medusa Backend Call
    try {
      const response = await fetch('http://localhost:9000/health');
      const data = await response.text();
      results.push({
        test: 'Direct Medusa Backend',
        status: response.ok ? 'success' : 'error',
        data: { status: response.status, response: data },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      results.push({
        test: 'Direct Medusa Backend',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  // Auto-run tests on component mount
  useEffect(() => {
    runAPITests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'connected':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'connected':
        return '✅';
      case 'error':
      case 'failed':
        return '❌';
      default:
        return '⏳';
    }
  };

  return (
    <>
      <Head>
        <title>Medusa-MCP Integration Test - ONDC Seller</title>
        <meta name="description" content="Real-time testing of Medusa-MCP integration" />
      </Head>

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Medusa-MCP Integration Test</h1>
            <p className="text-gray-600 mb-6">
              Real-time testing of the connection between frontend, medusa-mcp server, and Medusa
              backend.
            </p>

            {/* Connection Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="flex items-center space-x-3">
                <span className={getStatusColor(connectionStatus.medusaBackend)}>
                  {getStatusIcon(connectionStatus.medusaBackend)}
                </span>
                <span className="font-medium">Medusa Backend:</span>
                <span className={getStatusColor(connectionStatus.medusaBackend)}>
                  {connectionStatus.medusaBackend}
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <span className={getStatusColor(connectionStatus.medusaMcp)}>
                  {getStatusIcon(connectionStatus.medusaMcp)}
                </span>
                <span className="font-medium">Medusa-MCP:</span>
                <span className={getStatusColor(connectionStatus.medusaMcp)}>
                  {connectionStatus.medusaMcp}
                </span>
              </div>
            </div>

            <button
              onClick={runAPITests}
              disabled={isLoading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Running Tests...' : 'Run Tests Again'}
            </button>
          </div>

          {/* Test Results */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">API Test Results</h2>

            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click "Run Tests" to start.</p>
            ) : (
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{result.test}</h3>
                      <span
                        className={`px-3 py-1 rounded-full text-sm ${getStatusColor(result.status)}`}
                      >
                        {getStatusIcon(result.status)} {result.status}
                      </span>
                    </div>

                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-3 mb-2">
                        <p className="text-red-800 text-sm">{result.error}</p>
                      </div>
                    )}

                    {result.data && (
                      <div className="bg-gray-50 rounded p-3 mb-2">
                        <pre className="text-sm text-gray-700 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}

                    <p className="text-xs text-gray-500">
                      Tested at: {new Date(result.timestamp).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Hook-based Data Display */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Products from useProducts Hook */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Products (useProducts Hook)
              </h2>

              {productsLoading && (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Loading products...</span>
                </div>
              )}

              {productsError && (
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-red-800">Error: {productsError}</p>
                </div>
              )}

              {products && products.length > 0 && (
                <div className="space-y-3">
                  {products.slice(0, 3).map(product => (
                    <div key={product.id} className="border rounded p-3">
                      <h3 className="font-medium">{product.title}</h3>
                      <p className="text-sm text-gray-600">
                        {product.description?.substring(0, 100)}...
                      </p>
                      <p className="text-xs text-gray-500">Status: {product.status}</p>
                    </div>
                  ))}
                  <p className="text-sm text-gray-500">Showing 3 of {products.length} products</p>
                </div>
              )}

              {products && products.length === 0 && (
                <p className="text-gray-500">No products found</p>
              )}
            </div>

            {/* Featured Products */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Featured Products (useFeaturedProducts Hook)
              </h2>

              {featuredLoading && (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Loading featured products...</span>
                </div>
              )}

              {featuredError && (
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-red-800">Error: {featuredError}</p>
                </div>
              )}

              {featuredProducts && featuredProducts.length > 0 && (
                <div className="space-y-3">
                  {featuredProducts.map(product => (
                    <div key={product.id} className="border rounded p-3">
                      <h3 className="font-medium">{product.title}</h3>
                      <p className="text-sm text-gray-600">
                        {product.description?.substring(0, 100)}...
                      </p>
                      <p className="text-xs text-gray-500">Status: {product.status}</p>
                    </div>
                  ))}
                </div>
              )}

              {featuredProducts && featuredProducts.length === 0 && (
                <p className="text-gray-500">No featured products found</p>
              )}
            </div>
          </div>

          {/* System Health */}
          <div className="bg-white shadow rounded-lg p-6 mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              System Health (useSystemHealth Hook)
            </h2>

            {healthLoading && (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Checking system health...</span>
              </div>
            )}

            {healthError && (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <p className="text-red-800">Error: {healthError}</p>
              </div>
            )}

            {health && (
              <div className="bg-green-50 border border-green-200 rounded p-3">
                <pre className="text-sm text-green-800">{JSON.stringify(health, null, 2)}</pre>
              </div>
            )}
          </div>

          {/* Environment Info */}
          <div className="bg-white shadow rounded-lg p-6 mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Environment Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Medusa API URL:</strong>{' '}
                {process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000'}
              </div>
              <div>
                <strong>Publishable Key:</strong>{' '}
                {process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY ? 'Configured' : 'Not configured'}
              </div>
              <div>
                <strong>Frontend URL:</strong> http://localhost:3000
              </div>
              <div>
                <strong>Test Page:</strong> /test-medusa-mcp
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestMedusaMCP;
