/**
 * Real-time test page for Medusa integration
 * Tests direct connection to Medusa backend without complex dependencies
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';

const TestMedusaReal: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    medusaBackend: 'unknown' | 'connected' | 'failed';
  }>({
    medusaBackend: 'unknown'
  });

  // Run API tests
  const runAPITests = async () => {
    setIsLoading(true);
    const results: any[] = [];

    console.log('🚀 Starting real-time Medusa API tests...');

    // Test 1: Health Check
    try {
      console.log('Testing health endpoint...');
      const response = await fetch('http://localhost:9000/health');
      const data = await response.text();
      
      const result = {
        test: 'Health Check',
        status: response.ok ? 'success' : 'error',
        data: { status: response.status, response: data },
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      console.log('Health check result:', result);
      setConnectionStatus(prev => ({ ...prev, medusaBackend: response.ok ? 'connected' : 'failed' }));
    } catch (error: any) {
      const result = {
        test: 'Health Check',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      results.push(result);
      console.error('Health check error:', error);
      setConnectionStatus(prev => ({ ...prev, medusaBackend: 'failed' }));
    }

    // Test 2: Version Check (Custom endpoint)
    try {
      console.log('Testing version endpoint...');
      const response = await fetch('http://localhost:9000/version');
      let data;
      try {
        data = await response.json();
      } catch {
        data = await response.text();
      }
      
      const result = {
        test: 'Version Check',
        status: response.ok ? 'success' : 'error',
        data: { status: response.status, response: data },
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      console.log('Version check result:', result);
    } catch (error: any) {
      const result = {
        test: 'Version Check',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      results.push(result);
      console.error('Version check error:', error);
    }

    // Test 3: Store Products API (with publishable key)
    try {
      console.log('Testing store products API...');
      const publishableKey = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || 'pk_01HQD0GMQXVK5XJ0XS0ZNPJ8T5';
      const response = await fetch('http://localhost:9000/store/products?limit=3', {
        headers: {
          'x-publishable-api-key': publishableKey,
          'Content-Type': 'application/json'
        }
      });
      
      let data;
      try {
        data = await response.json();
      } catch {
        data = await response.text();
      }
      
      const result = {
        test: 'Store Products API',
        status: response.ok ? 'success' : 'error',
        data: { 
          status: response.status, 
          response: data,
          count: data?.products?.length || 0,
          publishableKey: publishableKey ? 'present' : 'missing'
        },
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      console.log('Store products result:', result);
    } catch (error: any) {
      const result = {
        test: 'Store Products API',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      results.push(result);
      console.error('Store products error:', error);
    }

    // Test 4: Featured Products (Custom endpoint)
    try {
      console.log('Testing featured products endpoint...');
      const response = await fetch('http://localhost:9000/products/featured?limit=3');
      let data;
      try {
        data = await response.json();
      } catch {
        data = await response.text();
      }
      
      const result = {
        test: 'Featured Products',
        status: response.ok ? 'success' : 'error',
        data: { 
          status: response.status, 
          response: data,
          count: data?.products?.length || 0
        },
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      console.log('Featured products result:', result);
    } catch (error: any) {
      const result = {
        test: 'Featured Products',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      results.push(result);
      console.error('Featured products error:', error);
    }

    // Test 5: Admin Products (without auth - should fail)
    try {
      console.log('Testing admin products API (should fail without auth)...');
      const response = await fetch('http://localhost:9000/admin/products?limit=3');
      let data;
      try {
        data = await response.json();
      } catch {
        data = await response.text();
      }
      
      const result = {
        test: 'Admin Products (No Auth)',
        status: response.status === 401 ? 'success' : 'error',
        data: { 
          status: response.status, 
          response: data,
          note: 'Expected 401 Unauthorized'
        },
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      console.log('Admin products result:', result);
    } catch (error: any) {
      const result = {
        test: 'Admin Products (No Auth)',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      results.push(result);
      console.error('Admin products error:', error);
    }

    setTestResults(results);
    setIsLoading(false);
    console.log('✅ All tests completed!');
  };

  // Auto-run tests on component mount
  useEffect(() => {
    runAPITests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'connected': return 'text-green-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'connected':
        return '✅';
      case 'error':
      case 'failed':
        return '❌';
      default:
        return '⏳';
    }
  };

  return (
    <>
      <Head>
        <title>Medusa Real-time Test - ONDC Seller</title>
        <meta name="description" content="Real-time testing of Medusa backend integration" />
      </Head>

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Medusa Backend Real-time Test
            </h1>
            <p className="text-gray-600 mb-6">
              Testing direct connection to Medusa backend API endpoints. Check browser console for detailed logs.
            </p>

            {/* Connection Status */}
            <div className="flex items-center space-x-3 mb-6">
              <span className={getStatusColor(connectionStatus.medusaBackend)}>
                {getStatusIcon(connectionStatus.medusaBackend)}
              </span>
              <span className="font-medium">Medusa Backend:</span>
              <span className={getStatusColor(connectionStatus.medusaBackend)}>
                {connectionStatus.medusaBackend}
              </span>
              <span className="text-sm text-gray-500">
                (http://localhost:9000)
              </span>
            </div>

            <button
              onClick={runAPITests}
              disabled={isLoading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Running Tests...' : 'Run Tests Again'}
            </button>
          </div>

          {/* Test Results */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">API Test Results</h2>
            
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Tests are running...</p>
            ) : (
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{result.test}</h3>
                      <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(result.status)}`}>
                        {getStatusIcon(result.status)} {result.status}
                      </span>
                    </div>
                    
                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-3 mb-2">
                        <p className="text-red-800 text-sm font-medium">Error:</p>
                        <p className="text-red-700 text-sm">{result.error}</p>
                      </div>
                    )}
                    
                    {result.data && (
                      <div className="bg-gray-50 rounded p-3 mb-2">
                        <p className="text-sm font-medium text-gray-700 mb-2">Response:</p>
                        <pre className="text-xs text-gray-600 overflow-x-auto max-h-40">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    <p className="text-xs text-gray-500">
                      Tested at: {new Date(result.timestamp).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Environment Info */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Environment Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Medusa API URL:</strong> {process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000'}
              </div>
              <div>
                <strong>Publishable Key:</strong> {process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY ? 'Configured' : 'Using default'}
              </div>
              <div>
                <strong>Frontend URL:</strong> http://localhost:3001
              </div>
              <div>
                <strong>Test Page:</strong> /test-medusa-real
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Testing Notes:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Health check should return "OK" (plain text)</li>
                <li>• Version endpoint may return 404 if custom routes aren't loaded</li>
                <li>• Store API requires publishable key</li>
                <li>• Admin API should return 401 without authentication</li>
                <li>• Check browser console for detailed logs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestMedusaReal;
