# ONDC Seller Frontend API Integration

## Overview

This document describes the comprehensive API integration system implemented for the ONDC Seller frontend application. The integration provides a robust, type-safe, and easy-to-use interface for communicating with backend services.

## 🏗️ Architecture

### Core Components

1. **API Client** (`lib/api-client.ts`)
   - Base HTTP client with authentication
   - Error handling and retry logic
   - Request/response transformation
   - Timeout and abort controller support

2. **API Services** (`lib/api/`)
   - Domain-specific service classes
   - Type-safe interfaces
   - CRUD operations
   - Business logic encapsulation

3. **React Hooks** (`lib/hooks/useAPI.ts`)
   - Easy-to-use React hooks
   - Loading state management
   - Error handling
   - Caching and optimization

4. **Shared Endpoints** (`packages/shared/api-endpoints.ts`)
   - Centralized endpoint configuration
   - Parameter validation
   - Bidirectional mapping (frontend ↔ backend)

## 📦 API Services

### Products API (`lib/api/products.ts`)

```typescript
// Get products with pagination and filtering
const { data, loading, error } = useProducts({
  limit: 20,
  status: 'published',
  category: 'electronics'
});

// Create a new product
const createProduct = useCreateProduct();
await createProduct({
  title: 'New Product',
  description: 'Product description',
  status: 'draft'
});

// Update existing product
const updateProduct = useUpdateProduct();
await updateProduct('product-id', {
  title: 'Updated Title',
  status: 'published'
});
```

### Orders API (`lib/api/orders.ts`)

```typescript
// Get orders with filtering
const { data: orders } = useOrders({
  status: 'pending',
  limit: 50
});

// Get single order
const { data: order } = useOrder('order-id');

// Update order status
const updateOrder = useUpdateOrder();
await updateOrder('order-id', {
  status: 'fulfilled'
});
```

### Customers API (`lib/api/customers.ts`)

```typescript
// Get customers
const { data: customers } = useCustomers({
  limit: 100,
  q: 'search query'
});

// Create customer
const createCustomer = useCreateCustomer();
await createCustomer({
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe'
});
```

### Collections API (`lib/api/collections.ts`)

```typescript
// Get collections
const { data: collections } = useCollections();

// Create collection
const createCollection = useCreateCollection();
await createCollection({
  title: 'Summer Collection',
  handle: 'summer-2024'
});
```

### Analytics API (`lib/api/analytics.ts`)

```typescript
// Get dashboard analytics
const { data: analytics } = useDashboardAnalytics('this_month');

// Get sales analytics
const { data: sales } = useSalesAnalytics({
  period: 'this_week',
  start_date: '2024-01-01',
  end_date: '2024-01-31'
});
```

## 🎣 React Hooks

### Basic Usage

```typescript
import { useProducts, useCreateProduct } from '@/lib/hooks/useAPI';

function ProductsPage() {
  // Fetch products with loading states
  const { data, loading, error, refresh } = useProducts({
    limit: 20,
    status: 'published'
  });

  // Create product mutation
  const createProduct = useCreateProduct();

  const handleCreate = async (productData) => {
    try {
      await createProduct(productData);
      refresh(); // Refresh the list
    } catch (error) {
      console.error('Failed to create product:', error);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {data?.data.map(product => (
        <div key={product.id}>{product.title}</div>
      ))}
    </div>
  );
}
```

### Advanced Features

```typescript
// Pagination support
const {
  data,
  loading,
  loadMore,
  hasMore,
  page,
  setPage
} = useProducts({ limit: 10 });

// Caching with TTL
const { data } = useDashboardAnalytics('this_month', {
  cache: true,
  cacheTTL: 300000 // 5 minutes
});

// Manual control
const { data, refetch, refresh } = useProducts({}, {
  immediate: false // Don't fetch on mount
});

// Fetch manually
useEffect(() => {
  refetch();
}, [someCondition]);
```

## 🔧 Configuration

### Environment Variables

```env
# API Configuration
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_your_key_here

# Development Mode
NODE_ENV=development
```

### API Client Setup

```typescript
import { APIClient, APIServiceFactory } from '@/lib/api';

// Configure global client
const client = new APIClient({
  baseURL: process.env.NEXT_PUBLIC_MEDUSA_API_URL,
  publishableKey: process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY,
  timeout: 30000
});

APIServiceFactory.setClient(client);
```

## 🔐 Authentication

### Admin Authentication

```typescript
// Set admin token
const client = new APIClient({
  adminToken: 'your-admin-token'
});

// API calls will automatically include admin headers
const products = await adminProductsAPI.getProducts();
```

### Customer Authentication

```typescript
// Set customer token
const client = new APIClient({
  publishableKey: 'your-publishable-key'
});

// Store API calls
const products = await productsAPI.getProducts();
```

## 📊 Data Types

### Product Interface

```typescript
interface Product {
  id: string;
  title: string;
  description?: string;
  handle?: string;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string;
  images?: string[];
  collection_id?: string;
  collection?: Collection;
  variants?: ProductVariant[];
  created_at: string;
  updated_at: string;
}
```

### Order Interface

```typescript
interface Order {
  id: string;
  display_id: number;
  status: 'pending' | 'completed' | 'archived' | 'canceled';
  fulfillment_status: 'not_fulfilled' | 'fulfilled' | 'shipped';
  payment_status: 'not_paid' | 'captured' | 'refunded';
  customer: Customer;
  items: OrderItem[];
  total: number;
  created_at: string;
}
```

## 🚨 Error Handling

### Global Error Handling

```typescript
try {
  const result = await adminProductsAPI.createProduct(data);
} catch (error) {
  if (error instanceof APIClientError) {
    console.error('API Error:', error.message);
    console.error('Status:', error.status);
    console.error('Endpoint:', error.endpoint);
  }
}
```

### Hook Error Handling

```typescript
const { data, loading, error } = useProducts();

if (error) {
  return (
    <div className="error">
      <h3>Failed to load products</h3>
      <p>{error}</p>
      <button onClick={refresh}>Retry</button>
    </div>
  );
}
```

## 🎯 Best Practices

### 1. Use Hooks for Components

```typescript
// ✅ Good - Use hooks in components
function ProductList() {
  const { data, loading } = useProducts();
  // ...
}

// ❌ Avoid - Direct API calls in components
function ProductList() {
  const [products, setProducts] = useState([]);
  useEffect(() => {
    adminProductsAPI.getProducts().then(setProducts);
  }, []);
  // ...
}
```

### 2. Handle Loading States

```typescript
// ✅ Good - Show loading states
const { data, loading, error } = useProducts();

if (loading) return <ProductsSkeleton />;
if (error) return <ErrorMessage error={error} />;
return <ProductsList products={data?.data} />;
```

### 3. Use Mutations for Write Operations

```typescript
// ✅ Good - Use mutation hooks
const createProduct = useCreateProduct();
const updateProduct = useUpdateProduct();

const handleSubmit = async (data) => {
  if (isEditing) {
    await updateProduct(productId, data);
  } else {
    await createProduct(data);
  }
  refresh();
};
```

### 4. Implement Optimistic Updates

```typescript
const { data, refresh } = useProducts();
const deleteProduct = useDeleteProduct();

const handleDelete = async (productId) => {
  // Optimistic update
  const optimisticData = data?.data.filter(p => p.id !== productId);
  
  try {
    await deleteProduct(productId);
    refresh(); // Confirm with server
  } catch (error) {
    refresh(); // Revert on error
    throw error;
  }
};
```

## 🔄 Migration Guide

### From Legacy API to New System

1. **Replace direct fetch calls**:
   ```typescript
   // Before
   const response = await fetch('/api/products');
   const products = await response.json();

   // After
   const { data: products } = useProducts();
   ```

2. **Update component structure**:
   ```typescript
   // Before
   function Products() {
     const [products, setProducts] = useState([]);
     const [loading, setLoading] = useState(true);
     
     useEffect(() => {
       fetchProducts().then(setProducts).finally(() => setLoading(false));
     }, []);
   }

   // After
   function Products() {
     const { data, loading, error } = useProducts();
   }
   ```

3. **Standardize error handling**:
   ```typescript
   // Before
   try {
     const response = await fetch('/api/products', { method: 'POST' });
     if (!response.ok) throw new Error('Failed');
   } catch (error) {
     alert('Error: ' + error.message);
   }

   // After
   const createProduct = useCreateProduct();
   try {
     await createProduct(data);
   } catch (error) {
     toast.error(error.message);
   }
   ```

## 📈 Performance Optimization

### Caching Strategy

```typescript
// Cache frequently accessed data
const { data } = useDashboardAnalytics('today', {
  cache: true,
  cacheTTL: 60000 // 1 minute
});

// Clear cache when needed
APICache.clear('analytics:*');
```

### Pagination

```typescript
// Efficient pagination
const {
  data,
  loadMore,
  hasMore,
  loading
} = useProducts({ limit: 20 });

// Load more on scroll
const handleScroll = () => {
  if (hasMore && !loading) {
    loadMore();
  }
};
```

## 🧪 Testing

### Mock API Responses

```typescript
// Test with mocked hooks
jest.mock('@/lib/hooks/useAPI', () => ({
  useProducts: () => ({
    data: { data: mockProducts },
    loading: false,
    error: null
  })
}));
```

### Integration Testing

```typescript
// Test actual API integration
test('should create product', async () => {
  const createProduct = useCreateProduct();
  const result = await createProduct({
    title: 'Test Product',
    status: 'draft'
  });
  
  expect(result.data.title).toBe('Test Product');
});
```

---

This API integration system provides a robust foundation for the ONDC Seller frontend, ensuring type safety, performance, and maintainability across all data operations.
