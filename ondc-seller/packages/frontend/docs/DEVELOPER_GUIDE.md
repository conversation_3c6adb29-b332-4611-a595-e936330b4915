# 🚀 ONDC Seller Platform - Developer Guide

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Quick Start](#quick-start)
3. [Frontend Development](#frontend-development)
4. [Backend Development](#backend-development)
5. [Authentication Setup](#authentication-setup)
6. [Database Configuration](#database-configuration)
7. [Real-time Notifications](#real-time-notifications)
8. [Development vs Production Modes](#development-vs-production-modes)
9. [Testing](#testing)
10. [Deployment](#deployment)

---

## 🎯 Project Overview

The ONDC Seller Platform is a comprehensive e-commerce solution built with:
- **Frontend**: Next.js 14, TypeScript, Material-UI, Tailwind CSS
- **Backend**: Node.js, Express, Prisma ORM
- **Database**: Self-hosted Supabase (PostgreSQL)
- **Authentication**: OneSSO (built over Keycloak)
- **Real-time**: Supabase Realtime / RabbitMQ
- **Charts**: Chart.js, React Chart.js 2

---

## ⚡ Quick Start

### Prerequisites
```bash
# Required versions
Node.js >= 18.0.0
npm >= 9.0.0
PostgreSQL >= 14.0
Docker (optional)
```

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ondc-seller

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local

# Start development servers
npm run dev:frontend  # Frontend on port 3000
npm run dev:backend   # Backend on port 8000
```

---

## 🎨 Frontend Development

### Project Structure
```
packages/frontend/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin interface
│   │   ├── analytics/     # User activity analytics
│   │   ├── users/         # Cart & wishlist management
│   │   └── products/      # Featured products management
│   ├── api/               # API routes
│   └── (customer)/        # Customer interface
├── components/            # Reusable components
│   ├── admin/            # Admin-specific components
│   ├── ui/               # UI components
│   └── charts/           # Chart components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── types/                # TypeScript definitions
└── styles/               # Global styles
```

### Development Commands
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run tests
npm test

# Lint code
npm run lint

# Type checking
npm run type-check
```

### Key Features Implementation

#### 1. Admin Analytics System
```typescript
// Location: app/admin/analytics/user-activity/page.tsx
// Features: Real-time metrics, charts, export functionality

import { UserActivityAnalytics } from '@/components/admin/analytics';

export default function UserActivityPage() {
  return <UserActivityAnalytics />;
}
```

#### 2. Cart & Wishlist Management
```typescript
// Location: app/admin/users/cart-wishlist/page.tsx
// Features: Tabbed interface, search, privacy controls

import { CartWishlistManagement } from '@/components/admin/users';

export default function CartWishlistPage() {
  return <CartWishlistManagement />;
}
```

#### 3. Featured Products Management
```typescript
// Location: app/admin/products/featured-management/page.tsx
// Features: Drag-and-drop, scheduling, preview mode

import { FeaturedProductsManagement } from '@/components/admin/products';

export default function FeaturedProductsPage() {
  return <FeaturedProductsManagement />;
}
```

### Customization Guide

#### Adding New Admin Sections
1. Create new page in `app/admin/[section]/page.tsx`
2. Add navigation item in `components/admin/Sidebar.tsx`
3. Create components in `components/admin/[section]/`
4. Add TypeScript types in `types/admin.ts`

#### Styling Customization
```typescript
// ONDC Brand Colors
const theme = {
  primary: '#3B82F6',    // Blue
  secondary: '#10B981',  // Green
  accent: '#F59E0B',     // Amber
  neutral: '#6B7280',    // Gray
};

// Tailwind Configuration
// File: tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        ondc: {
          blue: '#3B82F6',
          green: '#10B981',
        }
      }
    }
  }
};
```

#### Chart Customization
```typescript
// Chart.js Configuration
// File: components/charts/ChartConfig.ts

export const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: true,
      text: 'Analytics Chart',
      color: '#1F2937',
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: '#F3F4F6',
      },
    },
  },
};
```

---

## ⚙️ Backend Development

### Project Structure
```
packages/backend/
├── src/
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Express middleware
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── services/         # Business logic
│   └── utils/            # Utility functions
├── prisma/               # Database schema
├── tests/                # Test files
└── docs/                 # API documentation
```

### API Development

#### Creating New Endpoints
```typescript
// File: src/routes/analytics.ts
import express from 'express';
import { getAnalytics } from '../controllers/analytics';

const router = express.Router();

router.get('/user-activity', getAnalytics.userActivity);
router.get('/cart-analytics', getAnalytics.cartAnalytics);
router.get('/product-analytics', getAnalytics.productAnalytics);

export default router;
```

#### Controller Implementation
```typescript
// File: src/controllers/analytics.ts
import { Request, Response } from 'express';
import { AnalyticsService } from '../services/analytics';

export const getAnalytics = {
  async userActivity(req: Request, res: Response) {
    try {
      const data = await AnalyticsService.getUserActivity();
      res.json({ success: true, data });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
};
```

#### Database Models with Prisma
```prisma
// File: prisma/schema.prisma
model UserActivity {
  id        String   @id @default(cuid())
  userId    String
  action    String
  metadata  Json?
  timestamp DateTime @default(now())
  
  @@map("user_activities")
}

model CartAnalytics {
  id          String   @id @default(cuid())
  cartId      String
  userId      String
  totalValue  Decimal
  itemCount   Int
  lastUpdated DateTime @default(now())
  
  @@map("cart_analytics")
}
```

---

## 🔐 Authentication Setup

### OneSSO Integration (Keycloak)

#### Environment Variables
```bash
# .env.local
KEYCLOAK_URL=https://your-keycloak-instance.com
KEYCLOAK_REALM=ondc-seller
KEYCLOAK_CLIENT_ID=ondc-seller-app
KEYCLOAK_CLIENT_SECRET=your-client-secret
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

#### NextAuth Configuration
```typescript
// File: lib/auth.ts
import NextAuth from 'next-auth';
import KeycloakProvider from 'next-auth/providers/keycloak';

export const authOptions = {
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID!,
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET!,
      issuer: `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}`,
    }),
  ],
  callbacks: {
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token;
      }
      return token;
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken;
      return session;
    },
  },
};

export default NextAuth(authOptions);
```

#### Development Mode Authentication
```typescript
// File: lib/dev-auth.ts
// For development mode with hardcoded credentials

export const DEV_AUTH = {
  username: 'demo',
  password: 'demo',
  isEnabled: process.env.NODE_ENV === 'development',
};

export function validateDevAuth(username: string, password: string): boolean {
  if (!DEV_AUTH.isEnabled) return false;
  return username === DEV_AUTH.username && password === DEV_AUTH.password;
}
```

---

## 🗄️ Database Configuration

### Self-hosted Supabase Setup

#### Docker Compose Configuration
```yaml
# File: docker-compose.supabase.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ondc_seller
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your-password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  supabase-studio:
    image: supabase/studio:latest
    environment:
      SUPABASE_URL: http://localhost:8000
      SUPABASE_ANON_KEY: your-anon-key
    ports:
      - "3001:3000"

volumes:
  postgres_data:
```

#### Environment Variables
```bash
# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/ondc_seller"
SUPABASE_URL="http://localhost:8000"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

#### Prisma Setup
```bash
# Initialize Prisma
npx prisma init

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Seed database
npx prisma db seed
```

---

## 🔔 Real-time Notifications

### Option 1: Supabase Realtime
```typescript
// File: lib/realtime-supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!
);

export function subscribeToUserActivity(callback: (data: any) => void) {
  return supabase
    .channel('user-activity')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'user_activities'
    }, callback)
    .subscribe();
}
```

### Option 2: RabbitMQ
```typescript
// File: lib/realtime-rabbitmq.ts
import amqp from 'amqplib';

export class RabbitMQService {
  private connection: amqp.Connection | null = null;
  private channel: amqp.Channel | null = null;

  async connect() {
    this.connection = await amqp.connect(process.env.RABBITMQ_URL!);
    this.channel = await this.connection.createChannel();
  }

  async publishUserActivity(data: any) {
    if (!this.channel) await this.connect();
    
    await this.channel!.assertQueue('user-activity');
    this.channel!.sendToQueue('user-activity', Buffer.from(JSON.stringify(data)));
  }

  async subscribeToUserActivity(callback: (data: any) => void) {
    if (!this.channel) await this.connect();
    
    await this.channel!.assertQueue('user-activity');
    this.channel!.consume('user-activity', (msg) => {
      if (msg) {
        const data = JSON.parse(msg.content.toString());
        callback(data);
        this.channel!.ack(msg);
      }
    });
  }
}
```

---

## 🔄 Development vs Production Modes

### Environment Configuration
```typescript
// File: lib/config.ts
export const config = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  
  auth: {
    // Development mode: hardcoded credentials
    // Production mode: OneSSO/Keycloak
    useDevAuth: process.env.NODE_ENV === 'development',
    devCredentials: {
      username: 'demo',
      password: 'demo'
    }
  },
  
  database: {
    // Development: local Supabase
    // Production: hosted Supabase
    url: process.env.NODE_ENV === 'development' 
      ? 'http://localhost:8000'
      : process.env.SUPABASE_URL
  },
  
  monitoring: {
    // Development: console logging
    // Production: structured logging
    enableConsoleMonitoring: process.env.NODE_ENV === 'development',
    enableErrorTracking: process.env.NODE_ENV === 'production'
  }
};
```

### Development Mode Features
```typescript
// File: components/DevTools.tsx
export function DevTools() {
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div className="fixed bottom-4 right-4 bg-yellow-100 p-4 rounded-lg shadow-lg">
      <h3 className="font-bold text-yellow-800">Development Mode</h3>
      <p className="text-sm text-yellow-700">
        Auth: demo/demo | DB: Local | Logs: Console
      </p>
      <button 
        onClick={() => console.clear()}
        className="mt-2 px-3 py-1 bg-yellow-500 text-white rounded text-xs"
      >
        Clear Console
      </button>
    </div>
  );
}
```

---

## 🧪 Testing

### Frontend Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test admin-analytics-integration.test.js
```

### Backend Testing
```bash
# Run API tests
npm run test:api

# Run integration tests
npm run test:integration

# Run load tests
npm run test:load
```

### Test Configuration
```typescript
// File: jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  collectCoverageFrom: [
    'components/**/*.{ts,tsx}',
    'app/**/*.{ts,tsx}',
    '!**/*.d.ts',
  ],
};
```

---

## 🚀 Deployment

### Production Build
```bash
# Build frontend
npm run build

# Build backend
npm run build:backend

# Start production servers
npm run start:prod
```

### Docker Deployment
```dockerfile
# Dockerfile.frontend
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production
```bash
# Production .env
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
DATABASE_URL=***********************************/ondc_seller
KEYCLOAK_URL=https://auth.your-domain.com
SUPABASE_URL=https://your-project.supabase.co
```

---

## 📊 Monitoring & Logging

### Browser Console Monitoring (Development)
```typescript
// File: lib/console-monitor.ts
export function setupConsoleMonitoring() {
  if (process.env.NODE_ENV !== 'development') return;
  
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.error = (...args) => {
    // Log to error tracking service
    logError('console.error', args);
    originalError.apply(console, args);
  };
  
  console.warn = (...args) => {
    // Log to warning tracking service
    logWarning('console.warn', args);
    originalWarn.apply(console, args);
  };
}
```

### Change Logs & Restore Points
```typescript
// File: lib/change-tracking.ts
export function createRestorePoint(description: string) {
  const restorePoint = {
    id: Date.now(),
    description,
    timestamp: new Date().toISOString(),
    gitCommit: process.env.GIT_COMMIT,
    version: process.env.npm_package_version,
  };
  
  // Save to restore points log
  appendToFile('logs/restore-points.log', JSON.stringify(restorePoint));
}

export function logChange(change: string, component: string) {
  const changeLog = {
    timestamp: new Date().toISOString(),
    change,
    component,
    user: process.env.USER || 'system',
  };
  
  // Save to change log
  appendToFile('logs/changes.log', JSON.stringify(changeLog));
}
```

---

## 🎯 Next Steps

1. **API Integration**: Replace mock data with real API endpoints
2. **Performance Optimization**: Implement caching and lazy loading
3. **Security Hardening**: Add rate limiting and input validation
4. **Monitoring**: Set up application performance monitoring
5. **Documentation**: Generate API documentation with OpenAPI

---

*📚 This guide provides comprehensive instructions for developing, customizing, and deploying the ONDC Seller Platform. For specific questions or issues, refer to the individual component documentation or create an issue in the project repository.*
