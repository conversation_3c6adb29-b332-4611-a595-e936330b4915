# Implementation Templates
## Ready-to-Use Templates and Checklists for E-Commerce TDD

This document provides customizable templates, checklists, and configuration examples that any development team can adapt for their specific e-commerce project.

---

## 📋 Project Setup Checklists

### Initial TDD Setup Checklist

#### Phase 1: Planning and Preparation
```
□ Define testing strategy based on project size (Startup/SME/Enterprise)
□ Identify critical e-commerce features to test first
□ Select appropriate testing tools for technology stack
□ Establish test coverage goals and metrics
□ Define test naming conventions and structure
□ Set up test data management strategy
□ Plan CI/CD integration approach
□ Allocate team resources for testing activities
```

#### Phase 2: Tool Installation and Configuration
```
□ Install testing framework for chosen technology
□ Configure test runner and reporting tools
□ Set up code coverage measurement
□ Install and configure mocking libraries
□ Set up E2E testing framework
□ Configure CI/CD pipeline for automated testing
□ Set up test database or data fixtures
□ Install code quality and linting tools
```

#### Phase 3: Initial Test Implementation
```
□ Create basic test structure and utilities
□ Implement first unit test following TDD cycle
□ Create test data factories and fixtures
□ Set up API mocking for external services
□ Implement first integration test
□ Create first E2E test for critical user journey
□ Establish test review process
□ Document testing guidelines for team
```

---

## 🏗️ Project Structure Templates

### Universal E-Commerce Test Structure

```
project-root/
├── src/                          # Source code
│   ├── components/               # UI Components
│   │   ├── ProductCard/
│   │   │   ├── ProductCard.{ext}
│   │   │   ├── ProductCard.test.{ext}
│   │   │   └── ProductCard.stories.{ext}
│   │   ├── ShoppingCart/
│   │   └── CheckoutForm/
│   ├── services/                 # Business Logic
│   │   ├── api/
│   │   │   ├── products.{ext}
│   │   │   ├── products.test.{ext}
│   │   │   └── products.integration.test.{ext}
│   │   ├── auth/
│   │   └── payment/
│   ├── utils/                    # Utility Functions
│   │   ├── formatters.{ext}
│   │   ├── formatters.test.{ext}
│   │   ├── validators.{ext}
│   │   └── validators.test.{ext}
│   └── models/                   # Data Models
│       ├── Product.{ext}
│       ├── Product.test.{ext}
│       ├── User.{ext}
│       └── Order.{ext}
├── tests/                        # Test-specific files
│   ├── fixtures/                 # Test data
│   │   ├── products.json
│   │   ├── users.json
│   │   └── orders.json
│   ├── mocks/                    # Mock implementations
│   │   ├── api-mocks.{ext}
│   │   ├── payment-gateway-mock.{ext}
│   │   └── database-mock.{ext}
│   ├── utils/                    # Test utilities
│   │   ├── test-helpers.{ext}
│   │   ├── data-factories.{ext}
│   │   └── custom-matchers.{ext}
│   ├── integration/              # Integration tests
│   │   ├── api/
│   │   ├── database/
│   │   └── external-services/
│   ├── e2e/                      # End-to-end tests
│   │   ├── user-journeys/
│   │   ├── admin-workflows/
│   │   └── payment-flows/
│   └── performance/              # Performance tests
│       ├── load-tests/
│       └── stress-tests/
├── config/                       # Configuration files
│   ├── test.config.{ext}
│   ├── jest.config.js            # For JavaScript projects
│   ├── pytest.ini               # For Python projects
│   └── ci-cd/
│       ├── github-actions.yml
│       ├── jenkins.groovy
│       └── docker-compose.test.yml
└── docs/                         # Documentation
    ├── testing-guidelines.md
    ├── test-data-management.md
    └── ci-cd-setup.md
```

---

## 🧪 Test Template Library

### Unit Test Templates

#### Component/Class Test Template
```pseudocode
// Universal component/class test template
DESCRIBE "{ComponentName} Component/Class"
  // Setup and teardown
  BEFORE_EACH
    // Initialize test dependencies
    mock_dependencies = CREATE_MOCKS()
    test_data = CREATE_TEST_DATA()
  END
  
  AFTER_EACH
    // Clean up resources
    CLEANUP_MOCKS()
    RESET_TEST_STATE()
  END
  
  // Positive test cases
  DESCRIBE "when given valid input"
    TEST "should render/behave correctly with default props/parameters"
      // Arrange
      default_props = CREATE_DEFAULT_PROPS()
      
      // Act
      result = RENDER_OR_EXECUTE(ComponentName, default_props)
      
      // Assert
      ASSERT result CONTAINS_EXPECTED_ELEMENTS()
      ASSERT result BEHAVES_AS_EXPECTED()
    END
    
    TEST "should handle user interactions correctly"
      // Arrange
      interaction_props = CREATE_INTERACTION_PROPS()
      mock_handler = CREATE_MOCK_HANDLER()
      
      // Act
      result = RENDER_OR_EXECUTE(ComponentName, interaction_props)
      SIMULATE_USER_INTERACTION(result)
      
      // Assert
      ASSERT mock_handler WAS_CALLED_WITH_CORRECT_PARAMETERS()
      ASSERT result STATE_UPDATED_CORRECTLY()
    END
  END
  
  // Negative test cases
  DESCRIBE "when given invalid input"
    TEST "should handle missing required props/parameters gracefully"
      // Arrange
      invalid_props = CREATE_INVALID_PROPS()
      
      // Act & Assert
      EXPECT_ERROR_OR_FALLBACK WHEN RENDER_OR_EXECUTE(ComponentName, invalid_props)
    END
    
    TEST "should validate input and show appropriate error messages"
      // Arrange
      invalid_input = CREATE_INVALID_INPUT()
      
      // Act
      result = RENDER_OR_EXECUTE(ComponentName, {input: invalid_input})
      
      // Assert
      ASSERT result SHOWS_ERROR_MESSAGE()
      ASSERT result PREVENTS_INVALID_SUBMISSION()
    END
  END
  
  // Edge cases
  DESCRIBE "edge cases"
    TEST "should handle empty/null data gracefully"
      // Test with empty arrays, null values, etc.
    END
    
    TEST "should handle large datasets efficiently"
      // Test with large amounts of data
    END
  END
END
```

#### Service/API Test Template
```pseudocode
DESCRIBE "{ServiceName} Service"
  BEFORE_EACH
    mock_http_client = CREATE_MOCK_HTTP_CLIENT()
    mock_database = CREATE_MOCK_DATABASE()
    service = CREATE_SERVICE(mock_http_client, mock_database)
  END
  
  DESCRIBE "successful operations"
    TEST "should fetch data successfully"
      // Arrange
      expected_data = CREATE_EXPECTED_DATA()
      MOCK_HTTP_RESPONSE(200, expected_data)
      
      // Act
      result = service.fetch_data(valid_parameters)
      
      // Assert
      ASSERT result EQUALS expected_data
      ASSERT HTTP_CLIENT WAS_CALLED_WITH_CORRECT_URL()
    END
    
    TEST "should create new record successfully"
      // Arrange
      new_record_data = CREATE_NEW_RECORD_DATA()
      expected_response = CREATE_EXPECTED_RESPONSE()
      MOCK_HTTP_RESPONSE(201, expected_response)
      
      // Act
      result = service.create_record(new_record_data)
      
      // Assert
      ASSERT result CONTAINS expected_response
      ASSERT HTTP_CLIENT WAS_CALLED_WITH_POST_METHOD()
    END
  END
  
  DESCRIBE "error handling"
    TEST "should handle network errors gracefully"
      // Arrange
      MOCK_HTTP_ERROR(NETWORK_ERROR)
      
      // Act
      result = service.fetch_data(valid_parameters)
      
      // Assert
      ASSERT result IS_ERROR_RESULT()
      ASSERT result.error_type EQUALS "NETWORK_ERROR"
    END
    
    TEST "should handle server errors with appropriate retry logic"
      // Arrange
      MOCK_HTTP_RESPONSE(500, server_error_response)
      
      // Act
      result = service.fetch_data(valid_parameters)
      
      // Assert
      ASSERT result IS_ERROR_RESULT()
      ASSERT RETRY_LOGIC_WAS_TRIGGERED()
    END
  END
END
```

### Integration Test Templates

#### API Integration Test Template
```pseudocode
DESCRIBE "{API_Endpoint} Integration Tests"
  BEFORE_ALL
    test_server = START_TEST_SERVER()
    test_database = CREATE_AND_SEED_TEST_DATABASE()
  END
  
  AFTER_ALL
    STOP_TEST_SERVER()
    CLEANUP_TEST_DATABASE()
  END
  
  BEFORE_EACH
    RESET_DATABASE_TO_KNOWN_STATE()
  END
  
  DESCRIBE "GET {endpoint}"
    TEST "should return correct data format"
      // Act
      response = HTTP_GET(endpoint_url)
      
      // Assert
      ASSERT response.status EQUALS 200
      ASSERT response.headers["Content-Type"] CONTAINS "application/json"
      ASSERT response.body MATCHES_EXPECTED_SCHEMA()
    END
    
    TEST "should handle query parameters correctly"
      // Arrange
      query_params = CREATE_QUERY_PARAMS()
      
      // Act
      response = HTTP_GET(endpoint_url + query_params)
      
      // Assert
      ASSERT response.status EQUALS 200
      ASSERT response.body FILTERED_CORRECTLY()
    END
  END
  
  DESCRIBE "POST {endpoint}"
    TEST "should create new resource with valid data"
      // Arrange
      valid_payload = CREATE_VALID_PAYLOAD()
      
      // Act
      response = HTTP_POST(endpoint_url, valid_payload)
      
      // Assert
      ASSERT response.status EQUALS 201
      ASSERT response.body CONTAINS_CREATED_RESOURCE()
      ASSERT DATABASE_CONTAINS_NEW_RECORD()
    END
    
    TEST "should reject invalid data with appropriate error"
      // Arrange
      invalid_payload = CREATE_INVALID_PAYLOAD()
      
      // Act
      response = HTTP_POST(endpoint_url, invalid_payload)
      
      // Assert
      ASSERT response.status EQUALS 400
      ASSERT response.body CONTAINS_VALIDATION_ERRORS()
    END
  END
END
```

### E2E Test Templates

#### User Journey Test Template
```pseudocode
DESCRIBE "Complete {UserJourney} Flow"
  BEFORE_EACH
    SETUP_TEST_ENVIRONMENT()
    SEED_TEST_DATA()
    browser = LAUNCH_BROWSER()
    page = OPEN_NEW_PAGE()
  END
  
  AFTER_EACH
    CLOSE_BROWSER()
    CLEANUP_TEST_DATA()
  END
  
  TEST "user can complete {journey_name} successfully"
    // Step 1: Navigate to starting point
    NAVIGATE_TO(starting_url)
    WAIT_FOR_PAGE_LOAD()
    
    // Step 2: Perform user actions
    FILL_FORM_FIELD("email", test_email)
    FILL_FORM_FIELD("password", test_password)
    CLICK_BUTTON("Login")
    
    // Step 3: Verify intermediate state
    WAIT_FOR_ELEMENT("dashboard")
    ASSERT_ELEMENT_VISIBLE("welcome_message")
    
    // Step 4: Continue journey
    CLICK_LINK("Products")
    WAIT_FOR_ELEMENT("product_list")
    
    // Step 5: Complete main action
    CLICK_ELEMENT("first_product")
    CLICK_BUTTON("Add to Cart")
    
    // Step 6: Verify final state
    ASSERT_ELEMENT_VISIBLE("cart_notification")
    ASSERT_TEXT_CONTAINS("cart_count", "1")
    
    // Step 7: Verify persistence
    REFRESH_PAGE()
    ASSERT_TEXT_CONTAINS("cart_count", "1")
  END
  
  TEST "user receives appropriate feedback for errors"
    // Test error scenarios in the same journey
    NAVIGATE_TO(starting_url)
    
    // Trigger error condition
    FILL_FORM_FIELD("email", "invalid_email")
    CLICK_BUTTON("Login")
    
    // Verify error handling
    ASSERT_ELEMENT_VISIBLE("error_message")
    ASSERT_TEXT_CONTAINS("error_message", "Invalid email format")
    ASSERT_FORM_NOT_SUBMITTED()
  END
END
```

---

## 📊 Configuration Templates

### Test Configuration Template (JavaScript/Jest)
```javascript
// jest.config.js - Universal Jest configuration
module.exports = {
  // Test environment
  testEnvironment: 'jsdom', // or 'node' for backend
  
  // File patterns
  testMatch: [
    '**/__tests__/**/*.(js|jsx|ts|tsx)',
    '**/*.(test|spec).(js|jsx|ts|tsx)'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.{js,jsx,ts,tsx}',
    '!src/**/*.stories.{js,jsx,ts,tsx}'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    // Critical paths require higher coverage
    './src/services/payment/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Module mapping for aliases
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest'
  },
  
  // Mock configuration
  clearMocks: true,
  restoreMocks: true
};
```

### Test Configuration Template (Python/pytest)
```python
# pytest.ini - Universal pytest configuration
[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Coverage configuration
addopts = 
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --strict-markers
    --disable-warnings

# Coverage thresholds for critical modules
cov-fail-under = 80

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    critical: Critical path tests
    
# Test environment
env = 
    TESTING = true
    DATABASE_URL = sqlite:///test.db
```

---

## 🔄 CI/CD Pipeline Templates

### GitHub Actions Template
```yaml
# .github/workflows/test.yml
name: E-Commerce Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
    
    - name: Build application
      run: npm run build
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload test artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: test-results
        path: |
          test-results/
          screenshots/
```

### Jenkins Pipeline Template
```groovy
// Jenkinsfile - Universal Jenkins pipeline
pipeline {
    agent any
    
    environment {
        NODE_VERSION = '18'
        TEST_DATABASE_URL = 'postgresql://test:test@localhost:5432/ecommerce_test'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup') {
            steps {
                sh 'nvm use ${NODE_VERSION}'
                sh 'npm ci'
            }
        }
        
        stage('Code Quality') {
            parallel {
                stage('Lint') {
                    steps {
                        sh 'npm run lint'
                    }
                }
                stage('Security Scan') {
                    steps {
                        sh 'npm audit --audit-level moderate'
                    }
                }
            }
        }
        
        stage('Unit Tests') {
            steps {
                sh 'npm run test:unit -- --coverage'
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'coverage',
                        reportFiles: 'index.html',
                        reportName: 'Coverage Report'
                    ])
                }
            }
        }
        
        stage('Integration Tests') {
            steps {
                sh 'docker-compose -f docker-compose.test.yml up -d'
                sh 'npm run test:integration'
            }
            post {
                always {
                    sh 'docker-compose -f docker-compose.test.yml down'
                }
            }
        }
        
        stage('Build') {
            steps {
                sh 'npm run build'
            }
        }
        
        stage('E2E Tests') {
            steps {
                sh 'npm run test:e2e'
            }
            post {
                always {
                    archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        failure {
            emailext (
                subject: "Build Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "Build failed. Check console output at ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

---

## 📝 Documentation Templates

### Testing Guidelines Template
```markdown
# Testing Guidelines for [Project Name]

## Overview
This document outlines testing standards and practices for our e-commerce platform.

## Test Categories

### Unit Tests
- **Purpose**: Test individual functions, classes, and components in isolation
- **Coverage Target**: 80% minimum, 95% for critical business logic
- **Location**: Co-located with source files (*.test.{ext})
- **Naming**: `describe('ComponentName', () => { test('should...', () => {}) })`

### Integration Tests
- **Purpose**: Test interaction between multiple components/services
- **Coverage Target**: 70% of integration points
- **Location**: `/tests/integration/`
- **Focus**: API endpoints, database operations, external service integration

### E2E Tests
- **Purpose**: Test complete user journeys
- **Coverage Target**: 100% of critical user paths
- **Location**: `/tests/e2e/`
- **Focus**: Happy paths, error scenarios, cross-browser compatibility

## Test Data Management
- Use data factories for consistent test data creation
- Keep test data minimal and focused
- Clean up test data after each test
- Use realistic but anonymized data for integration tests

## Best Practices
1. Follow the AAA pattern (Arrange, Act, Assert)
2. Write descriptive test names that explain the scenario
3. Keep tests independent and isolated
4. Mock external dependencies
5. Test behavior, not implementation details

## Code Review Checklist
- [ ] Tests cover both happy path and error scenarios
- [ ] Test names clearly describe what is being tested
- [ ] Tests are independent and can run in any order
- [ ] Appropriate mocking is used for external dependencies
- [ ] Test data is properly cleaned up
```

These templates provide a solid foundation that can be customized for any e-commerce project, regardless of technology stack or project size.
