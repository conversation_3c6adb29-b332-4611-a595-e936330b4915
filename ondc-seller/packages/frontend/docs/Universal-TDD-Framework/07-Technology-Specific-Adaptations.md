# Technology-Specific Adaptations
## Adapting Universal TDD Principles to Popular Technology Stacks

This document provides specific guidance for implementing the universal TDD framework across different technology stacks commonly used in e-commerce development.

---

## 🌐 Frontend Technologies

### React.js Ecosystem

#### Testing Stack
```javascript
// Recommended React testing setup
{
  "testFramework": "Jest",
  "testingLibrary": "@testing-library/react",
  "e2eFramework": "Playwright or Cypress",
  "mockingLibrary": "MSW (Mock Service Worker)",
  "componentTesting": "Storybook"
}
```

#### Component Testing Pattern
```javascript
// ProductCard.test.jsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ProductCard } from './ProductCard';

describe('ProductCard Component', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 99.99,
    image: '/test-image.jpg'
  };

  it('should display product information correctly', () => {
    // Arrange
    const mockOnAddToCart = jest.fn();
    
    // Act
    render(<ProductCard product={mockProduct} onAddToCart={mockOnAddToCart} />);
    
    // Assert
    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('$99.99')).toBeInTheDocument();
  });

  it('should call onAddToCart when button is clicked', () => {
    // Arrange
    const mockOnAddToCart = jest.fn();
    render(<ProductCard product={mockProduct} onAddToCart={mockOnAddToCart} />);
    
    // Act
    fireEvent.click(screen.getByText('Add to Cart'));
    
    // Assert
    expect(mockOnAddToCart).toHaveBeenCalledWith(mockProduct);
  });
});
```

#### Hook Testing Pattern
```javascript
// useCart.test.js
import { renderHook, act } from '@testing-library/react';
import { useCart } from './useCart';

describe('useCart Hook', () => {
  it('should add item to cart', () => {
    // Arrange
    const { result } = renderHook(() => useCart());
    const product = { id: '1', name: 'Test Product', price: 99.99 };
    
    // Act
    act(() => {
      result.current.addItem(product, 2);
    });
    
    // Assert
    expect(result.current.items).toHaveLength(1);
    expect(result.current.totalItems).toBe(2);
    expect(result.current.totalPrice).toBe(199.98);
  });
});
```

### Vue.js Ecosystem

#### Testing Stack
```javascript
{
  "testFramework": "Vitest or Jest",
  "testingLibrary": "@testing-library/vue",
  "e2eFramework": "Cypress or Playwright",
  "mockingLibrary": "MSW",
  "componentTesting": "Vue Test Utils"
}
```

#### Component Testing Pattern
```javascript
// ProductCard.spec.js
import { mount } from '@vue/test-utils';
import ProductCard from './ProductCard.vue';

describe('ProductCard Component', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 99.99,
    image: '/test-image.jpg'
  };

  it('should display product information correctly', () => {
    // Arrange & Act
    const wrapper = mount(ProductCard, {
      props: { product: mockProduct }
    });
    
    // Assert
    expect(wrapper.text()).toContain('Test Product');
    expect(wrapper.text()).toContain('$99.99');
  });

  it('should emit add-to-cart event when button is clicked', async () => {
    // Arrange
    const wrapper = mount(ProductCard, {
      props: { product: mockProduct }
    });
    
    // Act
    await wrapper.find('[data-testid="add-to-cart"]').trigger('click');
    
    // Assert
    expect(wrapper.emitted('add-to-cart')).toBeTruthy();
    expect(wrapper.emitted('add-to-cart')[0]).toEqual([mockProduct]);
  });
});
```

### Angular Ecosystem

#### Testing Stack
```typescript
{
  "testFramework": "Jasmine + Karma",
  "testingLibrary": "@angular/testing",
  "e2eFramework": "Protractor or Cypress",
  "mockingLibrary": "Angular HttpClientTestingModule",
  "componentTesting": "Angular Testing Utilities"
}
```

#### Component Testing Pattern
```typescript
// product-card.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProductCardComponent } from './product-card.component';

describe('ProductCardComponent', () => {
  let component: ProductCardComponent;
  let fixture: ComponentFixture<ProductCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ProductCardComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductCardComponent);
    component = fixture.componentInstance;
    component.product = {
      id: '1',
      name: 'Test Product',
      price: 99.99,
      image: '/test-image.jpg'
    };
    fixture.detectChanges();
  });

  it('should display product information correctly', () => {
    // Assert
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('h3').textContent).toContain('Test Product');
    expect(compiled.querySelector('.price').textContent).toContain('$99.99');
  });

  it('should emit addToCart event when button is clicked', () => {
    // Arrange
    spyOn(component.addToCart, 'emit');
    
    // Act
    const button = fixture.nativeElement.querySelector('button');
    button.click();
    
    // Assert
    expect(component.addToCart.emit).toHaveBeenCalledWith(component.product);
  });
});
```

---

## 🖥️ Backend Technologies

### Node.js with Express

#### Testing Stack
```javascript
{
  "testFramework": "Jest or Mocha",
  "httpTesting": "Supertest",
  "mockingLibrary": "Sinon or Jest mocks",
  "databaseTesting": "In-memory database or test containers"
}
```

#### API Testing Pattern
```javascript
// products.test.js
const request = require('supertest');
const app = require('../app');
const Product = require('../models/Product');

describe('Products API', () => {
  beforeEach(async () => {
    await Product.deleteMany({});
  });

  describe('GET /api/products', () => {
    it('should return list of products', async () => {
      // Arrange
      await Product.create([
        { name: 'Product 1', price: 99.99 },
        { name: 'Product 2', price: 149.99 }
      ]);
      
      // Act
      const response = await request(app)
        .get('/api/products')
        .expect(200);
      
      // Assert
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('price');
    });
  });

  describe('POST /api/products', () => {
    it('should create new product with valid data', async () => {
      // Arrange
      const newProduct = {
        name: 'New Product',
        price: 199.99,
        category: 'Electronics'
      };
      
      // Act
      const response = await request(app)
        .post('/api/products')
        .send(newProduct)
        .expect(201);
      
      // Assert
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe('New Product');
      
      // Verify in database
      const savedProduct = await Product.findById(response.body.data.id);
      expect(savedProduct).toBeTruthy();
    });
  });
});
```

### Python with Django/FastAPI

#### Testing Stack (Django)
```python
{
    "testFramework": "pytest or Django TestCase",
    "httpTesting": "Django Test Client or pytest-django",
    "mockingLibrary": "unittest.mock or pytest-mock",
    "databaseTesting": "Django test database"
}
```

#### Model Testing Pattern (Django)
```python
# test_models.py
import pytest
from django.core.exceptions import ValidationError
from products.models import Product

class TestProductModel:
    def test_create_product_with_valid_data(self):
        # Arrange
        product_data = {
            'name': 'Test Product',
            'price': 99.99,
            'category': 'Electronics'
        }
        
        # Act
        product = Product.objects.create(**product_data)
        
        # Assert
        assert product.id is not None
        assert product.name == 'Test Product'
        assert product.price == 99.99
    
    def test_product_price_cannot_be_negative(self):
        # Arrange
        product_data = {
            'name': 'Invalid Product',
            'price': -10.00,
            'category': 'Electronics'
        }
        
        # Act & Assert
        with pytest.raises(ValidationError):
            product = Product(**product_data)
            product.full_clean()
```

#### API Testing Pattern (FastAPI)
```python
# test_products_api.py
import pytest
from fastapi.testclient import TestClient
from main import app
from database import get_test_db

client = TestClient(app)

class TestProductsAPI:
    def test_get_products_returns_list(self):
        # Arrange
        # Seed test database with products
        
        # Act
        response = client.get("/api/products")
        
        # Assert
        assert response.status_code == 200
        assert isinstance(response.json()["data"], list)
    
    def test_create_product_with_valid_data(self):
        # Arrange
        product_data = {
            "name": "New Product",
            "price": 199.99,
            "category": "Electronics"
        }
        
        # Act
        response = client.post("/api/products", json=product_data)
        
        # Assert
        assert response.status_code == 201
        assert response.json()["data"]["name"] == "New Product"
```

### Java with Spring Boot

#### Testing Stack
```java
{
    "testFramework": "JUnit 5",
    "springTesting": "Spring Boot Test",
    "mockingLibrary": "Mockito",
    "httpTesting": "MockMvc or TestRestTemplate",
    "databaseTesting": "@DataJpaTest"
}
```

#### Service Testing Pattern
```java
// ProductServiceTest.java
@ExtendWith(MockitoExtension.class)
class ProductServiceTest {
    
    @Mock
    private ProductRepository productRepository;
    
    @InjectMocks
    private ProductService productService;
    
    @Test
    void shouldCreateProductWithValidData() {
        // Arrange
        Product newProduct = new Product("Test Product", 99.99, "Electronics");
        Product savedProduct = new Product(1L, "Test Product", 99.99, "Electronics");
        
        when(productRepository.save(any(Product.class))).thenReturn(savedProduct);
        
        // Act
        Product result = productService.createProduct(newProduct);
        
        // Assert
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("Test Product");
        verify(productRepository).save(newProduct);
    }
    
    @Test
    void shouldThrowExceptionWhenProductNameIsEmpty() {
        // Arrange
        Product invalidProduct = new Product("", 99.99, "Electronics");
        
        // Act & Assert
        assertThrows(ValidationException.class, () -> {
            productService.createProduct(invalidProduct);
        });
        
        verify(productRepository, never()).save(any());
    }
}
```

#### Controller Testing Pattern
```java
// ProductControllerTest.java
@WebMvcTest(ProductController.class)
class ProductControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private ProductService productService;
    
    @Test
    void shouldReturnProductList() throws Exception {
        // Arrange
        List<Product> products = Arrays.asList(
            new Product(1L, "Product 1", 99.99, "Electronics"),
            new Product(2L, "Product 2", 149.99, "Electronics")
        );
        
        when(productService.getAllProducts()).thenReturn(products);
        
        // Act & Assert
        mockMvc.perform(get("/api/products"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data", hasSize(2)))
            .andExpected(jsonPath("$.data[0].name", is("Product 1")));
    }
    
    @Test
    void shouldCreateProductWithValidData() throws Exception {
        // Arrange
        Product newProduct = new Product("New Product", 199.99, "Electronics");
        Product savedProduct = new Product(1L, "New Product", 199.99, "Electronics");
        
        when(productService.createProduct(any(Product.class))).thenReturn(savedProduct);
        
        // Act & Assert
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"New Product\",\"price\":199.99,\"category\":\"Electronics\"}"))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.data.id", is(1)))
            .andExpect(jsonPath("$.data.name", is("New Product")));
    }
}
```

---

## 📱 Mobile Technologies

### React Native

#### Testing Stack
```javascript
{
  "testFramework": "Jest",
  "testingLibrary": "@testing-library/react-native",
  "e2eFramework": "Detox",
  "mockingLibrary": "Jest mocks + Metro mocks"
}
```

#### Component Testing Pattern
```javascript
// ProductCard.test.js
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductCard from './ProductCard';

describe('ProductCard Component', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 99.99,
    image: 'test-image.jpg'
  };

  it('should display product information correctly', () => {
    // Arrange & Act
    const { getByText } = render(<ProductCard product={mockProduct} />);
    
    // Assert
    expect(getByText('Test Product')).toBeTruthy();
    expect(getByText('$99.99')).toBeTruthy();
  });

  it('should call onAddToCart when button is pressed', () => {
    // Arrange
    const mockOnAddToCart = jest.fn();
    const { getByText } = render(
      <ProductCard product={mockProduct} onAddToCart={mockOnAddToCart} />
    );
    
    // Act
    fireEvent.press(getByText('Add to Cart'));
    
    // Assert
    expect(mockOnAddToCart).toHaveBeenCalledWith(mockProduct);
  });
});
```

### Flutter

#### Testing Stack
```dart
{
  "testFramework": "flutter_test",
  "widgetTesting": "WidgetTester",
  "integrationTesting": "integration_test",
  "mockingLibrary": "mockito"
}
```

#### Widget Testing Pattern
```dart
// product_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/product_card.dart';
import 'package:myapp/models/product.dart';

void main() {
  group('ProductCard Widget', () {
    final mockProduct = Product(
      id: '1',
      name: 'Test Product',
      price: 99.99,
      image: 'test-image.jpg',
    );

    testWidgets('should display product information correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: ProductCard(product: mockProduct),
        ),
      );
      
      // Assert
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });

    testWidgets('should call onAddToCart when button is tapped', (WidgetTester tester) async {
      // Arrange
      bool addToCartCalled = false;
      void mockOnAddToCart(Product product) {
        addToCartCalled = true;
      }
      
      await tester.pumpWidget(
        MaterialApp(
          home: ProductCard(
            product: mockProduct,
            onAddToCart: mockOnAddToCart,
          ),
        ),
      );
      
      // Act
      await tester.tap(find.text('Add to Cart'));
      await tester.pump();
      
      // Assert
      expect(addToCartCalled, isTrue);
    });
  });
}
```

---

## 🗄️ Database Technologies

### SQL Databases (PostgreSQL, MySQL)

#### Testing Strategies
```sql
-- Test data setup
CREATE TABLE test_products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test data cleanup
TRUNCATE TABLE test_products RESTART IDENTITY CASCADE;
```

#### Repository Testing Pattern
```javascript
// productRepository.test.js
describe('Product Repository', () => {
  beforeEach(async () => {
    await db.query('TRUNCATE TABLE products RESTART IDENTITY CASCADE');
  });

  it('should create product with valid data', async () => {
    // Arrange
    const productData = {
      name: 'Test Product',
      price: 99.99,
      category: 'Electronics'
    };
    
    // Act
    const result = await productRepository.create(productData);
    
    // Assert
    expect(result.id).toBeDefined();
    expect(result.name).toBe('Test Product');
    
    // Verify in database
    const dbProduct = await db.query('SELECT * FROM products WHERE id = $1', [result.id]);
    expect(dbProduct.rows[0].name).toBe('Test Product');
  });
});
```

### NoSQL Databases (MongoDB)

#### Testing Pattern
```javascript
// productRepository.test.js
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const Product = require('../models/Product');

describe('Product Repository', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    await mongoose.connect(mongoServer.getUri());
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await Product.deleteMany({});
  });

  it('should create product with valid data', async () => {
    // Arrange
    const productData = {
      name: 'Test Product',
      price: 99.99,
      category: 'Electronics'
    };
    
    // Act
    const product = await Product.create(productData);
    
    // Assert
    expect(product._id).toBeDefined();
    expect(product.name).toBe('Test Product');
    
    // Verify in database
    const savedProduct = await Product.findById(product._id);
    expect(savedProduct.name).toBe('Test Product');
  });
});
```

---

## 🔧 Tool-Specific Configurations

### CI/CD Platform Adaptations

#### GitHub Actions for Different Stacks
```yaml
# Node.js project
- name: Setup Node.js
  uses: actions/setup-node@v4
  with:
    node-version: '18'
    cache: 'npm'

# Python project  
- name: Setup Python
  uses: actions/setup-python@v4
  with:
    python-version: '3.9'
    cache: 'pip'

# Java project
- name: Setup Java
  uses: actions/setup-java@v4
  with:
    java-version: '11'
    distribution: 'temurin'
    cache: 'maven'
```

#### Docker Test Environments
```dockerfile
# Node.js test environment
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "test"]

# Python test environment
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["pytest"]
```

These technology-specific adaptations ensure that the universal TDD principles can be effectively implemented regardless of your chosen technology stack, while leveraging the best tools and practices for each platform.
