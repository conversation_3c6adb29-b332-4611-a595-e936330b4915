# Technology-Agnostic Examples
## Universal Test Patterns for E-Commerce Development

This document provides test examples and patterns that can be adapted to any technology stack, programming language, or framework used in e-commerce development.

---

## 🎯 Universal Test Patterns

### Basic Test Structure Template

```pseudocode
// Universal test structure that works in any language
DESCRIBE "Feature or Component Name"
  BEFORE_EACH
    // Setup test data and environment
    test_data = CREATE_TEST_DATA()
    mock_dependencies = SETUP_MOCKS()
  END
  
  TEST "should perform expected behavior when given valid input"
    // Arrange: Set up the test scenario
    input = VALID_INPUT_DATA
    expected_result = EXPECTED_OUTPUT
    
    // Act: Execute the functionality being tested
    actual_result = EXECUTE_FUNCTION(input)
    
    // Assert: Verify the outcome
    ASSERT actual_result EQUALS expected_result
    VERIFY_SIDE_EFFECTS()
  END
  
  TEST "should handle error conditions gracefully"
    // Arrange: Set up error scenario
    invalid_input = INVALID_INPUT_DATA
    
    // Act & Assert: Verify error handling
    EXPECT_ERROR WHEN EXECUTE_FUNCTION(invalid_input)
    VERIFY_ERROR_MESSAGE_IS_HELPFUL()
  END
  
  AFTER_EACH
    // Clean up test environment
    CLEANUP_TEST_DATA()
    RESET_MOCKS()
  END
END
```

---

## 🛍️ Product Catalog Testing Patterns

### Product Model Testing

#### Data Validation Pattern
```pseudocode
// Works with any OOP language (Java, C#, Python, JavaScript, etc.)
DESCRIBE "Product Model"
  TEST "should create valid product with required fields"
    // Arrange
    product_data = {
      id: "PROD-001",
      name: "Premium Laptop",
      price: 999.99,
      category: "Electronics",
      in_stock: true
    }
    
    // Act
    product = CREATE_PRODUCT(product_data)
    
    // Assert
    ASSERT product.id EQUALS "PROD-001"
    ASSERT product.name EQUALS "Premium Laptop"
    ASSERT product.price EQUALS 999.99
    ASSERT product.is_valid() EQUALS true
  END
  
  TEST "should reject product with invalid price"
    // Arrange
    invalid_data = {
      id: "PROD-002",
      name: "Invalid Product",
      price: -10.00,  // Invalid negative price
      category: "Electronics"
    }
    
    // Act & Assert
    EXPECT_EXCEPTION WHEN CREATE_PRODUCT(invalid_data)
    OR
    product = CREATE_PRODUCT(invalid_data)
    ASSERT product.is_valid() EQUALS false
    ASSERT product.get_errors() CONTAINS "Price must be positive"
  END
END
```

#### Business Logic Pattern
```pseudocode
DESCRIBE "Product Price Calculations"
  TEST "should calculate discounted price correctly"
    // Arrange
    product = CREATE_PRODUCT({price: 100.00})
    discount_percentage = 20
    
    // Act
    discounted_price = product.calculate_discounted_price(discount_percentage)
    
    // Assert
    ASSERT discounted_price EQUALS 80.00
  END
  
  TEST "should handle zero discount"
    // Arrange
    product = CREATE_PRODUCT({price: 100.00})
    discount_percentage = 0
    
    // Act
    discounted_price = product.calculate_discounted_price(discount_percentage)
    
    // Assert
    ASSERT discounted_price EQUALS 100.00
  END
  
  TEST "should reject invalid discount percentage"
    // Arrange
    product = CREATE_PRODUCT({price: 100.00})
    invalid_discount = 150  // Over 100%
    
    // Act & Assert
    EXPECT_EXCEPTION WHEN product.calculate_discounted_price(invalid_discount)
  END
END
```

### Product Service Testing

#### Repository Pattern Testing
```pseudocode
DESCRIBE "Product Repository"
  BEFORE_EACH
    mock_database = CREATE_MOCK_DATABASE()
    product_repository = CREATE_PRODUCT_REPOSITORY(mock_database)
  END
  
  TEST "should retrieve product by ID"
    // Arrange
    expected_product = CREATE_TEST_PRODUCT({id: "PROD-001"})
    MOCK_DATABASE_RETURN(expected_product) WHEN QUERY_BY_ID("PROD-001")
    
    // Act
    actual_product = product_repository.get_by_id("PROD-001")
    
    // Assert
    ASSERT actual_product EQUALS expected_product
    VERIFY_DATABASE_CALLED_WITH("SELECT * FROM products WHERE id = ?", "PROD-001")
  END
  
  TEST "should return null for non-existent product"
    // Arrange
    MOCK_DATABASE_RETURN(null) WHEN QUERY_BY_ID("NON-EXISTENT")
    
    // Act
    result = product_repository.get_by_id("NON-EXISTENT")
    
    // Assert
    ASSERT result EQUALS null
  END
  
  TEST "should handle database connection errors"
    // Arrange
    MOCK_DATABASE_THROW_ERROR() WHEN QUERY_BY_ID(ANY)
    
    // Act & Assert
    EXPECT_EXCEPTION WHEN product_repository.get_by_id("PROD-001")
    OR
    result = product_repository.get_by_id("PROD-001")
    ASSERT result.is_error() EQUALS true
  END
END
```

---

## 🛒 Shopping Cart Testing Patterns

### Cart State Management

#### Functional Programming Approach
```pseudocode
DESCRIBE "Cart State Functions"
  TEST "should add item to empty cart"
    // Arrange
    empty_cart = CREATE_EMPTY_CART()
    product = CREATE_TEST_PRODUCT()
    quantity = 2
    
    // Act
    new_cart = ADD_ITEM_TO_CART(empty_cart, product, quantity)
    
    // Assert
    ASSERT new_cart.items.length EQUALS 1
    ASSERT new_cart.items[0].product EQUALS product
    ASSERT new_cart.items[0].quantity EQUALS 2
    ASSERT new_cart.total_items EQUALS 2
  END
  
  TEST "should update quantity for existing item"
    // Arrange
    cart_with_item = CREATE_CART_WITH_ITEMS([
      {product: PRODUCT_A, quantity: 1}
    ])
    
    // Act
    updated_cart = ADD_ITEM_TO_CART(cart_with_item, PRODUCT_A, 2)
    
    // Assert
    ASSERT updated_cart.items.length EQUALS 1
    ASSERT updated_cart.items[0].quantity EQUALS 3
  END
  
  TEST "should remove item from cart"
    // Arrange
    cart_with_items = CREATE_CART_WITH_ITEMS([
      {product: PRODUCT_A, quantity: 2},
      {product: PRODUCT_B, quantity: 1}
    ])
    
    // Act
    updated_cart = REMOVE_ITEM_FROM_CART(cart_with_items, PRODUCT_A.id)
    
    // Assert
    ASSERT updated_cart.items.length EQUALS 1
    ASSERT updated_cart.items[0].product EQUALS PRODUCT_B
  END
END
```

#### Object-Oriented Approach
```pseudocode
DESCRIBE "Shopping Cart Class"
  BEFORE_EACH
    cart = CREATE_SHOPPING_CART()
  END
  
  TEST "should start with empty state"
    // Assert
    ASSERT cart.get_item_count() EQUALS 0
    ASSERT cart.get_total_price() EQUALS 0.00
    ASSERT cart.is_empty() EQUALS true
  END
  
  TEST "should add item and update totals"
    // Arrange
    product = CREATE_TEST_PRODUCT({price: 50.00})
    quantity = 2
    
    // Act
    cart.add_item(product, quantity)
    
    // Assert
    ASSERT cart.get_item_count() EQUALS 2
    ASSERT cart.get_total_price() EQUALS 100.00
    ASSERT cart.contains_product(product.id) EQUALS true
  END
  
  TEST "should enforce maximum quantity limits"
    // Arrange
    product = CREATE_TEST_PRODUCT({max_quantity: 5})
    
    // Act & Assert
    cart.add_item(product, 3)  // Should succeed
    ASSERT cart.get_quantity_for_product(product.id) EQUALS 3
    
    EXPECT_EXCEPTION WHEN cart.add_item(product, 5)  // Would exceed limit
    OR
    result = cart.add_item(product, 5)
    ASSERT result.is_success() EQUALS false
    ASSERT result.get_error() CONTAINS "exceeds maximum quantity"
  END
END
```

---

## 👤 User Authentication Testing Patterns

### Authentication Service Testing

#### Service Layer Pattern
```pseudocode
DESCRIBE "Authentication Service"
  BEFORE_EACH
    mock_user_repository = CREATE_MOCK_USER_REPOSITORY()
    mock_password_hasher = CREATE_MOCK_PASSWORD_HASHER()
    auth_service = CREATE_AUTH_SERVICE(mock_user_repository, mock_password_hasher)
  END
  
  TEST "should authenticate user with valid credentials"
    // Arrange
    email = "<EMAIL>"
    password = "correct_password"
    hashed_password = "hashed_correct_password"
    user = CREATE_USER({email: email, password_hash: hashed_password})
    
    MOCK_REPOSITORY_RETURN(user) WHEN FIND_BY_EMAIL(email)
    MOCK_HASHER_RETURN(true) WHEN VERIFY_PASSWORD(password, hashed_password)
    
    // Act
    result = auth_service.authenticate(email, password)
    
    // Assert
    ASSERT result.is_success() EQUALS true
    ASSERT result.get_user() EQUALS user
    ASSERT result.get_token() IS_NOT_NULL
  END
  
  TEST "should reject authentication with invalid password"
    // Arrange
    email = "<EMAIL>"
    wrong_password = "wrong_password"
    user = CREATE_USER({email: email})
    
    MOCK_REPOSITORY_RETURN(user) WHEN FIND_BY_EMAIL(email)
    MOCK_HASHER_RETURN(false) WHEN VERIFY_PASSWORD(wrong_password, ANY)
    
    // Act
    result = auth_service.authenticate(email, wrong_password)
    
    // Assert
    ASSERT result.is_success() EQUALS false
    ASSERT result.get_error() EQUALS "Invalid credentials"
    ASSERT result.get_user() IS_NULL
  END
  
  TEST "should handle non-existent user"
    // Arrange
    email = "<EMAIL>"
    password = "any_password"
    
    MOCK_REPOSITORY_RETURN(null) WHEN FIND_BY_EMAIL(email)
    
    // Act
    result = auth_service.authenticate(email, password)
    
    // Assert
    ASSERT result.is_success() EQUALS false
    ASSERT result.get_error() EQUALS "Invalid credentials"
  END
END
```

### Session Management Testing

#### Token-Based Authentication
```pseudocode
DESCRIBE "JWT Token Service"
  BEFORE_EACH
    secret_key = "test_secret_key"
    token_service = CREATE_TOKEN_SERVICE(secret_key)
  END
  
  TEST "should generate valid token for user"
    // Arrange
    user = CREATE_USER({id: "USER-001", email: "<EMAIL>"})
    
    // Act
    token = token_service.generate_token(user)
    
    // Assert
    ASSERT token IS_NOT_NULL
    ASSERT token.length > 0
    
    // Verify token can be decoded
    decoded_payload = token_service.decode_token(token)
    ASSERT decoded_payload.user_id EQUALS "USER-001"
    ASSERT decoded_payload.email EQUALS "<EMAIL>"
  END
  
  TEST "should reject expired token"
    // Arrange
    user = CREATE_USER({id: "USER-001"})
    expired_token = token_service.generate_token(user, expiry: PAST_TIME)
    
    // Act
    result = token_service.validate_token(expired_token)
    
    // Assert
    ASSERT result.is_valid() EQUALS false
    ASSERT result.get_error() EQUALS "Token expired"
  END
  
  TEST "should reject tampered token"
    // Arrange
    user = CREATE_USER({id: "USER-001"})
    valid_token = token_service.generate_token(user)
    tampered_token = valid_token + "tampered"
    
    // Act
    result = token_service.validate_token(tampered_token)
    
    // Assert
    ASSERT result.is_valid() EQUALS false
    ASSERT result.get_error() CONTAINS "Invalid token"
  END
END
```

---

## 💳 Payment Processing Testing Patterns

### Payment Gateway Integration

#### External Service Mocking Pattern
```pseudocode
DESCRIBE "Payment Processing Service"
  BEFORE_EACH
    mock_payment_gateway = CREATE_MOCK_PAYMENT_GATEWAY()
    payment_service = CREATE_PAYMENT_SERVICE(mock_payment_gateway)
  END
  
  TEST "should process successful payment"
    // Arrange
    payment_request = CREATE_PAYMENT_REQUEST({
      amount: 99.99,
      currency: "USD",
      card_token: "valid_card_token"
    })
    
    gateway_response = CREATE_GATEWAY_RESPONSE({
      status: "SUCCESS",
      transaction_id: "TXN-12345",
      amount: 99.99
    })
    
    MOCK_GATEWAY_RETURN(gateway_response) WHEN PROCESS_PAYMENT(payment_request)
    
    // Act
    result = payment_service.process_payment(payment_request)
    
    // Assert
    ASSERT result.is_success() EQUALS true
    ASSERT result.get_transaction_id() EQUALS "TXN-12345"
    ASSERT result.get_amount() EQUALS 99.99
  END
  
  TEST "should handle declined payment"
    // Arrange
    payment_request = CREATE_PAYMENT_REQUEST({
      amount: 99.99,
      card_token: "declined_card_token"
    })
    
    gateway_response = CREATE_GATEWAY_RESPONSE({
      status: "DECLINED",
      error_code: "INSUFFICIENT_FUNDS",
      error_message: "Insufficient funds"
    })
    
    MOCK_GATEWAY_RETURN(gateway_response) WHEN PROCESS_PAYMENT(payment_request)
    
    // Act
    result = payment_service.process_payment(payment_request)
    
    // Assert
    ASSERT result.is_success() EQUALS false
    ASSERT result.get_error_code() EQUALS "INSUFFICIENT_FUNDS"
    ASSERT result.get_error_message() EQUALS "Insufficient funds"
  END
  
  TEST "should handle gateway timeout"
    // Arrange
    payment_request = CREATE_PAYMENT_REQUEST({amount: 99.99})
    
    MOCK_GATEWAY_THROW_TIMEOUT() WHEN PROCESS_PAYMENT(payment_request)
    
    // Act
    result = payment_service.process_payment(payment_request)
    
    // Assert
    ASSERT result.is_success() EQUALS false
    ASSERT result.get_error_type() EQUALS "TIMEOUT"
    ASSERT result.should_retry() EQUALS true
  END
END
```

---

## 🔧 API Testing Patterns

### RESTful API Testing

#### HTTP Endpoint Testing
```pseudocode
DESCRIBE "Products API Endpoint"
  BEFORE_EACH
    test_server = START_TEST_SERVER()
    test_database = CREATE_TEST_DATABASE()
    SEED_TEST_DATA(test_database)
  END
  
  TEST "GET /api/products should return product list"
    // Act
    response = HTTP_GET("/api/products")
    
    // Assert
    ASSERT response.status_code EQUALS 200
    ASSERT response.headers["Content-Type"] CONTAINS "application/json"
    
    products = PARSE_JSON(response.body)
    ASSERT products.length > 0
    ASSERT products[0] HAS_PROPERTIES ["id", "name", "price"]
  END
  
  TEST "GET /api/products/:id should return specific product"
    // Arrange
    product_id = "PROD-001"
    
    // Act
    response = HTTP_GET("/api/products/" + product_id)
    
    // Assert
    ASSERT response.status_code EQUALS 200
    
    product = PARSE_JSON(response.body)
    ASSERT product.id EQUALS product_id
    ASSERT product.name IS_NOT_NULL
    ASSERT product.price > 0
  END
  
  TEST "GET /api/products/:id should return 404 for non-existent product"
    // Arrange
    non_existent_id = "NON-EXISTENT"
    
    // Act
    response = HTTP_GET("/api/products/" + non_existent_id)
    
    // Assert
    ASSERT response.status_code EQUALS 404
    
    error = PARSE_JSON(response.body)
    ASSERT error.message CONTAINS "Product not found"
  END
  
  TEST "POST /api/products should create new product"
    // Arrange
    new_product = {
      name: "New Product",
      price: 149.99,
      category: "Electronics"
    }
    
    // Act
    response = HTTP_POST("/api/products", new_product)
    
    // Assert
    ASSERT response.status_code EQUALS 201
    
    created_product = PARSE_JSON(response.body)
    ASSERT created_product.id IS_NOT_NULL
    ASSERT created_product.name EQUALS "New Product"
    ASSERT created_product.price EQUALS 149.99
  END
  
  AFTER_EACH
    CLEANUP_TEST_DATABASE()
    STOP_TEST_SERVER()
  END
END
```

---

These technology-agnostic patterns can be adapted to any programming language or framework by translating the pseudocode into the specific syntax and testing tools of your chosen technology stack.
