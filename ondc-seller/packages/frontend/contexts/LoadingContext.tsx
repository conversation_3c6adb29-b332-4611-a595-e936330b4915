'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { PageTransitionLoader } from '@/components/skeletons/SkeletonBase';

interface LoadingState {
  isPageLoading: boolean;
  isContentLoading: boolean;
  loadingMessage: string;
  loadingProgress: number;
}

interface LoadingContextType {
  loading: LoadingState;
  setPageLoading: (loading: boolean, message?: string) => void;
  setContentLoading: (loading: boolean, message?: string) => void;
  setLoadingProgress: (progress: number) => void;
  startPageTransition: (message?: string) => void;
  finishPageTransition: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState<LoadingState>({
    isPageLoading: false,
    isContentLoading: false,
    loadingMessage: 'Loading...',
    loadingProgress: 0,
  });

  const pathname = usePathname();

  // Handle page transitions automatically
  useEffect(() => {
    setPageLoading(true, 'Loading page...');
    
    // Simulate loading time for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [pathname]);

  const setPageLoading = useCallback((isLoading: boolean, message = 'Loading...') => {
    setLoading(prev => ({
      ...prev,
      isPageLoading: isLoading,
      loadingMessage: message,
      loadingProgress: isLoading ? 0 : 100,
    }));
  }, []);

  const setContentLoading = useCallback((isLoading: boolean, message = 'Loading content...') => {
    setLoading(prev => ({
      ...prev,
      isContentLoading: isLoading,
      loadingMessage: message,
    }));
  }, []);

  const setLoadingProgress = useCallback((progress: number) => {
    setLoading(prev => ({
      ...prev,
      loadingProgress: Math.max(0, Math.min(100, progress)),
    }));
  }, []);

  const startPageTransition = useCallback((message = 'Loading...') => {
    setPageLoading(true, message);
    setLoadingProgress(0);
    
    // Simulate progressive loading
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 90) {
        clearInterval(interval);
        setLoadingProgress(90);
      } else {
        setLoadingProgress(progress);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [setPageLoading, setLoadingProgress]);

  const finishPageTransition = useCallback(() => {
    setLoadingProgress(100);
    setTimeout(() => {
      setPageLoading(false);
    }, 200);
  }, [setPageLoading, setLoadingProgress]);

  const value: LoadingContextType = {
    loading,
    setPageLoading,
    setContentLoading,
    setLoadingProgress,
    startPageTransition,
    finishPageTransition,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
      <PageTransitionLoader isLoading={loading.isPageLoading} />
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}

// Hook for managing async operations with loading states
export function useAsyncOperation() {
  const { setContentLoading } = useLoading();

  const executeWithLoading = useCallback(async <T>(
    operation: () => Promise<T>,
    loadingMessage = 'Processing...'
  ): Promise<T> => {
    try {
      setContentLoading(true, loadingMessage);
      const result = await operation();
      return result;
    } finally {
      setContentLoading(false);
    }
  }, [setContentLoading]);

  return { executeWithLoading };
}

// Hook for page navigation with loading states
export function usePageNavigation() {
  const { startPageTransition, finishPageTransition } = useLoading();

  const navigateWithLoading = useCallback(async (
    navigationFn: () => Promise<void> | void,
    message = 'Loading page...'
  ) => {
    const cleanup = startPageTransition(message);
    
    try {
      await navigationFn();
    } finally {
      cleanup();
      finishPageTransition();
    }
  }, [startPageTransition, finishPageTransition]);

  return { navigateWithLoading };
}

// Hook for data fetching with loading states
export function useDataFetching() {
  const { setContentLoading, setLoadingProgress } = useLoading();

  const fetchWithLoading = useCallback(async <T>(
    fetchFn: (onProgress?: (progress: number) => void) => Promise<T>,
    loadingMessage = 'Fetching data...'
  ): Promise<T> => {
    try {
      setContentLoading(true, loadingMessage);
      setLoadingProgress(0);

      const result = await fetchFn((progress) => {
        setLoadingProgress(progress);
      });

      setLoadingProgress(100);
      return result;
    } finally {
      setTimeout(() => {
        setContentLoading(false);
        setLoadingProgress(0);
      }, 200);
    }
  }, [setContentLoading, setLoadingProgress]);

  return { fetchWithLoading };
}

// Higher-order component for automatic loading states
export function withLoading<P extends object>(
  Component: React.ComponentType<P>,
  loadingComponent?: React.ComponentType
) {
  return function LoadingWrapper(props: P) {
    const { loading } = useLoading();
    
    if (loading.isContentLoading && loadingComponent) {
      const LoadingComponent = loadingComponent;
      return <LoadingComponent />;
    }

    return <Component {...props} />;
  };
}

// Loading state manager for complex operations
export class LoadingStateManager {
  private setLoading: (loading: boolean, message?: string) => void;
  private setProgress: (progress: number) => void;

  constructor(
    setLoading: (loading: boolean, message?: string) => void,
    setProgress: (progress: number) => void
  ) {
    this.setLoading = setLoading;
    this.setProgress = setProgress;
  }

  async executeSteps<T>(
    steps: Array<{
      name: string;
      execute: () => Promise<T>;
      weight?: number;
    }>
  ): Promise<T[]> {
    const totalWeight = steps.reduce((sum, step) => sum + (step.weight || 1), 0);
    let currentProgress = 0;
    const results: T[] = [];

    this.setLoading(true, 'Starting...');
    this.setProgress(0);

    try {
      for (const step of steps) {
        this.setLoading(true, step.name);

        const result = await step.execute();
        results.push(result);

        currentProgress += (step.weight || 1) / totalWeight * 100;
        this.setProgress(currentProgress);
      }

      this.setProgress(100);
      return results;
    } finally {
      setTimeout(() => {
        this.setLoading(false);
        this.setProgress(0);
      }, 200);
    }
  }
}

export default LoadingContext;
