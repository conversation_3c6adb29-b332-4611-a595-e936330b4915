#!/usr/bin/env node

/**
 * Category Hierarchy System Testing Script
 * 
 * This script tests the category-subcategory hierarchy implementation
 * when the Next.js server is running properly.
 */

const https = require('http');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const tests = [
  {
    name: 'Test Parent Categories API',
    url: '/api/categories?parentOnly=true&pageSize=10',
    expectedFields: ['success', 'data'],
    validation: (data) => {
      if (!data.success) return 'API call failed';
      if (!Array.isArray(data.data)) return 'Data is not an array';
      if (data.data.length === 0) return 'No parent categories found';
      
      // Check if categories have hierarchy fields
      const firstCategory = data.data[0];
      if (!firstCategory.hasOwnProperty('isSubcategory')) {
        return 'Missing isSubcategory field';
      }
      
      return null; // Success
    }
  },
  {
    name: 'Test All Categories API (for comparison)',
    url: '/api/categories?pageSize=20',
    expectedFields: ['success', 'data'],
    validation: (data) => {
      if (!data.success) return 'API call failed';
      if (!Array.isArray(data.data)) return 'Data is not an array';
      return null;
    }
  },
  {
    name: 'Test Subcategories API',
    url: '/api/categories/2/subcategories',
    expectedFields: ['success', 'data'],
    validation: (data) => {
      if (!data.success) return 'API call failed';
      if (!Array.isArray(data.data)) return 'Data is not an array';
      return null;
    }
  },
  {
    name: 'Test Category Detail API',
    url: '/api/categories/electronics',
    expectedFields: ['success', 'data'],
    validation: (data) => {
      if (!data.success) return 'API call failed';
      if (!data.data) return 'No category data found';
      return null;
    }
  }
];

// Test pages
const pageTests = [
  {
    name: 'Homepage (should show parent categories only)',
    url: '/',
    validation: (html) => {
      if (html.includes('Internal Server Error')) return 'Server error on homepage';
      if (!html.includes('Shop by Category')) return 'Homepage not loading properly';
      return null;
    }
  },
  {
    name: 'Categories Page (should show parent categories)',
    url: '/categories',
    validation: (html) => {
      if (html.includes('Internal Server Error')) return 'Server error on categories page';
      if (!html.includes('Shop by Category')) return 'Categories page not loading properly';
      return null;
    }
  }
];

// Utility function to make HTTP requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const fullUrl = `${BASE_URL}${url}`;
    console.log(`🚀 Testing: ${fullUrl}`);
    
    https.get(fullUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          // Try to parse as JSON first
          const jsonData = JSON.parse(data);
          resolve({ type: 'json', data: jsonData, raw: data });
        } catch (e) {
          // If not JSON, return as HTML
          resolve({ type: 'html', data: null, raw: data });
        }
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Run API tests
async function runAPITests() {
  console.log('\n🧪 **CATEGORY HIERARCHY API TESTS**\n');
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await makeRequest(test.url);
      
      if (result.type === 'json') {
        const error = test.validation(result.data);
        if (error) {
          console.log(`❌ ${test.name}: ${error}`);
        } else {
          console.log(`✅ ${test.name}: PASSED`);
          passedTests++;
        }
      } else {
        console.log(`❌ ${test.name}: Expected JSON, got HTML (server error)`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Request failed - ${error.message}`);
    }
  }
  
  console.log(`\n📊 API Tests: ${passedTests}/${totalTests} passed\n`);
  return passedTests === totalTests;
}

// Run page tests
async function runPageTests() {
  console.log('🌐 **CATEGORY HIERARCHY PAGE TESTS**\n');
  
  let passedTests = 0;
  let totalTests = pageTests.length;
  
  for (const test of pageTests) {
    try {
      const result = await makeRequest(test.url);
      
      const error = test.validation(result.raw);
      if (error) {
        console.log(`❌ ${test.name}: ${error}`);
      } else {
        console.log(`✅ ${test.name}: PASSED`);
        passedTests++;
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Request failed - ${error.message}`);
    }
  }
  
  console.log(`\n📊 Page Tests: ${passedTests}/${totalTests} passed\n`);
  return passedTests === totalTests;
}

// Main test runner
async function runAllTests() {
  console.log('🎯 **CATEGORY HIERARCHY SYSTEM TESTING**');
  console.log('==========================================\n');
  
  // Check if server is running
  try {
    await makeRequest('/');
    console.log('✅ Server is running on http://localhost:3000\n');
  } catch (error) {
    console.log('❌ Server is not running on http://localhost:3000');
    console.log('Please start the Next.js development server first:\n');
    console.log('   npm run dev\n');
    process.exit(1);
  }
  
  // Run tests
  const apiTestsPass = await runAPITests();
  const pageTestsPass = await runPageTests();
  
  // Summary
  console.log('🎉 **TEST SUMMARY**');
  console.log('==================');
  
  if (apiTestsPass && pageTestsPass) {
    console.log('✅ All tests passed! Category hierarchy system is working correctly.');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
  }
  
  console.log('\n📋 **EXPECTED BEHAVIOR**');
  console.log('- Homepage: Shows only parent categories (no subcategories)');
  console.log('- Categories page: Shows parent categories with subcategory info');
  console.log('- Category detail pages: Show products from all subcategories');
  console.log('- Subcategory filtering: Works with clickable badges');
  console.log('- API endpoints: Support parentOnly parameter and hierarchy');
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  runAPITests,
  runPageTests,
  makeRequest
};
