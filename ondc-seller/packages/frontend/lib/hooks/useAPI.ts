/**
 * React Hooks for API Integration
 * Provides easy-to-use hooks for API operations with loading states
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAsyncOperation } from '@/contexts/LoadingContext';
import {
  adminProductsAPI,
  adminOrdersAPI,
  adminCustomersAPI,
  adminCollectionsAPI,
  analyticsAPI,
  APIListResponse,
  APIItemResponse,
  ListParams
} from '../api';

export interface UseAPIOptions {
  immediate?: boolean;
  cache?: boolean;
  cacheTTL?: number;
}

export interface UseAPIState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  refresh: () => Promise<void>;
}

export interface UseAPIListState<T> extends UseAPIState<APIListResponse<T>> {
  loadMore: () => Promise<void>;
  hasMore: boolean;
  page: number;
  setPage: (page: number) => void;
}

/**
 * Base hook for API operations
 */
function useAPIBase<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = [],
  options: UseAPIOptions = {}
): UseAPIState<T> {
  const { immediate = true, cache = false, cacheTTL = 300000 } = options;
  const { executeWithLoading } = useAsyncOperation();
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const cacheRef = useRef<Map<string, { data: T; timestamp: number }>>(new Map());

  const cacheKey = JSON.stringify(dependencies);

  const fetchData = useCallback(async () => {
    try {
      setError(null);
      
      // Check cache first
      if (cache) {
        const cached = cacheRef.current.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < cacheTTL) {
          setData(cached.data);
          return;
        }
      }

      setLoading(true);
      const result = await executeWithLoading(apiCall, 'Loading...');
      
      setData(result);
      
      // Cache the result
      if (cache) {
        cacheRef.current.set(cacheKey, { data: result, timestamp: Date.now() });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [apiCall, executeWithLoading, cache, cacheKey, cacheTTL]);

  const refresh = useCallback(async () => {
    // Clear cache and refetch
    if (cache) {
      cacheRef.current.delete(cacheKey);
    }
    await fetchData();
  }, [fetchData, cache, cacheKey]);

  useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, dependencies);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    refresh
  };
}

/**
 * Hook for paginated API operations
 */
function useAPIList<T>(
  apiCall: (params: ListParams) => Promise<APIListResponse<T>>,
  initialParams: ListParams = {},
  options: UseAPIOptions = {}
): UseAPIListState<T> {
  const [params, setParams] = useState<ListParams>({ page: 1, limit: 20, ...initialParams });
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);

  const baseState = useAPIBase(
    () => apiCall(params),
    [params],
    options
  );

  useEffect(() => {
    if (baseState.data) {
      if (params.page === 1) {
        // First page - replace all data
        setAllData(baseState.data.data);
      } else {
        // Subsequent pages - append data
        setAllData(prev => [...prev, ...baseState.data!.data]);
      }
      setHasMore(baseState.data.hasMore);
    }
  }, [baseState.data, params.page]);

  const loadMore = useCallback(async () => {
    if (hasMore && !baseState.loading) {
      setParams(prev => ({ ...prev, page: (prev.page || 1) + 1 }));
    }
  }, [hasMore, baseState.loading]);

  const setPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  const refresh = useCallback(async () => {
    setParams(prev => ({ ...prev, page: 1 }));
    setAllData([]);
    await baseState.refresh();
  }, [baseState.refresh]);

  return {
    ...baseState,
    data: baseState.data ? { ...baseState.data, data: allData } : null,
    loadMore,
    hasMore,
    page: params.page || 1,
    setPage,
    refresh
  };
}

// ============================================================================
// Products Hooks
// ============================================================================

export function useProducts(params: ListParams = {}, options?: UseAPIOptions) {
  return useAPIList(
    (p) => adminProductsAPI.getProducts(p),
    params,
    options
  );
}

export function useProduct(id: string, options?: UseAPIOptions) {
  return useAPIBase(
    () => adminProductsAPI.getProduct(id),
    [id],
    options
  );
}

export function useCreateProduct() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (data: any) => {
    return executeWithLoading(
      () => adminProductsAPI.createProduct(data),
      'Creating product...'
    );
  }, [executeWithLoading]);
}

export function useUpdateProduct() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string, data: any) => {
    return executeWithLoading(
      () => adminProductsAPI.updateProduct(id, data),
      'Updating product...'
    );
  }, [executeWithLoading]);
}

export function useDeleteProduct() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string) => {
    return executeWithLoading(
      () => adminProductsAPI.deleteProduct(id),
      'Deleting product...'
    );
  }, [executeWithLoading]);
}

// ============================================================================
// Orders Hooks
// ============================================================================

export function useOrders(params: ListParams = {}, options?: UseAPIOptions) {
  return useAPIList(
    (p) => adminOrdersAPI.getOrders(p),
    params,
    options
  );
}

export function useOrder(id: string, options?: UseAPIOptions) {
  return useAPIBase(
    () => adminOrdersAPI.getOrder(id),
    [id],
    options
  );
}

export function useUpdateOrder() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string, data: any) => {
    return executeWithLoading(
      () => adminOrdersAPI.updateOrder(id, data),
      'Updating order...'
    );
  }, [executeWithLoading]);
}

export function useCancelOrder() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string) => {
    return executeWithLoading(
      () => adminOrdersAPI.cancelOrder(id),
      'Canceling order...'
    );
  }, [executeWithLoading]);
}

// ============================================================================
// Customers Hooks
// ============================================================================

export function useCustomers(params: ListParams = {}, options?: UseAPIOptions) {
  return useAPIList(
    (p) => adminCustomersAPI.getCustomers(p),
    params,
    options
  );
}

export function useCustomer(id: string, options?: UseAPIOptions) {
  return useAPIBase(
    () => adminCustomersAPI.getCustomer(id),
    [id],
    options
  );
}

export function useCreateCustomer() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (data: any) => {
    return executeWithLoading(
      () => adminCustomersAPI.createCustomer(data),
      'Creating customer...'
    );
  }, [executeWithLoading]);
}

export function useUpdateCustomer() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string, data: any) => {
    return executeWithLoading(
      () => adminCustomersAPI.updateCustomer(id, data),
      'Updating customer...'
    );
  }, [executeWithLoading]);
}

export function useDeleteCustomer() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string) => {
    return executeWithLoading(
      () => adminCustomersAPI.deleteCustomer(id),
      'Deleting customer...'
    );
  }, [executeWithLoading]);
}

// ============================================================================
// Collections Hooks
// ============================================================================

export function useCollections(params: ListParams = {}, options?: UseAPIOptions) {
  return useAPIList(
    (p) => adminCollectionsAPI.getCollections(p),
    params,
    options
  );
}

export function useCollection(id: string, options?: UseAPIOptions) {
  return useAPIBase(
    () => adminCollectionsAPI.getCollection(id),
    [id],
    options
  );
}

export function useCreateCollection() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (data: any) => {
    return executeWithLoading(
      () => adminCollectionsAPI.createCollection(data),
      'Creating collection...'
    );
  }, [executeWithLoading]);
}

export function useUpdateCollection() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string, data: any) => {
    return executeWithLoading(
      () => adminCollectionsAPI.updateCollection(id, data),
      'Updating collection...'
    );
  }, [executeWithLoading]);
}

export function useDeleteCollection() {
  const { executeWithLoading } = useAsyncOperation();
  
  return useCallback(async (id: string) => {
    return executeWithLoading(
      () => adminCollectionsAPI.deleteCollection(id),
      'Deleting collection...'
    );
  }, [executeWithLoading]);
}

// ============================================================================
// Analytics Hooks
// ============================================================================

export function useDashboardAnalytics(period: string = 'this_month', options?: UseAPIOptions) {
  return useAPIBase(
    () => analyticsAPI.getDashboardAnalytics(period as any),
    [period],
    { cache: true, cacheTTL: 60000, ...options } // Cache for 1 minute
  );
}

export function useSalesAnalytics(params: any, options?: UseAPIOptions) {
  return useAPIBase(
    () => analyticsAPI.getSalesAnalytics(params),
    [params],
    { cache: true, cacheTTL: 60000, ...options }
  );
}

export function useCustomerAnalytics(params: any, options?: UseAPIOptions) {
  return useAPIBase(
    () => analyticsAPI.getCustomerAnalytics(params),
    [params],
    { cache: true, cacheTTL: 60000, ...options }
  );
}
