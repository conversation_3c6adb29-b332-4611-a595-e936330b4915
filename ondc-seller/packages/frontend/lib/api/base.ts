/**
 * Base API Service Class
 * Provides common functionality for all API services
 */

import { APIClient, APIResponse, APIClientError } from '../api-client';
import { useAsyncOperation } from '@/contexts/LoadingContext';

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface SearchParams {
  q?: string;
  search?: string;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  status?: string | string[];
  category?: string | string[];
  tags?: string[];
  [key: string]: any;
}

export interface ListParams extends PaginationParams, SearchParams, SortParams, FilterParams {}

export interface APIListResponse<T> {
  data: T[];
  count: number;
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface APIItemResponse<T> {
  data: T;
}

/**
 * Base API Service
 */
export abstract class BaseAPIService {
  protected client: APIClient;
  protected category: string;

  constructor(category: string, client?: APIClient) {
    this.category = category;
    this.client = client || new APIClient();
  }

  /**
   * Handle API errors with user-friendly messages
   */
  protected handleError(error: any, operation: string): never {
    console.error(`[${this.category}] ${operation} failed:`, error);
    
    if (error instanceof APIClientError) {
      throw new Error(`${operation} failed: ${error.message}`);
    }
    
    if (error.response?.data?.error) {
      throw new Error(`${operation} failed: ${error.response.data.error}`);
    }
    
    throw new Error(`${operation} failed: ${error.message || 'Unknown error'}`);
  }

  /**
   * Transform API response to standardized format
   */
  protected transformListResponse<T>(response: APIResponse): APIListResponse<T> {
    const data = response.data;
    
    // Handle different response formats
    if (Array.isArray(data)) {
      return {
        data,
        count: data.length,
        total: data.length,
        page: 1,
        limit: data.length,
        hasMore: false
      };
    }
    
    if (data && typeof data === 'object') {
      return {
        data: data.products || data.orders || data.customers || data.items || [],
        count: data.count || 0,
        total: data.total || data.count || 0,
        page: data.page || 1,
        limit: data.limit || 20,
        hasMore: data.hasMore || false
      };
    }
    
    return {
      data: [],
      count: 0,
      total: 0,
      page: 1,
      limit: 20,
      hasMore: false
    };
  }

  /**
   * Transform API response to standardized item format
   */
  protected transformItemResponse<T>(response: APIResponse): APIItemResponse<T> {
    return {
      data: response.data
    };
  }

  /**
   * Convert pagination params to API format
   */
  protected convertPaginationParams(params: PaginationParams) {
    const { page = 1, limit = 20 } = params;
    return {
      limit,
      offset: (page - 1) * limit
    };
  }

  /**
   * Execute API call with loading state
   */
  protected async executeWithLoading<T>(
    operation: () => Promise<APIResponse<T>>,
    loadingMessage: string
  ): Promise<T> {
    try {
      const response = await operation();
      return response.data;
    } catch (error) {
      this.handleError(error, loadingMessage);
    }
  }

  /**
   * Generic list method
   */
  protected async list<T>(
    endpointName: string,
    params: ListParams = {}
  ): Promise<APIListResponse<T>> {
    try {
      const apiParams = {
        ...this.convertPaginationParams(params),
        ...params
      };
      
      const response = await this.client.callEndpoint(
        this.category,
        endpointName,
        apiParams
      );
      
      return this.transformListResponse<T>(response);
    } catch (error) {
      this.handleError(error, 'List operation');
    }
  }

  /**
   * Generic get method
   */
  protected async get<T>(
    endpointName: string,
    id: string,
    params: Record<string, any> = {}
  ): Promise<APIItemResponse<T>> {
    try {
      const response = await this.client.callEndpoint(
        this.category,
        endpointName,
        { id, ...params }
      );
      
      return this.transformItemResponse<T>(response);
    } catch (error) {
      this.handleError(error, 'Get operation');
    }
  }

  /**
   * Generic create method
   */
  protected async create<T>(
    endpointName: string,
    data: Record<string, any>
  ): Promise<APIItemResponse<T>> {
    try {
      const response = await this.client.callEndpoint(
        this.category,
        endpointName,
        {},
        data
      );
      
      return this.transformItemResponse<T>(response);
    } catch (error) {
      this.handleError(error, 'Create operation');
    }
  }

  /**
   * Generic update method
   */
  protected async update<T>(
    endpointName: string,
    id: string,
    data: Record<string, any>
  ): Promise<APIItemResponse<T>> {
    try {
      const response = await this.client.callEndpoint(
        this.category,
        endpointName,
        { id },
        data
      );
      
      return this.transformItemResponse<T>(response);
    } catch (error) {
      this.handleError(error, 'Update operation');
    }
  }

  /**
   * Generic delete method
   */
  protected async delete(
    endpointName: string,
    id: string
  ): Promise<{ success: boolean }> {
    try {
      await this.client.callEndpoint(
        this.category,
        endpointName,
        { id }
      );
      
      return { success: true };
    } catch (error) {
      this.handleError(error, 'Delete operation');
    }
  }
}

/**
 * API Service Factory
 */
export class APIServiceFactory {
  private static client: APIClient;
  private static services: Map<string, BaseAPIService> = new Map();

  static setClient(client: APIClient) {
    this.client = client;
  }

  static getClient(): APIClient {
    if (!this.client) {
      this.client = new APIClient();
    }
    return this.client;
  }

  static registerService<T extends BaseAPIService>(
    name: string,
    serviceClass: new (client: APIClient) => T
  ): T {
    const service = new serviceClass(this.getClient());
    this.services.set(name, service);
    return service;
  }

  static getService<T extends BaseAPIService>(name: string): T {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service '${name}' not found. Make sure to register it first.`);
    }
    return service as T;
  }
}

/**
 * API Response Cache
 */
export class APICache {
  private static cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();

  static set(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  static get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static clear(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  static generateKey(category: string, endpoint: string, params: any = {}): string {
    return `${category}:${endpoint}:${JSON.stringify(params)}`;
  }
}
