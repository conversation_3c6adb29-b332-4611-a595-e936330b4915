/**
 * Orders API Service
 * Handles all order-related API operations
 */

import { BaseAPIService, APIListResponse, APIItemResponse, ListParams } from './base';
import { APIClient, apiClient } from '../api-client';

export interface Order {
  id: string;
  display_id: number;
  status: 'pending' | 'completed' | 'archived' | 'canceled' | 'requires_action';
  fulfillment_status:
    | 'not_fulfilled'
    | 'partially_fulfilled'
    | 'fulfilled'
    | 'partially_shipped'
    | 'shipped'
    | 'partially_returned'
    | 'returned'
    | 'canceled'
    | 'requires_action';
  payment_status:
    | 'not_paid'
    | 'awaiting'
    | 'captured'
    | 'partially_refunded'
    | 'refunded'
    | 'canceled'
    | 'requires_action';
  customer_id: string;
  customer: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  };
  email: string;
  billing_address: Address;
  shipping_address: Address;
  region_id: string;
  region: {
    id: string;
    name: string;
    currency_code: string;
  };
  currency_code: string;
  tax_rate?: number;
  items: OrderItem[];
  shipping_methods: ShippingMethod[];
  payments: Payment[];
  fulfillments: Fulfillment[];
  returns: Return[];
  claims: Claim[];
  swaps: Swap[];
  discounts: Discount[];
  gift_cards: GiftCard[];
  subtotal: number;
  discount_total: number;
  shipping_total: number;
  tax_total: number;
  gift_card_total: number;
  total: number;
  paid_total: number;
  refunded_total: number;
  created_at: string;
  updated_at: string;
  canceled_at?: string;
  metadata?: Record<string, any>;
}

export interface OrderItem {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  is_return: boolean;
  is_giftcard: boolean;
  should_merge: boolean;
  allow_discounts: boolean;
  has_shipping: boolean;
  unit_price: number;
  variant_id: string;
  variant: {
    id: string;
    title: string;
    sku?: string;
    barcode?: string;
    product: {
      id: string;
      title: string;
      thumbnail?: string;
    };
  };
  quantity: number;
  fulfilled_quantity: number;
  returned_quantity: number;
  shipped_quantity: number;
  refundable_amount: number;
  subtotal: number;
  tax_total: number;
  total: number;
  original_total: number;
  original_item_id?: string;
  order_id: string;
  swap_id?: string;
  claim_order_id?: string;
  return_id?: string;
  fulfillment_id?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface Address {
  id: string;
  customer_id?: string;
  company?: string;
  first_name?: string;
  last_name?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province?: string;
  postal_code: string;
  phone?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ShippingMethod {
  id: string;
  shipping_option_id: string;
  order_id: string;
  shipping_option: {
    id: string;
    name: string;
    region_id: string;
    profile_id: string;
    provider_id: string;
    price_type: 'flat_rate' | 'calculated';
    amount?: number;
    is_return: boolean;
    admin_only: boolean;
    data: Record<string, any>;
  };
  tax_lines: TaxLine[];
  price: number;
  data: Record<string, any>;
  includes_tax: boolean;
  subtotal: number;
  total: number;
  tax_total: number;
}

export interface Payment {
  id: string;
  swap_id?: string;
  cart_id?: string;
  order_id?: string;
  amount: number;
  currency_code: string;
  amount_refunded: number;
  provider_id: string;
  data: Record<string, any>;
  captured_at?: string;
  canceled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Fulfillment {
  id: string;
  claim_order_id?: string;
  swap_id?: string;
  order_id: string;
  provider_id: string;
  location_id?: string;
  shipped_at?: string;
  canceled_at?: string;
  data: Record<string, any>;
  items: FulfillmentItem[];
  tracking_links: TrackingLink[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface FulfillmentItem {
  fulfillment_id: string;
  item_id: string;
  quantity: number;
}

export interface TrackingLink {
  id: string;
  url: string;
  tracking_number: string;
  fulfillment_id: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface TaxLine {
  id: string;
  rate: number;
  name: string;
  code?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface Return {
  id: string;
  status: 'requested' | 'received' | 'requires_action';
  items: ReturnItem[];
  swap_id?: string;
  claim_order_id?: string;
  order_id: string;
  shipping_method?: ShippingMethod;
  shipping_data?: Record<string, any>;
  location_id?: string;
  refund_amount: number;
  no_notification?: boolean;
  idempotency_key?: string;
  received_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ReturnItem {
  return_id: string;
  item_id: string;
  quantity: number;
  is_requested: boolean;
  requested_quantity?: number;
  received_quantity?: number;
  reason_id?: string;
  note?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface Claim {
  id: string;
  type: 'refund' | 'replace';
  payment_status: 'na' | 'not_refunded' | 'refunded';
  fulfillment_status:
    | 'not_fulfilled'
    | 'partially_fulfilled'
    | 'fulfilled'
    | 'partially_shipped'
    | 'shipped'
    | 'partially_returned'
    | 'returned'
    | 'canceled'
    | 'requires_action';
  claim_items: ClaimItem[];
  additional_items: OrderItem[];
  order_id: string;
  return_order?: Return;
  shipping_address_id?: string;
  shipping_methods: ShippingMethod[];
  fulfillments: Fulfillment[];
  refund_amount?: number;
  canceled_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ClaimItem {
  id: string;
  images: ClaimImage[];
  claim_order_id: string;
  item_id: string;
  variant_id: string;
  reason: 'missing_item' | 'wrong_item' | 'production_failure' | 'other';
  note?: string;
  quantity: number;
  tags: ClaimTag[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ClaimImage {
  id: string;
  claim_item_id: string;
  url: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ClaimTag {
  id: string;
  value: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface Swap {
  id: string;
  fulfillment_status:
    | 'not_fulfilled'
    | 'fulfilled'
    | 'shipped'
    | 'partially_shipped'
    | 'canceled'
    | 'requires_action';
  payment_status:
    | 'not_paid'
    | 'awaiting'
    | 'captured'
    | 'confirmed'
    | 'canceled'
    | 'difference_refunded'
    | 'partially_refunded'
    | 'refunded'
    | 'requires_action';
  order_id: string;
  additional_items: OrderItem[];
  return_order: Return;
  fulfillments: Fulfillment[];
  payment?: Payment;
  difference_due?: number;
  shipping_address_id?: string;
  shipping_methods: ShippingMethod[];
  cart_id?: string;
  confirmed_at?: string;
  canceled_at?: string;
  no_notification?: boolean;
  allow_backorder: boolean;
  idempotency_key?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface Discount {
  id: string;
  code: string;
  is_dynamic: boolean;
  rule_id: string;
  is_disabled: boolean;
  parent_discount_id?: string;
  starts_at: string;
  ends_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface GiftCard {
  id: string;
  code: string;
  value: number;
  balance: number;
  region_id: string;
  order_id?: string;
  is_disabled: boolean;
  ends_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface OrderListParams extends ListParams {
  status?: string | string[];
  fulfillment_status?: string | string[];
  payment_status?: string | string[];
  customer_id?: string;
  region_id?: string;
  currency_code?: string;
  created_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  updated_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
}

/**
 * Orders API Service (Store)
 */
export class OrdersAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('orders', client);
  }

  /**
   * Get customer orders
   */
  async getOrders(params: OrderListParams = {}): Promise<APIListResponse<Order>> {
    return this.list<Order>('list', params);
  }

  /**
   * Get single order
   */
  async getOrder(id: string): Promise<APIItemResponse<Order>> {
    return this.get<Order>('get', id);
  }
}

/**
 * Admin Orders API Service
 */
export class AdminOrdersAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminOrders', client);
  }

  /**
   * Get all orders (admin)
   */
  async getOrders(params: OrderListParams = {}): Promise<APIListResponse<Order>> {
    return this.list<Order>('list', params);
  }

  /**
   * Get single order (admin)
   */
  async getOrder(id: string): Promise<APIItemResponse<Order>> {
    return this.get<Order>('get', id);
  }

  /**
   * Update order
   */
  async updateOrder(id: string, data: Partial<Order>): Promise<APIItemResponse<Order>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Order>(response);
    } catch (error) {
      this.handleError(error, 'Update order');
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(id: string): Promise<APIItemResponse<Order>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}/cancel`,
        {
          method: 'POST',
        },
        'admin'
      );

      return this.transformItemResponse<Order>(response);
    } catch (error) {
      this.handleError(error, 'Cancel order');
    }
  }

  /**
   * Capture payment
   */
  async capturePayment(id: string, amount?: number): Promise<APIItemResponse<Order>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}/capture`,
        {
          method: 'POST',
          body: JSON.stringify({ amount }),
        },
        'admin'
      );

      return this.transformItemResponse<Order>(response);
    } catch (error) {
      this.handleError(error, 'Capture payment');
    }
  }

  /**
   * Create fulfillment
   */
  async createFulfillment(
    id: string,
    data: {
      items: Array<{ item_id: string; quantity: number }>;
      location_id?: string;
      no_notification?: boolean;
      metadata?: Record<string, any>;
    }
  ): Promise<APIItemResponse<Order>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}/fulfillment`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Order>(response);
    } catch (error) {
      this.handleError(error, 'Create fulfillment');
    }
  }

  /**
   * Create shipment
   */
  async createShipment(
    id: string,
    fulfillmentId: string,
    trackingNumbers?: string[]
  ): Promise<APIItemResponse<Order>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}/shipment`,
        {
          method: 'POST',
          body: JSON.stringify({
            fulfillment_id: fulfillmentId,
            tracking_numbers: trackingNumbers,
          }),
        },
        'admin'
      );

      return this.transformItemResponse<Order>(response);
    } catch (error) {
      this.handleError(error, 'Create shipment');
    }
  }

  /**
   * Request return
   */
  async requestReturn(
    id: string,
    data: {
      items: Array<{
        item_id: string;
        quantity: number;
        reason_id?: string;
        note?: string;
      }>;
      return_shipping?: {
        option_id: string;
        price?: number;
      };
      note?: string;
      receive_now?: boolean;
      no_notification?: boolean;
      refund?: number;
    }
  ): Promise<APIItemResponse<Return>> {
    try {
      const response = await this.client.request(
        `/admin/orders/${id}/return`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Return>(response);
    } catch (error) {
      this.handleError(error, 'Request return');
    }
  }
}

// Export service instances using the frontend API client
export const ordersAPI = new OrdersAPIService(apiClient);
export const adminOrdersAPI = new AdminOrdersAPIService(apiClient);
