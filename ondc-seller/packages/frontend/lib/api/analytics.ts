/**
 * Analytics API Service
 * Handles all analytics and reporting API operations
 */

import { BaseAPIService, APIItemResponse } from './base';
import { APIClient, apiClient } from '../api-client';

export interface DashboardAnalytics {
  sales: SalesAnalytics;
  orders: OrderAnalytics;
  customers: CustomerAnalytics;
  products: ProductAnalytics;
  traffic: TrafficAnalytics;
  revenue: RevenueAnalytics;
}

export interface SalesAnalytics {
  total_sales: number;
  sales_today: number;
  sales_this_week: number;
  sales_this_month: number;
  sales_this_year: number;
  sales_growth: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  sales_by_period: Array<{
    period: string;
    sales: number;
    orders: number;
  }>;
}

export interface OrderAnalytics {
  total_orders: number;
  orders_today: number;
  orders_this_week: number;
  orders_this_month: number;
  orders_this_year: number;
  order_growth: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  orders_by_status: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  average_order_value: number;
  fulfillment_rate: number;
}

export interface CustomerAnalytics {
  total_customers: number;
  new_customers_today: number;
  new_customers_this_week: number;
  new_customers_this_month: number;
  new_customers_this_year: number;
  customer_growth: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  customer_retention_rate: number;
  customer_lifetime_value: number;
  repeat_customer_rate: number;
}

export interface ProductAnalytics {
  total_products: number;
  products_in_stock: number;
  products_out_of_stock: number;
  low_stock_products: number;
  top_selling_products: Array<{
    id: string;
    title: string;
    sales: number;
    revenue: number;
    units_sold: number;
  }>;
  product_performance: Array<{
    id: string;
    title: string;
    views: number;
    conversions: number;
    conversion_rate: number;
  }>;
}

export interface TrafficAnalytics {
  total_visitors: number;
  visitors_today: number;
  visitors_this_week: number;
  visitors_this_month: number;
  page_views: number;
  bounce_rate: number;
  average_session_duration: number;
  traffic_sources: Array<{
    source: string;
    visitors: number;
    percentage: number;
  }>;
  device_breakdown: Array<{
    device: string;
    visitors: number;
    percentage: number;
  }>;
  browser_breakdown: Array<{
    browser: string;
    visitors: number;
    percentage: number;
  }>;
}

export interface RevenueAnalytics {
  total_revenue: number;
  revenue_today: number;
  revenue_this_week: number;
  revenue_this_month: number;
  revenue_this_year: number;
  revenue_growth: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  revenue_by_period: Array<{
    period: string;
    revenue: number;
    profit: number;
    margin: number;
  }>;
  revenue_by_product: Array<{
    product_id: string;
    product_title: string;
    revenue: number;
    percentage: number;
  }>;
  revenue_by_region: Array<{
    region: string;
    revenue: number;
    percentage: number;
  }>;
}

export interface SalesReport {
  period: string;
  start_date: string;
  end_date: string;
  summary: {
    total_sales: number;
    total_orders: number;
    average_order_value: number;
    total_customers: number;
    new_customers: number;
    returning_customers: number;
  };
  sales_by_day: Array<{
    date: string;
    sales: number;
    orders: number;
    customers: number;
  }>;
  top_products: Array<{
    id: string;
    title: string;
    units_sold: number;
    revenue: number;
  }>;
  top_customers: Array<{
    id: string;
    name: string;
    email: string;
    total_spent: number;
    orders_count: number;
  }>;
}

export interface InventoryReport {
  total_products: number;
  total_variants: number;
  in_stock: number;
  out_of_stock: number;
  low_stock: number;
  low_stock_threshold: number;
  inventory_value: number;
  products: Array<{
    id: string;
    title: string;
    sku: string;
    quantity: number;
    reserved_quantity: number;
    available_quantity: number;
    value: number;
    status: 'in_stock' | 'out_of_stock' | 'low_stock';
  }>;
}

export interface CustomerReport {
  period: string;
  start_date: string;
  end_date: string;
  summary: {
    total_customers: number;
    new_customers: number;
    active_customers: number;
    customer_retention_rate: number;
    customer_lifetime_value: number;
  };
  customer_acquisition: Array<{
    date: string;
    new_customers: number;
    acquisition_cost: number;
  }>;
  customer_segments: Array<{
    segment: string;
    count: number;
    percentage: number;
    average_order_value: number;
  }>;
  top_customers: Array<{
    id: string;
    name: string;
    email: string;
    total_spent: number;
    orders_count: number;
    last_order_date: string;
  }>;
}

export type ReportPeriod =
  | 'today'
  | 'yesterday'
  | 'this_week'
  | 'last_week'
  | 'this_month'
  | 'last_month'
  | 'this_year'
  | 'last_year'
  | 'custom';

export interface ReportParams {
  period: ReportPeriod;
  start_date?: string;
  end_date?: string;
  timezone?: string;
}

/**
 * Analytics API Service
 */
export class AnalyticsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('analytics', client);
  }

  /**
   * Get dashboard analytics
   */
  async getDashboardAnalytics(period: ReportPeriod = 'this_month'): Promise<DashboardAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/dashboard',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get dashboard analytics');
    }
  }

  /**
   * Get sales analytics
   */
  async getSalesAnalytics(params: ReportParams): Promise<SalesAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/sales',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get sales analytics');
    }
  }

  /**
   * Get order analytics
   */
  async getOrderAnalytics(params: ReportParams): Promise<OrderAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/orders',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get order analytics');
    }
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(params: ReportParams): Promise<CustomerAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/customers',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get customer analytics');
    }
  }

  /**
   * Get product analytics
   */
  async getProductAnalytics(params: ReportParams): Promise<ProductAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/products',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get product analytics');
    }
  }

  /**
   * Get traffic analytics
   */
  async getTrafficAnalytics(params: ReportParams): Promise<TrafficAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/traffic',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get traffic analytics');
    }
  }

  /**
   * Get revenue analytics
   */
  async getRevenueAnalytics(params: ReportParams): Promise<RevenueAnalytics> {
    try {
      const response = await this.client.request(
        '/admin/analytics/revenue',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get revenue analytics');
    }
  }

  /**
   * Generate sales report
   */
  async generateSalesReport(params: ReportParams): Promise<SalesReport> {
    try {
      const response = await this.client.request(
        '/admin/reports/sales',
        {
          method: 'POST',
          body: JSON.stringify(params),
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Generate sales report');
    }
  }

  /**
   * Generate inventory report
   */
  async generateInventoryReport(): Promise<InventoryReport> {
    try {
      const response = await this.client.request(
        '/admin/reports/inventory',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Generate inventory report');
    }
  }

  /**
   * Generate customer report
   */
  async generateCustomerReport(params: ReportParams): Promise<CustomerReport> {
    try {
      const response = await this.client.request(
        '/admin/reports/customers',
        {
          method: 'POST',
          body: JSON.stringify(params),
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Generate customer report');
    }
  }

  /**
   * Export report
   */
  async exportReport(
    type: 'sales' | 'inventory' | 'customers',
    format: 'csv' | 'xlsx' | 'pdf',
    params: ReportParams
  ): Promise<{ downloadUrl: string }> {
    try {
      const response = await this.client.request(
        `/admin/reports/${type}/export`,
        {
          method: 'POST',
          body: JSON.stringify({ ...params, format }),
        },
        'admin'
      );

      return { downloadUrl: response.data.downloadUrl };
    } catch (error) {
      this.handleError(error, 'Export report');
    }
  }
}

// Export service instance using the frontend API client
export const analyticsAPI = new AnalyticsAPIService(apiClient);
