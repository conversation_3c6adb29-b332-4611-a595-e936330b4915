/**
 * Customers API Service
 * Handles all customer-related API operations
 */

import { BaseAPIService, APIListResponse, APIItemResponse, ListParams } from './base';
import { APIClient, apiClient } from '../api-client';
import { Address } from './orders';

export interface Customer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  billing_address_id?: string;
  billing_address?: Address;
  shipping_addresses: Address[];
  phone?: string;
  has_account: boolean;
  orders?: CustomerOrder[];
  groups: CustomerGroup[];
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  metadata?: Record<string, any>;
}

export interface CustomerOrder {
  id: string;
  display_id: number;
  status: string;
  fulfillment_status: string;
  payment_status: string;
  total: number;
  currency_code: string;
  created_at: string;
}

export interface CustomerGroup {
  id: string;
  name: string;
  customers?: Customer[];
  price_lists: PriceList[];
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  metadata?: Record<string, any>;
}

export interface PriceList {
  id: string;
  name: string;
  description: string;
  type: 'sale' | 'override';
  status: 'active' | 'draft';
  starts_at?: string;
  ends_at?: string;
  customer_groups: CustomerGroup[];
  prices: Price[];
  includes_tax: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Price {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id: string;
  variant_id: string;
  region_id?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CreateCustomerData {
  email: string;
  first_name: string;
  last_name: string;
  password?: string;
  phone?: string;
  metadata?: Record<string, any>;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  billing_address?: Partial<Address>;
  shipping_addresses?: Partial<Address>[];
}

export interface CustomerListParams extends ListParams {
  groups?: string[];
  created_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  updated_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  has_account?: boolean;
}

export interface CustomerStats {
  total_customers: number;
  new_customers_today: number;
  new_customers_this_week: number;
  new_customers_this_month: number;
  active_customers: number;
  customers_with_orders: number;
  average_order_value: number;
  total_customer_value: number;
}

/**
 * Admin Customers API Service
 */
export class AdminCustomersAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminCustomers', client);
  }

  /**
   * Get all customers (admin)
   */
  async getCustomers(params: CustomerListParams = {}): Promise<APIListResponse<Customer>> {
    return this.list<Customer>('list', params);
  }

  /**
   * Get single customer (admin)
   */
  async getCustomer(id: string): Promise<APIItemResponse<Customer>> {
    return this.get<Customer>('get', id);
  }

  /**
   * Create new customer
   */
  async createCustomer(data: CreateCustomerData): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.request(
        '/admin/customers',
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Customer>(response);
    } catch (error) {
      this.handleError(error, 'Create customer');
    }
  }

  /**
   * Update customer
   */
  async updateCustomer(id: string, data: UpdateCustomerData): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.request(
        `/admin/customers/${id}`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Customer>(response);
    } catch (error) {
      this.handleError(error, 'Update customer');
    }
  }

  /**
   * Delete customer
   */
  async deleteCustomer(id: string): Promise<{ success: boolean }> {
    try {
      await this.client.request(
        `/admin/customers/${id}`,
        {
          method: 'DELETE',
        },
        'admin'
      );

      return { success: true };
    } catch (error) {
      this.handleError(error, 'Delete customer');
    }
  }

  /**
   * Get customer orders
   */
  async getCustomerOrders(
    id: string,
    params: { limit?: number; offset?: number } = {}
  ): Promise<APIListResponse<CustomerOrder>> {
    try {
      const response = await this.client.request(
        `/admin/customers/${id}/orders`,
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformListResponse<CustomerOrder>(response);
    } catch (error) {
      this.handleError(error, 'Get customer orders');
    }
  }

  /**
   * Add customer to group
   */
  async addToGroup(customerId: string, groupId: string): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.request(
        `/admin/customers/${customerId}/groups`,
        {
          method: 'POST',
          body: JSON.stringify({ group_id: groupId }),
        },
        'admin'
      );

      return this.transformItemResponse<Customer>(response);
    } catch (error) {
      this.handleError(error, 'Add customer to group');
    }
  }

  /**
   * Remove customer from group
   */
  async removeFromGroup(customerId: string, groupId: string): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.request(
        `/admin/customers/${customerId}/groups/${groupId}`,
        {
          method: 'DELETE',
        },
        'admin'
      );

      return this.transformItemResponse<Customer>(response);
    } catch (error) {
      this.handleError(error, 'Remove customer from group');
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(): Promise<CustomerStats> {
    try {
      const response = await this.client.request(
        '/admin/customers/stats',
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get customer statistics');
    }
  }

  /**
   * Search customers
   */
  async searchCustomers(
    query: string,
    params: CustomerListParams = {}
  ): Promise<APIListResponse<Customer>> {
    return this.getCustomers({ ...params, q: query });
  }

  /**
   * Export customers
   */
  async exportCustomers(params: CustomerListParams = {}): Promise<{ downloadUrl: string }> {
    try {
      const response = await this.client.request(
        '/admin/customers/export',
        {
          method: 'POST',
          body: JSON.stringify(params),
        },
        'admin'
      );

      return { downloadUrl: response.data.downloadUrl };
    } catch (error) {
      this.handleError(error, 'Export customers');
    }
  }

  /**
   * Import customers
   */
  async importCustomers(file: File): Promise<{ jobId: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.client.request(
        '/admin/customers/import',
        {
          method: 'POST',
          body: formData,
          headers: {}, // Let browser set content-type for FormData
        },
        'admin'
      );

      return { jobId: response.data.jobId };
    } catch (error) {
      this.handleError(error, 'Import customers');
    }
  }
}

/**
 * Customer Groups API Service
 */
export class CustomerGroupsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminCustomers', client);
  }

  /**
   * Get all customer groups
   */
  async getGroups(params: ListParams = {}): Promise<APIListResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        '/admin/customer-groups',
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformListResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Get customer groups');
    }
  }

  /**
   * Get single customer group
   */
  async getGroup(id: string): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        `/admin/customer-groups/${id}`,
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformItemResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Get customer group');
    }
  }

  /**
   * Create customer group
   */
  async createGroup(data: {
    name: string;
    metadata?: Record<string, any>;
  }): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        '/admin/customer-groups',
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Create customer group');
    }
  }

  /**
   * Update customer group
   */
  async updateGroup(
    id: string,
    data: {
      name?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        `/admin/customer-groups/${id}`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Update customer group');
    }
  }

  /**
   * Delete customer group
   */
  async deleteGroup(id: string): Promise<{ success: boolean }> {
    try {
      await this.client.request(
        `/admin/customer-groups/${id}`,
        {
          method: 'DELETE',
        },
        'admin'
      );

      return { success: true };
    } catch (error) {
      this.handleError(error, 'Delete customer group');
    }
  }

  /**
   * Add customers to group
   */
  async addCustomersToGroup(
    groupId: string,
    customerIds: string[]
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        `/admin/customer-groups/${groupId}/customers/batch`,
        {
          method: 'POST',
          body: JSON.stringify({ customer_ids: customerIds }),
        },
        'admin'
      );

      return this.transformItemResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Add customers to group');
    }
  }

  /**
   * Remove customers from group
   */
  async removeCustomersFromGroup(
    groupId: string,
    customerIds: string[]
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.request(
        `/admin/customer-groups/${groupId}/customers/batch`,
        {
          method: 'DELETE',
          body: JSON.stringify({ customer_ids: customerIds }),
        },
        'admin'
      );

      return this.transformItemResponse<CustomerGroup>(response);
    } catch (error) {
      this.handleError(error, 'Remove customers from group');
    }
  }
}

// Export service instances using the frontend API client
export const adminCustomersAPI = new AdminCustomersAPIService(apiClient);
export const customerGroupsAPI = new CustomerGroupsAPIService(apiClient);
