/**
 * Products API Service
 * Handles all product-related API operations
 */

import { BaseAPIService, APIListResponse, APIItemResponse, ListParams } from './base';
import { APIClient, apiClient } from '../api-client';

export interface Product {
  id: string;
  title: string;
  description?: string;
  handle?: string;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string;
  images?: string[];
  collection_id?: string;
  collection?: {
    id: string;
    title: string;
    handle: string;
  };
  tags?: Array<{ id: string; value: string }>;
  variants?: ProductVariant[];
  options?: ProductOption[];
  categories?: ProductCategory[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ProductVariant {
  id: string;
  title: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  prices: ProductPrice[];
  options: Array<{ option_id: string; value: string }>;
}

export interface ProductPrice {
  id: string;
  currency_code: string;
  amount: number;
  region_id?: string;
}

export interface ProductOption {
  id: string;
  title: string;
  values: Array<{ id: string; value: string }>;
}

export interface ProductCategory {
  id: string;
  name: string;
  handle: string;
  parent_category_id?: string;
}

export interface CreateProductData {
  title: string;
  description?: string;
  handle?: string;
  status?: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string;
  images?: string[];
  collection_id?: string;
  tags?: Array<{ value: string }>;
  variants?: Partial<ProductVariant>[];
  options?: Partial<ProductOption>[];
  categories?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id?: string;
}

export interface ProductListParams extends ListParams {
  collection_id?: string | string[];
  category_id?: string | string[];
  region_id?: string;
  tags?: string[];
  status?: string | string[];
  price_min?: number;
  price_max?: number;
  inventory_min?: number;
  inventory_max?: number;
}

/**
 * Products API Service
 */
export class ProductsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('products', client);
  }

  /**
   * Get all products (store API)
   */
  async getProducts(params: ProductListParams = {}): Promise<APIListResponse<Product>> {
    return this.list<Product>('list', params);
  }

  /**
   * Get single product (store API)
   */
  async getProduct(id: string, region_id?: string): Promise<APIItemResponse<Product>> {
    return this.get<Product>('get', id, { region_id });
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit?: number): Promise<APIListResponse<Product>> {
    try {
      const response = await this.client.callEndpoint('products', 'featured', { limit });

      return this.transformListResponse<Product>(response);
    } catch (error) {
      this.handleError(error, 'Get featured products');
    }
  }

  /**
   * Search products
   */
  async searchProducts(
    query: string,
    params: ProductListParams = {}
  ): Promise<APIListResponse<Product>> {
    return this.getProducts({ ...params, q: query });
  }
}

/**
 * Admin Products API Service
 */
export class AdminProductsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminProducts', client);
  }

  /**
   * Get all products (admin API)
   */
  async getProducts(params: ProductListParams = {}): Promise<APIListResponse<Product>> {
    return this.list<Product>('list', params);
  }

  /**
   * Get single product (admin API)
   */
  async getProduct(id: string): Promise<APIItemResponse<Product>> {
    return this.get<Product>('get', id);
  }

  /**
   * Create new product
   */
  async createProduct(data: CreateProductData): Promise<APIItemResponse<Product>> {
    return this.create<Product>('create', data);
  }

  /**
   * Update product
   */
  async updateProduct(id: string, data: UpdateProductData): Promise<APIItemResponse<Product>> {
    return this.update<Product>('update', id, data);
  }

  /**
   * Delete product
   */
  async deleteProduct(id: string): Promise<{ success: boolean }> {
    return this.delete('delete', id);
  }

  /**
   * Bulk update product status
   */
  async bulkUpdateStatus(
    productIds: string[],
    status: string
  ): Promise<{ success: boolean; updated: number }> {
    try {
      const response = await this.client.bulkUpdateProductStatus(productIds, status);
      return {
        success: true,
        updated: response.data?.updated || productIds.length,
      };
    } catch (error) {
      this.handleError(error, 'Bulk update product status');
    }
  }

  /**
   * Get product analytics
   */
  async getProductAnalytics(id: string, period: string = '30d'): Promise<any> {
    try {
      const response = await this.client.request(
        `/admin/products/${id}/analytics`,
        {
          method: 'GET',
        },
        'admin'
      );

      return response.data;
    } catch (error) {
      this.handleError(error, 'Get product analytics');
    }
  }

  /**
   * Duplicate product
   */
  async duplicateProduct(id: string, title?: string): Promise<APIItemResponse<Product>> {
    try {
      const response = await this.client.request(
        `/admin/products/${id}/duplicate`,
        {
          method: 'POST',
          body: JSON.stringify({ title }),
        },
        'admin'
      );

      return this.transformItemResponse<Product>(response);
    } catch (error) {
      this.handleError(error, 'Duplicate product');
    }
  }

  /**
   * Export products
   */
  async exportProducts(params: ProductListParams = {}): Promise<{ downloadUrl: string }> {
    try {
      const response = await this.client.request(
        '/admin/products/export',
        {
          method: 'POST',
          body: JSON.stringify(params),
        },
        'admin'
      );

      return { downloadUrl: response.data.downloadUrl };
    } catch (error) {
      this.handleError(error, 'Export products');
    }
  }

  /**
   * Import products
   */
  async importProducts(file: File): Promise<{ jobId: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.client.request(
        '/admin/products/import',
        {
          method: 'POST',
          body: formData,
          headers: {}, // Let browser set content-type for FormData
        },
        'admin'
      );

      return { jobId: response.data.jobId };
    } catch (error) {
      this.handleError(error, 'Import products');
    }
  }
}

/**
 * Product Variants API Service
 */
export class ProductVariantsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminProducts', client);
  }

  /**
   * Get product variants
   */
  async getVariants(productId: string): Promise<APIListResponse<ProductVariant>> {
    try {
      const response = await this.client.request(
        `/admin/products/${productId}/variants`,
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformListResponse<ProductVariant>(response);
    } catch (error) {
      this.handleError(error, 'Get product variants');
    }
  }

  /**
   * Create product variant
   */
  async createVariant(
    productId: string,
    data: Partial<ProductVariant>
  ): Promise<APIItemResponse<ProductVariant>> {
    try {
      const response = await this.client.request(
        `/admin/products/${productId}/variants`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<ProductVariant>(response);
    } catch (error) {
      this.handleError(error, 'Create product variant');
    }
  }

  /**
   * Update product variant
   */
  async updateVariant(
    productId: string,
    variantId: string,
    data: Partial<ProductVariant>
  ): Promise<APIItemResponse<ProductVariant>> {
    try {
      const response = await this.client.request(
        `/admin/products/${productId}/variants/${variantId}`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<ProductVariant>(response);
    } catch (error) {
      this.handleError(error, 'Update product variant');
    }
  }

  /**
   * Delete product variant
   */
  async deleteVariant(productId: string, variantId: string): Promise<{ success: boolean }> {
    try {
      await this.client.request(
        `/admin/products/${productId}/variants/${variantId}`,
        {
          method: 'DELETE',
        },
        'admin'
      );

      return { success: true };
    } catch (error) {
      this.handleError(error, 'Delete product variant');
    }
  }
}

// Export service instances using the frontend API client
export const productsAPI = new ProductsAPIService(apiClient);
export const adminProductsAPI = new AdminProductsAPIService(apiClient);
export const productVariantsAPI = new ProductVariantsAPIService(apiClient);
