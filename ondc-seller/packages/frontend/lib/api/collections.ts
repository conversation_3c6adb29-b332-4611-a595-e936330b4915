/**
 * Collections API Service
 * Handles all collection-related API operations
 */

import { BaseAPIService, APIListResponse, APIItemResponse, ListParams } from './base';
import { APIClient, apiClient } from '../api-client';
import { Product } from './products';

export interface Collection {
  id: string;
  title: string;
  handle: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CollectionWithProducts extends Collection {
  products: Product[];
}

export interface CreateCollectionData {
  title: string;
  handle?: string;
  metadata?: Record<string, any>;
}

export interface UpdateCollectionData extends Partial<CreateCollectionData> {}

export interface CollectionListParams extends ListParams {
  created_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  updated_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
}

/**
 * Collections API Service (Store)
 */
export class CollectionsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('collections', client);
  }

  /**
   * Get all collections
   */
  async getCollections(params: CollectionListParams = {}): Promise<APIListResponse<Collection>> {
    return this.list<Collection>('list', params);
  }

  /**
   * Get single collection
   */
  async getCollection(id: string): Promise<APIItemResponse<CollectionWithProducts>> {
    return this.get<CollectionWithProducts>('get', id);
  }

  /**
   * Get collection by handle
   */
  async getCollectionByHandle(handle: string): Promise<APIItemResponse<CollectionWithProducts>> {
    try {
      const response = await this.client.request(
        `/store/collections/${handle}`,
        {
          method: 'GET',
        },
        'publishable'
      );

      return this.transformItemResponse<CollectionWithProducts>(response);
    } catch (error) {
      this.handleError(error, 'Get collection by handle');
    }
  }
}

/**
 * Admin Collections API Service
 */
export class AdminCollectionsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('collections', client);
  }

  /**
   * Get all collections (admin)
   */
  async getCollections(params: CollectionListParams = {}): Promise<APIListResponse<Collection>> {
    try {
      const response = await this.client.request(
        '/admin/collections',
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformListResponse<Collection>(response);
    } catch (error) {
      this.handleError(error, 'Get collections');
    }
  }

  /**
   * Get single collection (admin)
   */
  async getCollection(id: string): Promise<APIItemResponse<CollectionWithProducts>> {
    try {
      const response = await this.client.request(
        `/admin/collections/${id}`,
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformItemResponse<CollectionWithProducts>(response);
    } catch (error) {
      this.handleError(error, 'Get collection');
    }
  }

  /**
   * Create new collection
   */
  async createCollection(data: CreateCollectionData): Promise<APIItemResponse<Collection>> {
    try {
      const response = await this.client.request(
        '/admin/collections',
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Collection>(response);
    } catch (error) {
      this.handleError(error, 'Create collection');
    }
  }

  /**
   * Update collection
   */
  async updateCollection(
    id: string,
    data: UpdateCollectionData
  ): Promise<APIItemResponse<Collection>> {
    try {
      const response = await this.client.request(
        `/admin/collections/${id}`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
        'admin'
      );

      return this.transformItemResponse<Collection>(response);
    } catch (error) {
      this.handleError(error, 'Update collection');
    }
  }

  /**
   * Delete collection
   */
  async deleteCollection(id: string): Promise<{ success: boolean }> {
    try {
      await this.client.request(
        `/admin/collections/${id}`,
        {
          method: 'DELETE',
        },
        'admin'
      );

      return { success: true };
    } catch (error) {
      this.handleError(error, 'Delete collection');
    }
  }

  /**
   * Add products to collection
   */
  async addProducts(
    collectionId: string,
    productIds: string[]
  ): Promise<APIItemResponse<Collection>> {
    try {
      const response = await this.client.request(
        `/admin/collections/${collectionId}/products/batch`,
        {
          method: 'POST',
          body: JSON.stringify({ product_ids: productIds }),
        },
        'admin'
      );

      return this.transformItemResponse<Collection>(response);
    } catch (error) {
      this.handleError(error, 'Add products to collection');
    }
  }

  /**
   * Remove products from collection
   */
  async removeProducts(
    collectionId: string,
    productIds: string[]
  ): Promise<APIItemResponse<Collection>> {
    try {
      const response = await this.client.request(
        `/admin/collections/${collectionId}/products/batch`,
        {
          method: 'DELETE',
          body: JSON.stringify({ product_ids: productIds }),
        },
        'admin'
      );

      return this.transformItemResponse<Collection>(response);
    } catch (error) {
      this.handleError(error, 'Remove products from collection');
    }
  }

  /**
   * Get collection products
   */
  async getCollectionProducts(
    id: string,
    params: { limit?: number; offset?: number } = {}
  ): Promise<APIListResponse<Product>> {
    try {
      const response = await this.client.request(
        `/admin/collections/${id}/products`,
        {
          method: 'GET',
        },
        'admin'
      );

      return this.transformListResponse<Product>(response);
    } catch (error) {
      this.handleError(error, 'Get collection products');
    }
  }
}

// Export service instances using the frontend API client
export const collectionsAPI = new CollectionsAPIService(apiClient);
export const adminCollectionsAPI = new AdminCollectionsAPIService(apiClient);
