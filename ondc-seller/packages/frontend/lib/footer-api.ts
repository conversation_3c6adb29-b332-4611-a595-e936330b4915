/**
 * Footer CMS API Integration
 * 
 * This module provides functions to fetch footer content from Strapi CMS
 * with comprehensive error handling and fallback mechanisms.
 */

import { FooterContent, StrapiFooterResponse, StaticFooterData } from '@/types/footer';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN;

// Static fallback footer data (current footer content)
const FALLBACK_FOOTER_DATA: StaticFooterData = {
  companyInfo: {
    name: 'ONDC Seller',
    description: 'Empowering sellers on the Open Network for Digital Commerce. Join the digital revolution and grow your business with ONDC.',
    logoText: 'O'
  },
  sections: [
    {
      title: 'Seller Tools',
      links: [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'Product Management', href: '/products' },
        { name: 'Order Management', href: '/orders' },
        { name: 'Analytics', href: '/analytics' },
      ],
    },
    {
      title: 'Support',
      links: [
        { name: 'Help Center', href: '/help' },
        { name: 'Contact Us', href: '/contact' },
        { name: 'API Documentation', href: '/docs' },
        { name: 'Developer Guide', href: '/developers' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'ONDC Guidelines', href: '/ondc-guidelines' },
        { name: 'Seller Agreement', href: '/seller-agreement' },
      ],
    },
  ],
  socialLinks: [
    {
      platform: 'Twitter',
      href: '#',
      icon: 'twitter'
    },
    {
      platform: 'Facebook',
      href: '#',
      icon: 'facebook'
    },
    {
      platform: 'LinkedIn',
      href: '#',
      icon: 'linkedin'
    }
  ],
  contactInfo: {
    address: 'Mumbai, Maharashtra, India',
    phone: '+91 1234567890',
    email: '<EMAIL>'
  },
  copyrightText: `© ${new Date().getFullYear()} ONDC Seller Platform. All rights reserved.`,
  poweredBy: 'Powered by ONDC',
  verificationBadge: {
    text: 'Verified Seller Platform',
    verified: true
  }
};

/**
 * Generic Strapi API request function for footer
 */
async function strapiFooterRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<StrapiFooterResponse> {
  const url = `${STRAPI_URL}/api${endpoint}`;

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (API_TOKEN) {
    defaultHeaders['Authorization'] = `Bearer ${API_TOKEN}`;
  }

  const config: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  };

  try {
    console.log(`🔗 Fetching footer data from Strapi: ${url}`);
    
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`Strapi Footer API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Footer data fetched successfully from Strapi');
    return data;
  } catch (error) {
    console.error('❌ Strapi Footer API request failed:', error);
    throw error;
  }
}

/**
 * Get footer content from Strapi CMS
 */
export async function getFooterContent(): Promise<StaticFooterData> {
  try {
    console.log('🚀 Attempting to fetch footer content from Strapi CMS...');
    
    const response = await strapiFooterRequest<FooterContent>('/footers?populate=*');
    
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      const footerData = response.data[0];
      console.log('✅ Successfully fetched footer content from Strapi');
      return transformStrapiFooterData(footerData);
    } else if (response.data && !Array.isArray(response.data)) {
      const footerData = response.data as FooterContent;
      console.log('✅ Successfully fetched footer content from Strapi');
      return transformStrapiFooterData(footerData);
    } else {
      console.warn('⚠️ No footer content found in Strapi, using fallback data');
      return FALLBACK_FOOTER_DATA;
    }
  } catch (error) {
    console.error('❌ Failed to fetch footer content from Strapi:', error);
    console.log('🔄 Using fallback footer data to ensure functionality');
    
    // Log error for monitoring
    logFooterError('fetch_failed', error);
    
    return FALLBACK_FOOTER_DATA;
  }
}

/**
 * Transform Strapi footer data to our internal format
 */
function transformStrapiFooterData(strapiData: FooterContent): StaticFooterData {
  try {
    const { attributes } = strapiData;
    
    return {
      companyInfo: {
        name: attributes.companyInfo?.name || FALLBACK_FOOTER_DATA.companyInfo.name,
        description: attributes.companyInfo?.description || FALLBACK_FOOTER_DATA.companyInfo.description,
        logoText: attributes.companyInfo?.name?.charAt(0) || FALLBACK_FOOTER_DATA.companyInfo.logoText
      },
      sections: attributes.quickLinks?.map(section => ({
        title: section.title,
        links: section.links?.map(link => ({
          name: link.title,
          href: link.url
        })) || []
      })) || FALLBACK_FOOTER_DATA.sections,
      socialLinks: attributes.socialMediaLinks?.filter(link => link.isActive).map(link => ({
        platform: link.platform,
        href: link.url,
        icon: link.icon || link.platform.toLowerCase()
      })) || FALLBACK_FOOTER_DATA.socialLinks,
      contactInfo: {
        address: attributes.contactInfo?.address,
        phone: attributes.contactInfo?.phone,
        email: attributes.contactInfo?.email
      },
      copyrightText: attributes.copyrightText || FALLBACK_FOOTER_DATA.copyrightText,
      poweredBy: FALLBACK_FOOTER_DATA.poweredBy,
      verificationBadge: FALLBACK_FOOTER_DATA.verificationBadge
    };
  } catch (error) {
    console.error('❌ Error transforming Strapi footer data:', error);
    logFooterError('transform_failed', error);
    return FALLBACK_FOOTER_DATA;
  }
}

/**
 * Get cached footer content with ISR support
 */
export async function getCachedFooterContent(): Promise<StaticFooterData> {
  try {
    // In a real implementation, you might use Next.js ISR or a caching layer
    // For now, we'll implement a simple in-memory cache with TTL
    const cacheKey = 'footer_content';
    const cacheTTL = 5 * 60 * 1000; // 5 minutes
    
    // Check if we have cached data
    const cached = getFromCache(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < cacheTTL) {
      console.log('📦 Using cached footer content');
      return cached.data;
    }
    
    // Fetch fresh data
    const footerData = await getFooterContent();
    
    // Cache the data
    setCache(cacheKey, {
      data: footerData,
      timestamp: Date.now()
    });
    
    return footerData;
  } catch (error) {
    console.error('❌ Error getting cached footer content:', error);
    logFooterError('cache_failed', error);
    return FALLBACK_FOOTER_DATA;
  }
}

/**
 * Simple in-memory cache implementation
 */
const cache = new Map();

function getFromCache(key: string) {
  return cache.get(key);
}

function setCache(key: string, value: any) {
  cache.set(key, value);
}

/**
 * Log footer-related errors for monitoring
 */
function logFooterError(type: string, error: any) {
  const errorLog = {
    timestamp: new Date().toISOString(),
    type: `footer_${type}`,
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    context: 'footer_cms_integration'
  };
  
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error('🚨 Footer Error Log:', errorLog);
  }
  
  // In production, you might send this to an error tracking service
  // Example: Sentry, LogRocket, etc.
}

/**
 * Test Strapi connection for footer content
 */
export async function testFooterConnection(): Promise<{
  success: boolean;
  message: string;
  data?: any;
}> {
  try {
    console.log('🧪 Testing Strapi footer connection...');
    
    const response = await strapiFooterRequest<FooterContent>('/footers?populate=*');
    
    return {
      success: true,
      message: 'Successfully connected to Strapi footer API',
      data: response
    };
  } catch (error) {
    return {
      success: false,
      message: `Failed to connect to Strapi footer API: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Validate footer data structure
 */
export function validateFooterData(data: any): boolean {
  try {
    // Basic validation of required fields
    return !!(
      data &&
      data.companyInfo &&
      data.companyInfo.name &&
      data.sections &&
      Array.isArray(data.sections) &&
      data.copyrightText
    );
  } catch (error) {
    console.error('❌ Footer data validation failed:', error);
    return false;
  }
}
