/**
 * Frontend API Client using shared endpoint configuration
 * This client automatically maps to backend endpoints using the shared configuration
 */

import { API_ENDPOINTS, EndpointMapper, EndpointConfig } from '../../shared/api-endpoints';

// Configuration
const FRONTEND_API_URL = process.env.NEXT_PUBLIC_FRONTEND_API_URL || 'http://localhost:3001/api';
const MEDUSA_API_URL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000';
const MEDUSA_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY;

// Types
export interface APIResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
  timestamp?: string;
}

export interface APIClientConfig {
  baseURL?: string;
  publishableKey?: string;
  adminToken?: string;
  timeout?: number;
  useFrontendAPI?: boolean;
}

/**
 * API Client Error
 */
export class APIClientError extends Error {
  constructor(
    message: string,
    public status: number,
    public endpoint?: string,
    public response?: any
  ) {
    super(message);
    this.name = 'APIClientError';
  }
}

/**
 * Main API Client Class
 */
export class APIClient {
  private baseURL: string;
  private publishableKey?: string;
  private adminToken?: string;
  private timeout: number;
  private useFrontendAPI: boolean;

  constructor(config: APIClientConfig = {}) {
    this.useFrontendAPI = config.useFrontendAPI ?? true; // Default to frontend API
    this.baseURL = config.baseURL || (this.useFrontendAPI ? FRONTEND_API_URL : MEDUSA_API_URL);
    this.publishableKey = config.publishableKey || MEDUSA_PUBLISHABLE_KEY;
    this.adminToken = config.adminToken;
    this.timeout = config.timeout || 30000;
  }

  /**
   * Make HTTP request
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    auth: 'none' | 'publishable' | 'admin' | 'bearer' = 'none'
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    return this.requestDirect<T>(url, options, auth);
  }

  /**
   * Make HTTP request with full URL (no baseURL prepending)
   */
  private async requestDirect<T>(
    url: string,
    options: RequestInit = {},
    auth: 'none' | 'publishable' | 'admin' | 'bearer' = 'none'
  ): Promise<APIResponse<T>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...((options.headers as Record<string, string>) || {}),
    };

    // Add authentication headers
    switch (auth) {
      case 'publishable':
        if (this.publishableKey) {
          headers['x-publishable-api-key'] = this.publishableKey;
        }
        break;
      case 'admin':
        if (this.adminToken) {
          headers['Authorization'] = `Bearer ${this.adminToken}`;
        }
        break;
      case 'bearer':
        if (this.adminToken) {
          headers['Authorization'] = `Bearer ${this.adminToken}`;
        }
        break;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      let responseData: any;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        throw new APIClientError(
          responseData?.error || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          url,
          responseData
        );
      }

      return {
        data: responseData,
        status: response.status,
        timestamp: responseData?.timestamp,
      };
    } catch (error) {
      if (error instanceof APIClientError) {
        throw error;
      }

      if (error.name === 'AbortError') {
        throw new APIClientError('Request timeout', 408, url);
      }

      throw new APIClientError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0,
        url
      );
    }
  }

  /**
   * Call endpoint using shared configuration
   */
  async callEndpoint<T>(
    category: string,
    name: string,
    params: Record<string, any> = {},
    body?: Record<string, any>
  ): Promise<APIResponse<T>> {
    const config = EndpointMapper.getEndpoint(category, name);

    if (!config) {
      throw new APIClientError(`Endpoint ${category}.${name} not found`, 404);
    }

    // Validate parameters
    const paramValidation = EndpointMapper.validateParams(config, params);
    if (!paramValidation.valid) {
      throw new APIClientError(
        `Parameter validation failed: ${paramValidation.errors.join(', ')}`,
        400
      );
    }

    // Validate body if present
    if (body && config.body) {
      const bodyValidation = EndpointMapper.validateBody(config, body);
      if (!bodyValidation.valid) {
        throw new APIClientError(
          `Body validation failed: ${bodyValidation.errors.join(', ')}`,
          400
        );
      }
    }

    // Build URL
    const pathParams: Record<string, any> = {};
    const queryParams: Record<string, any> = {};

    // Separate path parameters from query parameters
    Object.entries(params).forEach(([key, value]) => {
      if (config.path.includes(`:${key}`)) {
        pathParams[key] = value;
      } else {
        queryParams[key] = value;
      }
    });

    let url = EndpointMapper.buildUrl(config.path, pathParams);
    const queryString = EndpointMapper.buildQueryString(queryParams);

    // For frontend API, we need to adjust the path
    if (this.useFrontendAPI) {
      // Remove leading slash and add to base URL
      url = url.startsWith('/') ? url.substring(1) : url;
      url = `${this.baseURL}/${url}`;
    } else {
      url = `${this.baseURL}${url}`;
    }

    const fullUrl = `${url}${queryString}`;

    // Prepare request options
    const options: RequestInit = {
      method: config.method,
    };

    if (body && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      options.body = JSON.stringify(body);
    }

    // For frontend API, we don't need authentication headers as they're handled by Next.js
    const authType = this.useFrontendAPI ? 'none' : config.auth;

    return this.requestDirect<T>(fullUrl, options, authType);
  }

  // ============================================================================
  // Convenience methods for common operations
  // ============================================================================

  /**
   * Products
   */
  async getProducts(
    params: {
      limit?: number;
      offset?: number;
      region_id?: string;
      collection_id?: string;
      category_id?: string;
      tags?: string[];
      q?: string;
    } = {}
  ) {
    return this.callEndpoint('products', 'list', params);
  }

  async getProduct(id: string, region_id?: string) {
    return this.callEndpoint('products', 'get', { id, region_id });
  }

  async getFeaturedProducts(limit?: number) {
    return this.callEndpoint('products', 'featured', { limit });
  }

  /**
   * Admin Products
   */
  async getAdminProducts(
    params: {
      limit?: number;
      offset?: number;
      q?: string;
      status?: string[];
      collection_id?: string[];
      tags?: string[];
    } = {}
  ) {
    return this.callEndpoint('adminProducts', 'list', params);
  }

  async createProduct(productData: {
    title: string;
    description?: string;
    handle?: string;
    status?: 'draft' | 'proposed' | 'published' | 'rejected';
    thumbnail?: string;
    images?: string[];
    collection_id?: string;
    tags?: Array<{ value: string }>;
  }) {
    return this.callEndpoint('adminProducts', 'create', {}, productData);
  }

  async updateProduct(id: string, productData: any) {
    return this.callEndpoint('adminProducts', 'update', { id }, productData);
  }

  async deleteProduct(id: string) {
    return this.callEndpoint('adminProducts', 'delete', { id });
  }

  /**
   * Orders
   */
  async getOrders(
    params: {
      limit?: number;
      offset?: number;
    } = {}
  ) {
    return this.callEndpoint('orders', 'list', params);
  }

  async getOrder(id: string) {
    return this.callEndpoint('orders', 'get', { id });
  }

  /**
   * Admin Orders
   */
  async getAdminOrders(
    params: {
      limit?: number;
      offset?: number;
      status?: string[];
      fulfillment_status?: string[];
      payment_status?: string[];
      q?: string;
    } = {}
  ) {
    return this.callEndpoint('adminOrders', 'list', params);
  }

  /**
   * Collections
   */
  async getCollections(
    params: {
      limit?: number;
      offset?: number;
    } = {}
  ) {
    return this.callEndpoint('collections', 'list', params);
  }

  async getCollection(id: string) {
    return this.callEndpoint('collections', 'get', { id });
  }

  /**
   * Regions
   */
  async getRegions() {
    return this.callEndpoint('regions', 'list');
  }

  /**
   * Cart
   */
  async createCart(region_id?: string) {
    return this.callEndpoint('cart', 'create', {}, { region_id });
  }

  async getCart(id: string) {
    return this.callEndpoint('cart', 'get', { id });
  }

  async addToCart(cartId: string, variant_id: string, quantity: number) {
    return this.callEndpoint('cart', 'addItem', { id: cartId }, { variant_id, quantity });
  }

  /**
   * System
   */
  async getHealth() {
    return this.callEndpoint('system', 'health');
  }

  async getVersion() {
    return this.callEndpoint('system', 'version');
  }

  // ============================================================================
  // Custom endpoints (not in shared config)
  // ============================================================================

  /**
   * ONDC specific endpoints
   */
  async getONDCCatalog(provider_id?: string, location_id?: string) {
    return this.request(
      '/ondc/catalog',
      {
        method: 'GET',
      },
      'none'
    );
  }

  async searchONDC(searchData: any) {
    return this.request(
      '/ondc/search',
      {
        method: 'POST',
        body: JSON.stringify(searchData),
      },
      'none'
    );
  }

  /**
   * Analytics
   */
  async getDashboardAnalytics() {
    return this.request(
      '/analytics/dashboard',
      {
        method: 'GET',
      },
      'admin'
    );
  }

  /**
   * Bulk operations
   */
  async bulkUpdateProductStatus(product_ids: string[], status: string) {
    return this.request(
      '/bulk/products/status',
      {
        method: 'POST',
        body: JSON.stringify({ product_ids, status }),
      },
      'admin'
    );
  }
}

// Export singleton instance configured for frontend API
export const apiClient = new APIClient({ useFrontendAPI: true });

// Export class for custom instances
export default APIClient;
