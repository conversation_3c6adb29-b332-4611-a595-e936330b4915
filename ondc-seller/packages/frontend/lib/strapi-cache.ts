/**
 * Strapi Content Caching System
 * 
 * This module provides caching functionality for Strapi CMS content
 * to improve performance and reduce API calls.
 */

import { Page } from './strapi-api';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  enableLocalStorage: boolean;
}

class StrapiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      maxSize: 100,
      enableLocalStorage: true,
      ...config,
    };

    // Load cache from localStorage on initialization
    if (this.config.enableLocalStorage && typeof window !== 'undefined') {
      this.loadFromLocalStorage();
    }
  }

  /**
   * Get cached data
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.saveToLocalStorage();
      return null;
    }

    return entry.data;
  }

  /**
   * Set cached data
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Enforce cache size limit
    if (this.cache.size >= this.config.maxSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
    };

    this.cache.set(key, entry);
    this.saveToLocalStorage();
  }

  /**
   * Remove cached data
   */
  delete(key: string): boolean {
    const result = this.cache.delete(key);
    this.saveToLocalStorage();
    return result;
  }

  /**
   * Clear all cached data
   */
  clear(): void {
    this.cache.clear();
    this.saveToLocalStorage();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      maxSize: this.config.maxSize,
      defaultTTL: this.config.defaultTTL,
    };
  }

  /**
   * Clean expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this.saveToLocalStorage();
    }

    return removedCount;
  }

  /**
   * Save cache to localStorage
   */
  private saveToLocalStorage(): void {
    if (!this.config.enableLocalStorage || typeof window === 'undefined') {
      return;
    }

    try {
      const cacheData = Array.from(this.cache.entries());
      localStorage.setItem('strapi-cache', JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error);
    }
  }

  /**
   * Load cache from localStorage
   */
  private loadFromLocalStorage(): void {
    if (!this.config.enableLocalStorage || typeof window === 'undefined') {
      return;
    }

    try {
      const cacheData = localStorage.getItem('strapi-cache');
      if (cacheData) {
        const entries = JSON.parse(cacheData);
        this.cache = new Map(entries);
        
        // Clean up expired entries on load
        this.cleanup();
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error);
      localStorage.removeItem('strapi-cache');
    }
  }
}

// Create singleton cache instance
export const strapiCache = new StrapiCache({
  defaultTTL: 10 * 60 * 1000, // 10 minutes for production
  maxSize: 50,
  enableLocalStorage: true,
});

/**
 * Cache key generators
 */
export const cacheKeys = {
  page: (slug: string) => `page:${slug}`,
  pages: (params?: Record<string, any>) => `pages:${JSON.stringify(params || {})}`,
  pageById: (id: number) => `page:id:${id}`,
};

/**
 * Cached page functions
 */
export async function getCachedPage(
  slug: string,
  fetcher: () => Promise<Page | null>,
  ttl?: number
): Promise<Page | null> {
  const cacheKey = cacheKeys.page(slug);
  
  // Try to get from cache first
  const cached = strapiCache.get<Page>(cacheKey);
  if (cached) {
    console.log(`Cache hit for page: ${slug}`);
    return cached;
  }

  // Fetch from API
  console.log(`Cache miss for page: ${slug}, fetching from API`);
  try {
    const data = await fetcher();
    
    if (data) {
      strapiCache.set(cacheKey, data, ttl);
    }
    
    return data;
  } catch (error) {
    console.error(`Failed to fetch page ${slug}:`, error);
    return null;
  }
}

export async function getCachedPages(
  params: Record<string, any> | undefined,
  fetcher: () => Promise<Page[]>,
  ttl?: number
): Promise<Page[]> {
  const cacheKey = cacheKeys.pages(params);
  
  // Try to get from cache first
  const cached = strapiCache.get<Page[]>(cacheKey);
  if (cached) {
    console.log(`Cache hit for pages:`, params);
    return cached;
  }

  // Fetch from API
  console.log(`Cache miss for pages:`, params, 'fetching from API');
  try {
    const data = await fetcher();
    strapiCache.set(cacheKey, data, ttl);
    return data;
  } catch (error) {
    console.error(`Failed to fetch pages:`, error);
    return [];
  }
}

/**
 * Cache invalidation functions
 */
export function invalidatePageCache(slug: string): void {
  strapiCache.delete(cacheKeys.page(slug));
}

export function invalidateAllPagesCache(): void {
  const keys = Array.from(strapiCache['cache'].keys());
  keys.forEach(key => {
    if (key.startsWith('page:') || key.startsWith('pages:')) {
      strapiCache.delete(key);
    }
  });
}

/**
 * Cache management utilities
 */
export function getCacheStats() {
  return strapiCache.getStats();
}

export function clearAllCache(): void {
  strapiCache.clear();
}

export function cleanupExpiredCache(): number {
  return strapiCache.cleanup();
}

// Auto cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    const removed = cleanupExpiredCache();
    if (removed > 0) {
      console.log(`Cleaned up ${removed} expired cache entries`);
    }
  }, 5 * 60 * 1000);
}
