/**
 * Medusa API Service
 * Direct integration with Medusa backend for e-commerce operations
 */

// Medusa API configuration
const MEDUSA_API_URL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000';
const MEDUSA_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY;

// Types for Medusa entities
export interface MedusaProduct {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string;
  images?: Array<{
    id: string;
    url: string;
  }>;
  variants?: Array<{
    id: string;
    title: string;
    prices: Array<{
      amount: number;
      currency_code: string;
    }>;
  }>;
  collection?: {
    id: string;
    title: string;
  };
  tags?: Array<{
    id: string;
    value: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface MedusaOrder {
  id: string;
  status: string;
  fulfillment_status: string;
  payment_status: string;
  display_id: number;
  cart_id: string;
  customer_id: string;
  email: string;
  billing_address: any;
  shipping_address: any;
  items: Array<{
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    quantity: number;
    unit_price: number;
    total: number;
  }>;
  total: number;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  created_at: string;
  updated_at: string;
}

export interface MedusaCustomer {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account: boolean;
  created_at: string;
  updated_at: string;
}

export interface MedusaRegion {
  id: string;
  name: string;
  currency_code: string;
  countries: Array<{
    id: string;
    name: string;
    iso_2: string;
  }>;
}

// API Response types
interface MedusaListResponse<T> {
  [key: string]: T[];
  count: number;
  offset: number;
  limit: number;
}

interface MedusaResponse<T> {
  [key: string]: T;
}

class MedusaAPIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'MedusaAPIError';
  }
}

/**
 * Medusa API Client
 */
class MedusaAPI {
  private baseURL: string;
  private publishableKey?: string;

  constructor() {
    this.baseURL = MEDUSA_API_URL;
    this.publishableKey = MEDUSA_PUBLISHABLE_KEY;
  }

  /**
   * Make HTTP request to Medusa API
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    useAdmin = false
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...((options.headers as Record<string, string>) || {}),
    };

    // Add publishable key for store API
    if (!useAdmin && this.publishableKey) {
      headers['x-publishable-api-key'] = this.publishableKey;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
        } catch {
          // Use default error message if JSON parsing fails
        }

        throw new MedusaAPIError(errorMessage, response.status);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof MedusaAPIError) {
        throw error;
      }
      throw new MedusaAPIError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  }

  // ============================================================================
  // STORE API METHODS (Public endpoints)
  // ============================================================================

  /**
   * Get all products from store
   */
  async getProducts(params: {
    limit?: number;
    offset?: number;
    region_id?: string;
    collection_id?: string;
    category_id?: string;
    tags?: string[];
    q?: string; // search query
  } = {}): Promise<MedusaListResponse<MedusaProduct>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    const endpoint = `/store/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<MedusaListResponse<MedusaProduct>>(endpoint);
  }

  /**
   * Get single product by ID
   */
  async getProduct(id: string, region_id?: string): Promise<MedusaResponse<MedusaProduct>> {
    const params = region_id ? `?region_id=${region_id}` : '';
    return this.request<MedusaResponse<MedusaProduct>>(`/store/products/${id}${params}`);
  }

  /**
   * Get regions
   */
  async getRegions(): Promise<MedusaListResponse<MedusaRegion>> {
    return this.request<MedusaListResponse<MedusaRegion>>('/store/regions');
  }

  /**
   * Search products
   */
  async searchProducts(query: string, params: {
    limit?: number;
    offset?: number;
    region_id?: string;
  } = {}): Promise<MedusaListResponse<MedusaProduct>> {
    return this.getProducts({ ...params, q: query });
  }

  // ============================================================================
  // ADMIN API METHODS (Requires authentication)
  // ============================================================================

  /**
   * Get all products (admin)
   */
  async getAdminProducts(params: {
    limit?: number;
    offset?: number;
    q?: string;
    status?: string[];
    collection_id?: string[];
    tags?: string[];
  } = {}): Promise<MedusaListResponse<MedusaProduct>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    const endpoint = `/admin/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<MedusaListResponse<MedusaProduct>>(endpoint, {}, true);
  }

  /**
   * Get single product (admin)
   */
  async getAdminProduct(id: string): Promise<MedusaResponse<MedusaProduct>> {
    return this.request<MedusaResponse<MedusaProduct>>(`/admin/products/${id}`, {}, true);
  }

  /**
   * Create product (admin)
   */
  async createProduct(productData: {
    title: string;
    description?: string;
    handle?: string;
    status?: 'draft' | 'proposed' | 'published' | 'rejected';
    thumbnail?: string;
    images?: string[];
    collection_id?: string;
    tags?: Array<{ value: string }>;
  }): Promise<MedusaResponse<MedusaProduct>> {
    return this.request<MedusaResponse<MedusaProduct>>(
      '/admin/products',
      {
        method: 'POST',
        body: JSON.stringify(productData),
      },
      true
    );
  }

  /**
   * Update product (admin)
   */
  async updateProduct(
    id: string,
    productData: Partial<{
      title: string;
      description: string;
      handle: string;
      status: 'draft' | 'proposed' | 'published' | 'rejected';
      thumbnail: string;
      images: string[];
      collection_id: string;
      tags: Array<{ value: string }>;
    }>
  ): Promise<MedusaResponse<MedusaProduct>> {
    return this.request<MedusaResponse<MedusaProduct>>(
      `/admin/products/${id}`,
      {
        method: 'POST',
        body: JSON.stringify(productData),
      },
      true
    );
  }

  /**
   * Delete product (admin)
   */
  async deleteProduct(id: string): Promise<{ id: string; deleted: boolean }> {
    return this.request<{ id: string; deleted: boolean }>(
      `/admin/products/${id}`,
      { method: 'DELETE' },
      true
    );
  }

  /**
   * Get all orders (admin)
   */
  async getAdminOrders(params: {
    limit?: number;
    offset?: number;
    status?: string[];
    fulfillment_status?: string[];
    payment_status?: string[];
    q?: string;
  } = {}): Promise<MedusaListResponse<MedusaOrder>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    const endpoint = `/admin/orders${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<MedusaListResponse<MedusaOrder>>(endpoint, {}, true);
  }

  /**
   * Get single order (admin)
   */
  async getAdminOrder(id: string): Promise<MedusaResponse<MedusaOrder>> {
    return this.request<MedusaResponse<MedusaOrder>>(`/admin/orders/${id}`, {}, true);
  }

  /**
   * Get all customers (admin)
   */
  async getAdminCustomers(params: {
    limit?: number;
    offset?: number;
    q?: string;
  } = {}): Promise<MedusaListResponse<MedusaCustomer>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, String(value));
      }
    });

    const endpoint = `/admin/customers${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<MedusaListResponse<MedusaCustomer>>(endpoint, {}, true);
  }
}

// Export singleton instance
export const medusaAPI = new MedusaAPI();

// Export error class
export { MedusaAPIError };

// Utility functions
export const formatPrice = (amount: number, currencyCode: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
  }).format(amount / 100); // Medusa stores prices in cents
};

export const getProductPrice = (product: MedusaProduct, currencyCode: string = 'USD'): string => {
  const variant = product.variants?.[0];
  const price = variant?.prices?.find(p => p.currency_code === currencyCode);
  return price ? formatPrice(price.amount, currencyCode) : 'Price not available';
};

export const getProductImage = (product: MedusaProduct): string => {
  return product.thumbnail || product.images?.[0]?.url || '/images/placeholder.svg';
};
