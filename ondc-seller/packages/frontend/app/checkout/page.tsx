'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCart, useCartSummary } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import CheckoutSteps from '@/components/checkout/CheckoutSteps';
import OrderSummary from '@/components/checkout/OrderSummary';
import CustomerInformation from '@/components/checkout/CustomerInformation';
import ShippingAddress from '@/components/checkout/ShippingAddress';
import PaymentMethod from '@/components/checkout/PaymentMethod';
import OrderConfirmation from '@/components/checkout/OrderConfirmation';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

export interface CustomerInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface Address {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
}

export interface PaymentInfo {
  method: 'card' | 'upi' | 'netbanking' | 'wallet' | 'cod';
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardholderName?: string;
  upiId?: string;
  bankName?: string;
  walletType?: string;
}

export interface OrderData {
  customer: CustomerInfo;
  shippingAddress: Address;
  billingAddress: Address;
  payment: PaymentInfo;
  items: any[];
  totals: {
    subtotal: number;
    tax: number;
    shipping: number;
    total: number;
  };
}

export default function CheckoutPage() {
  const router = useRouter();
  const { items, isLoading, clearCart } = useCart();
  const { subtotal, tax, shipping, total, isEmpty } = useCartSummary();
  const { user, isAuthenticated } = useAuth();

  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  // Form data states
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    email: user?.email || '',
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: user?.phone || '',
  });

  const [shippingAddress, setShippingAddress] = useState<Address>({
    firstName: '',
    lastName: '',
    company: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'India',
    phone: '',
  });

  const [billingAddress, setBillingAddress] = useState<Address>({
    firstName: '',
    lastName: '',
    company: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'India',
    phone: '',
  });

  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo>({
    method: 'card',
  });

  const [sameAsShipping, setSameAsShipping] = useState(true);

  // Redirect if cart is empty
  useEffect(() => {
    if (!isLoading && isEmpty) {
      router.push('/cart');
    }
  }, [isLoading, isEmpty, router]);

  // Update billing address when shipping address changes and sameAsShipping is true
  useEffect(() => {
    if (sameAsShipping) {
      setBillingAddress(shippingAddress);
    }
  }, [shippingAddress, sameAsShipping]);

  const steps = [
    { id: 1, name: 'Customer Information', completed: currentStep > 1 },
    { id: 2, name: 'Shipping Address', completed: currentStep > 2 },
    { id: 3, name: 'Payment Method', completed: currentStep > 3 },
    { id: 4, name: 'Review & Place Order', completed: orderComplete },
  ];

  const handleNextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePlaceOrder = async () => {
    setIsProcessing(true);

    try {
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      const orderData: OrderData = {
        customer: customerInfo,
        shippingAddress,
        billingAddress: sameAsShipping ? shippingAddress : billingAddress,
        payment: paymentInfo,
        items,
        totals: { subtotal, tax, shipping, total },
      };

      // Generate order ID
      const newOrderId = `ONDC-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      setOrderId(newOrderId);

      console.log('Order placed:', orderData);

      // Clear cart and show confirmation
      clearCart();
      setOrderComplete(true);
    } catch (error) {
      console.error('Order placement failed:', error);
      alert('Failed to place order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  if (orderComplete && orderId) {
    return (
      <OrderConfirmation
        orderId={orderId}
        customerEmail={customerInfo.email}
        orderTotal={total}
        estimatedDelivery="3-5 business days"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
          <p className="mt-2 text-gray-600">Complete your order in just a few steps</p>
        </div>

        {/* Progress Steps */}
        <CheckoutSteps steps={steps} currentStep={currentStep} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {currentStep === 1 && (
                <CustomerInformation
                  customerInfo={customerInfo}
                  setCustomerInfo={setCustomerInfo}
                  onNext={handleNextStep}
                  isAuthenticated={isAuthenticated}
                />
              )}

              {currentStep === 2 && (
                <ShippingAddress
                  shippingAddress={shippingAddress}
                  setShippingAddress={setShippingAddress}
                  billingAddress={billingAddress}
                  setBillingAddress={setBillingAddress}
                  sameAsShipping={sameAsShipping}
                  setSameAsShipping={setSameAsShipping}
                  onNext={handleNextStep}
                  onPrevious={handlePreviousStep}
                />
              )}

              {currentStep === 3 && (
                <PaymentMethod
                  paymentInfo={paymentInfo}
                  setPaymentInfo={setPaymentInfo}
                  onNext={handleNextStep}
                  onPrevious={handlePreviousStep}
                />
              )}

              {currentStep === 4 && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Review Your Order</h2>

                  {/* Order Review Summary */}
                  <div className="space-y-6">
                    {/* Customer Info */}
                    <div className="border-b pb-4">
                      <h3 className="font-medium text-gray-900 mb-2">Customer Information</h3>
                      <p className="text-gray-600">
                        {customerInfo.firstName} {customerInfo.lastName}
                      </p>
                      <p className="text-gray-600">{customerInfo.email}</p>
                      <p className="text-gray-600">{customerInfo.phone}</p>
                    </div>

                    {/* Shipping Address */}
                    <div className="border-b pb-4">
                      <h3 className="font-medium text-gray-900 mb-2">Shipping Address</h3>
                      <div className="text-gray-600">
                        <p>
                          {shippingAddress.firstName} {shippingAddress.lastName}
                        </p>
                        <p>{shippingAddress.address1}</p>
                        {shippingAddress.address2 && <p>{shippingAddress.address2}</p>}
                        <p>
                          {shippingAddress.city}, {shippingAddress.state}{' '}
                          {shippingAddress.postalCode}
                        </p>
                        <p>{shippingAddress.country}</p>
                      </div>
                    </div>

                    {/* Payment Method */}
                    <div className="border-b pb-4">
                      <h3 className="font-medium text-gray-900 mb-2">Payment Method</h3>
                      <p className="text-gray-600 capitalize">{paymentInfo.method}</p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-between pt-4">
                      <button
                        onClick={handlePreviousStep}
                        className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        Previous
                      </button>
                      <button
                        onClick={handlePlaceOrder}
                        disabled={isProcessing}
                        className="px-8 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                      >
                        {isProcessing ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <CheckCircleIcon className="w-5 h-5 mr-2" />
                            Place Order
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <OrderSummary
              items={items}
              subtotal={subtotal}
              tax={tax}
              shipping={shipping}
              total={total}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
