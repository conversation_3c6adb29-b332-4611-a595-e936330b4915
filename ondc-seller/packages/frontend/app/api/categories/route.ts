import { NextResponse } from 'next/server';
import { getCategories } from '@/lib/strapi-api';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined;
    const pageSize = searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize')!) : 20;
    const featured = searchParams.get('featured') === 'true' ? true : undefined;
    const parentOnly = searchParams.get('parentOnly') === 'true' ? true : undefined; // New parameter

    console.log('🚀 Categories API: Fetching categories from Strapi CMS...');
    console.log('📊 Parameters:', { page, pageSize, featured, parentOnly });

    // Fetch categories from Strapi CMS
    const response = await getCategories({
      page,
      pageSize,
      featured,
      parentOnly, // New parameter
    });

    console.log('✅ Categories API: Successfully fetched categories');
    console.log('📊 Categories count:', response.data?.length || 0);

    return NextResponse.json({
      success: true,
      data: response.data || [],
      meta: response.meta || {},
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Categories API: Error fetching categories:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: [],
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
