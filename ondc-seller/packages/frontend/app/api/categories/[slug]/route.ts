import { NextResponse } from 'next/server';
import { getCategoryBySlug } from '@/lib/strapi-api';

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    console.log('🚀 Category Detail API: Fetching category by slug:', slug);
    
    // Fetch category from Strapi CMS by slug
    const category = await getCategoryBySlug(slug);

    if (!category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Category not found',
          data: null,
        },
        { status: 404 }
      );
    }

    console.log('✅ Category Detail API: Successfully fetched category');
    console.log('📊 Category:', category.name);

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('❌ Category Detail API: Error fetching category:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch category',
        data: null,
      },
      { status: 500 }
    );
  }
}
