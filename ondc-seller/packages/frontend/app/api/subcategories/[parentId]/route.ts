import { NextResponse } from 'next/server';
import { getSubcategoriesByParent } from '@/lib/strapi-api';

export async function GET(
  request: Request,
  { params }: { params: { parentId: string } }
) {
  try {
    const parentId = parseInt(params.parentId);
    
    if (isNaN(parentId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid parent category ID',
          data: null,
        },
        { status: 400 }
      );
    }

    console.log('🚀 Subcategories API: Fetching subcategories for parent:', parentId);
    
    // Fetch subcategories from Strapi CMS
    const response = await getSubcategoriesByParent(parentId);

    console.log('✅ Subcategories API: Successfully fetched subcategories');
    console.log('📊 Subcategories count:', response.data?.length || 0);

    return NextResponse.json({
      success: true,
      data: response.data,
      meta: response.meta,
    });
  } catch (error) {
    console.error('❌ Subcategories API: Error fetching subcategories:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch subcategories',
        data: null,
      },
      { status: 500 }
    );
  }
}
