'use client';

import { useState, useEffect } from 'react';
import { getCategories, getProducts, getCategoryBySlug, getProductBySlug } from '@/lib/strapi-api';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  data?: any;
  timestamp: string;
}

export default function TestCategoriesProductsPage() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (result: Omit<TestResult, 'timestamp'>) => {
    setResults(prev => [...prev, { ...result, timestamp: new Date().toISOString() }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    
    console.log('🧪 Starting Categories & Products API Tests...');

    // Test 1: Get Categories
    try {
      console.log('🧪 Test 1: Testing getCategories()...');
      const categoriesResponse = await getCategories({ featured: true, pageSize: 10 });
      
      if (categoriesResponse.data && categoriesResponse.data.length > 0) {
        addResult({
          test: 'Get Categories',
          status: 'PASS',
          message: `Successfully fetched ${categoriesResponse.data.length} categories`,
          data: categoriesResponse
        });
      } else {
        addResult({
          test: 'Get Categories',
          status: 'WARNING',
          message: 'Categories API returned empty or fallback data',
          data: categoriesResponse
        });
      }
    } catch (error) {
      addResult({
        test: 'Get Categories',
        status: 'FAIL',
        message: `Categories API failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: String(error) }
      });
    }

    // Test 2: Get Category by Slug
    try {
      console.log('🧪 Test 2: Testing getCategoryBySlug()...');
      const category = await getCategoryBySlug('electronics');
      
      if (category) {
        addResult({
          test: 'Get Category by Slug',
          status: 'PASS',
          message: `Successfully fetched category: ${category.name || 'Unknown'}`,
          data: category
        });
      } else {
        addResult({
          test: 'Get Category by Slug',
          status: 'WARNING',
          message: 'Category by slug returned null (using fallback)',
          data: null
        });
      }
    } catch (error) {
      addResult({
        test: 'Get Category by Slug',
        status: 'FAIL',
        message: `Category by slug failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: String(error) }
      });
    }

    // Test 3: Get Products
    try {
      console.log('🧪 Test 3: Testing getProducts()...');
      const productsResponse = await getProducts({ featured: true, pageSize: 10 });
      
      if (productsResponse.data && productsResponse.data.length > 0) {
        addResult({
          test: 'Get Products',
          status: 'PASS',
          message: `Successfully fetched ${productsResponse.data.length} products`,
          data: productsResponse
        });
      } else {
        addResult({
          test: 'Get Products',
          status: 'WARNING',
          message: 'Products API returned empty or fallback data',
          data: productsResponse
        });
      }
    } catch (error) {
      addResult({
        test: 'Get Products',
        status: 'FAIL',
        message: `Products API failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: String(error) }
      });
    }

    // Test 4: Get Product by Slug
    try {
      console.log('🧪 Test 4: Testing getProductBySlug()...');
      const product = await getProductBySlug('organic-apples');
      
      if (product) {
        addResult({
          test: 'Get Product by Slug',
          status: 'PASS',
          message: `Successfully fetched product: ${product.name || 'Unknown'}`,
          data: product
        });
      } else {
        addResult({
          test: 'Get Product by Slug',
          status: 'WARNING',
          message: 'Product by slug returned null (using fallback)',
          data: null
        });
      }
    } catch (error) {
      addResult({
        test: 'Get Product by Slug',
        status: 'FAIL',
        message: `Product by slug failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: String(error) }
      });
    }

    // Test 5: Get Products by Category
    try {
      console.log('🧪 Test 5: Testing getProducts() with category filter...');
      const categoryProductsResponse = await getProducts({ category: 'electronics', pageSize: 5 });
      
      if (categoryProductsResponse.data && categoryProductsResponse.data.length > 0) {
        addResult({
          test: 'Get Products by Category',
          status: 'PASS',
          message: `Successfully fetched ${categoryProductsResponse.data.length} products for electronics category`,
          data: categoryProductsResponse
        });
      } else {
        addResult({
          test: 'Get Products by Category',
          status: 'WARNING',
          message: 'Products by category returned empty or fallback data',
          data: categoryProductsResponse
        });
      }
    } catch (error) {
      addResult({
        test: 'Get Products by Category',
        status: 'FAIL',
        message: `Products by category failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: String(error) }
      });
    }

    setIsRunning(false);
    console.log('✅ Categories & Products API Tests completed');
  };

  useEffect(() => {
    // Auto-run tests on page load
    runTests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASS':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'FAIL':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'WARNING':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PASS':
        return '✅';
      case 'FAIL':
        return '❌';
      case 'WARNING':
        return '⚠️';
      default:
        return '⏳';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Categories & Products API Test</h1>
          <p className="mt-2 text-gray-600">
            Comprehensive testing of Strapi CMS integration for categories and products
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Running Tests...
              </>
            ) : (
              'Run Tests'
            )}
          </button>
        </div>

        {/* Results Summary */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {results.filter(r => r.status === 'PASS').length}
                </div>
                <div className="text-sm text-green-800">Tests Passed</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {results.filter(r => r.status === 'FAIL').length}
                </div>
                <div className="text-sm text-red-800">Tests Failed</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {results.filter(r => r.status === 'WARNING').length}
                </div>
                <div className="text-sm text-yellow-800">Warnings</div>
              </div>
            </div>
          </div>
        )}

        {/* Detailed Results */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Detailed Test Results</h2>
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">{getStatusIcon(result.status)}</span>
                      <div>
                        <h3 className="font-medium">{result.test}</h3>
                        <p className="text-sm mt-1">{result.message}</p>
                        <p className="text-xs mt-1 opacity-75">
                          {new Date(result.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      result.status === 'PASS' ? 'bg-green-100 text-green-800' : 
                      result.status === 'FAIL' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  {result.data && (
                    <details className="mt-3">
                      <summary className="cursor-pointer text-sm font-medium">View Data</summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
