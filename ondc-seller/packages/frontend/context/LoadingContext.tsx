'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { PageLoadingOverlay } from '@/components/skeletons/SkeletonBase';

interface LoadingState {
  isPageLoading: boolean;
  isNavigating: boolean;
  loadingMessage: string;
  loadingStates: Record<string, boolean>;
}

interface LoadingContextType extends LoadingState {
  setPageLoading: (loading: boolean, message?: string) => void;
  setNavigating: (navigating: boolean) => void;
  setComponentLoading: (key: string, loading: boolean) => void;
  isComponentLoading: (key: string) => boolean;
  startNavigation: (message?: string) => void;
  finishNavigation: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: React.ReactNode;
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isPageLoading: false,
    isNavigating: false,
    loadingMessage: 'Loading...',
    loadingStates: {}
  });

  const setPageLoading = useCallback((loading: boolean, message = 'Loading...') => {
    setLoadingState(prev => ({
      ...prev,
      isPageLoading: loading,
      loadingMessage: message
    }));
  }, []);

  const setNavigating = useCallback((navigating: boolean) => {
    setLoadingState(prev => ({
      ...prev,
      isNavigating: navigating
    }));
  }, []);

  const setComponentLoading = useCallback((key: string, loading: boolean) => {
    setLoadingState(prev => ({
      ...prev,
      loadingStates: {
        ...prev.loadingStates,
        [key]: loading
      }
    }));
  }, []);

  const isComponentLoading = useCallback((key: string) => {
    return loadingState.loadingStates[key] || false;
  }, [loadingState.loadingStates]);

  const startNavigation = useCallback((message = 'Loading...') => {
    setLoadingState(prev => ({
      ...prev,
      isNavigating: true,
      isPageLoading: true,
      loadingMessage: message
    }));
  }, []);

  const finishNavigation = useCallback(() => {
    setLoadingState(prev => ({
      ...prev,
      isNavigating: false,
      isPageLoading: false
    }));
  }, []);

  const contextValue: LoadingContextType = {
    ...loadingState,
    setPageLoading,
    setNavigating,
    setComponentLoading,
    isComponentLoading,
    startNavigation,
    finishNavigation
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
      {/* Global page loading overlay */}
      {loadingState.isPageLoading && <PageLoadingOverlay />}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}

// Hook for navigation loading
export function useNavigationLoading() {
  const { startNavigation, finishNavigation, isNavigating } = useLoading();
  
  const navigateWithLoading = useCallback(async (
    navigationFn: () => Promise<void> | void,
    message?: string
  ) => {
    try {
      startNavigation(message);
      await navigationFn();
    } finally {
      // Add a small delay to ensure smooth transition
      setTimeout(finishNavigation, 300);
    }
  }, [startNavigation, finishNavigation]);

  return {
    navigateWithLoading,
    isNavigating
  };
}

// Hook for component-specific loading
export function useComponentLoading(componentKey: string) {
  const { setComponentLoading, isComponentLoading } = useLoading();
  
  const setLoading = useCallback((loading: boolean) => {
    setComponentLoading(componentKey, loading);
  }, [componentKey, setComponentLoading]);

  const isLoading = isComponentLoading(componentKey);

  const withLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    try {
      setLoading(true);
      return await asyncFn();
    } finally {
      setLoading(false);
    }
  }, [setLoading]);

  return {
    isLoading,
    setLoading,
    withLoading
  };
}

// Hook for data fetching with loading states
export function useAsyncData<T>(
  fetchFn: () => Promise<T>,
  dependencies: React.DependencyList = [],
  options: {
    immediate?: boolean;
    loadingKey?: string;
  } = {}
) {
  const { immediate = true, loadingKey = 'data-fetch' } = options;
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const { isLoading, withLoading } = useComponentLoading(loadingKey);

  const fetchData = useCallback(async () => {
    try {
      setError(null);
      const result = await withLoading(fetchFn);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    }
  }, [fetchFn, withLoading]);

  React.useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, dependencies);

  return {
    data,
    error,
    isLoading,
    refetch: fetchData
  };
}
