/**
 * ONDC Seller Platform - Prisma Schema Test
 * 
 * This file tests the comprehensive e-commerce schema following Medusa Commerce patterns
 * with ONDC integration capabilities.
 */

import { PrismaClient, ProductStatus, InventoryStatus } from './generated/prisma';

const prisma = new PrismaClient();

/**
 * Test the complete e-commerce schema with all relationships
 */
export async function testEcommerceSchema() {
  try {
    console.log('🧪 Testing ONDC E-commerce Schema...\n');

    // 1. Test Category Creation (with hierarchical support)
    console.log('📁 Testing Category model...');
    const parentCategory = await prisma.category.create({
      data: {
        title: 'Electronics',
        description: 'Electronic devices and accessories',
        handle: 'electronics',
        isActive: true,
        metadata: {
          seoTitle: 'Electronics - ONDC Seller',
          seoDescription: 'Browse our electronics collection'
        }
      }
    });

    const childCategory = await prisma.category.create({
      data: {
        title: 'Smartphones',
        description: 'Mobile phones and accessories',
        handle: 'smartphones',
        isActive: true,
        parentCategoryId: parentCategory.id,
        metadata: {
          seoTitle: 'Smartphones - ONDC Seller'
        }
      }
    });

    console.log(`✅ Created categories: ${parentCategory.title} -> ${childCategory.title}`);

    // 2. Test Product Creation (with Medusa compatibility)
    console.log('\n📦 Testing Product model...');
    const product = await prisma.product.create({
      data: {
        title: 'iPhone 15 Pro',
        description: 'Latest iPhone with advanced features',
        handle: 'iphone-15-pro',
        status: ProductStatus.PUBLISHED,
        thumbnail: '/images/iphone-15-pro.jpg',
        categoryId: childCategory.id,
        weight: 0.187,
        height: 14.67,
        width: 7.09,
        length: 0.83,
        subtitle: 'Pro. Beyond.',
        isGiftcard: false,
        discountable: true,
        ondcCategoryId: 'ondc-electronics-smartphones',
        ondcItemId: 'ondc-iphone-15-pro',
        metadata: {
          brand: 'Apple',
          model: 'iPhone 15 Pro',
          warranty: '1 year'
        }
      }
    });

    console.log(`✅ Created product: ${product.title} (${product.handle})`);

    // 3. Test Product Variant Creation (with pricing)
    console.log('\n🎨 Testing ProductVariant model...');
    const variant128GB = await prisma.productVariant.create({
      data: {
        title: 'iPhone 15 Pro - 128GB - Natural Titanium',
        sku: 'IPHONE-15-PRO-128GB-NT',
        barcode: '194253404057',
        productId: product.id,
        price: 99900.00, // ₹99,900
        compareAtPrice: 109900.00, // ₹1,09,900
        costPrice: 85000.00,
        weight: 0.187,
        allowBackorder: false,
        manageInventory: true,
        ondcVariantId: 'ondc-iphone-15-pro-128gb-nt',
        metadata: {
          storage: '128GB',
          color: 'Natural Titanium',
          colorCode: '#8D7B68'
        }
      }
    });

    const variant256GB = await prisma.productVariant.create({
      data: {
        title: 'iPhone 15 Pro - 256GB - Blue Titanium',
        sku: 'IPHONE-15-PRO-256GB-BT',
        barcode: '194253404064',
        productId: product.id,
        price: 109900.00, // ₹1,09,900
        compareAtPrice: 119900.00, // ₹1,19,900
        costPrice: 95000.00,
        weight: 0.187,
        allowBackorder: false,
        manageInventory: true,
        ondcVariantId: 'ondc-iphone-15-pro-256gb-bt',
        metadata: {
          storage: '256GB',
          color: 'Blue Titanium',
          colorCode: '#5E7C8B'
        }
      }
    });

    console.log(`✅ Created variants: ${variant128GB.sku}, ${variant256GB.sku}`);

    // 4. Test Inventory Item Creation (one-to-one with variants)
    console.log('\n📊 Testing InventoryItem model...');
    const inventory128GB = await prisma.inventoryItem.create({
      data: {
        variantId: variant128GB.id,
        quantity: 50,
        reservedQuantity: 5,
        availableQuantity: 45,
        allowBackorder: false,
        lowStockThreshold: 10,
        status: InventoryStatus.IN_STOCK,
        locationId: 'warehouse-mumbai-01',
        warehouseId: 'wh-mumbai-central',
        ondcInventoryId: 'ondc-inv-iphone-15-pro-128gb',
        metadata: {
          lastRestocked: new Date().toISOString(),
          supplier: 'Apple India'
        }
      }
    });

    const inventory256GB = await prisma.inventoryItem.create({
      data: {
        variantId: variant256GB.id,
        quantity: 25,
        reservedQuantity: 2,
        availableQuantity: 23,
        allowBackorder: false,
        lowStockThreshold: 5,
        status: InventoryStatus.IN_STOCK,
        locationId: 'warehouse-mumbai-01',
        warehouseId: 'wh-mumbai-central',
        ondcInventoryId: 'ondc-inv-iphone-15-pro-256gb',
        metadata: {
          lastRestocked: new Date().toISOString(),
          supplier: 'Apple India'
        }
      }
    });

    console.log(`✅ Created inventory: ${inventory128GB.quantity} units (128GB), ${inventory256GB.quantity} units (256GB)`);

    // 5. Test Product Tags
    console.log('\n🏷️ Testing ProductTag model...');
    await prisma.productTag.createMany({
      data: [
        { productId: product.id, value: 'smartphone' },
        { productId: product.id, value: 'apple' },
        { productId: product.id, value: 'premium' },
        { productId: product.id, value: 'latest' }
      ]
    });

    console.log('✅ Created product tags: smartphone, apple, premium, latest');

    // 6. Test Product Images
    console.log('\n🖼️ Testing ProductImage model...');
    await prisma.productImage.createMany({
      data: [
        {
          productId: product.id,
          url: '/images/iphone-15-pro-front.jpg',
          altText: 'iPhone 15 Pro front view',
          sortOrder: 1,
          isMain: true,
          width: 1200,
          height: 1200,
          mimeType: 'image/jpeg'
        },
        {
          productId: product.id,
          url: '/images/iphone-15-pro-back.jpg',
          altText: 'iPhone 15 Pro back view',
          sortOrder: 2,
          isMain: false,
          width: 1200,
          height: 1200,
          mimeType: 'image/jpeg'
        }
      ]
    });

    console.log('✅ Created product images: front view, back view');

    // 7. Test Product Pricing (multi-currency support)
    console.log('\n💰 Testing ProductPrice model...');
    await prisma.productPrice.createMany({
      data: [
        {
          variantId: variant128GB.id,
          currencyCode: 'INR',
          amount: 99900.00,
          priceType: 'base'
        },
        {
          variantId: variant128GB.id,
          currencyCode: 'USD',
          amount: 1199.00,
          priceType: 'base'
        },
        {
          variantId: variant256GB.id,
          currencyCode: 'INR',
          amount: 109900.00,
          priceType: 'base'
        }
      ]
    });

    console.log('✅ Created pricing: INR and USD support');

    // 8. Test Collections
    console.log('\n📚 Testing Collection model...');
    const featuredCollection = await prisma.collection.create({
      data: {
        title: 'Featured Products',
        description: 'Our most popular products',
        handle: 'featured',
        isActive: true,
        isFeatured: true,
        sortOrder: 1
      }
    });

    await prisma.collectionProduct.create({
      data: {
        collectionId: featuredCollection.id,
        productId: product.id,
        position: 1
      }
    });

    console.log(`✅ Created collection: ${featuredCollection.title} with product`);

    // 9. Test Complex Query with Relationships
    console.log('\n🔍 Testing complex relationships query...');
    const productWithRelations = await prisma.product.findUnique({
      where: { id: product.id },
      include: {
        category: {
          include: {
            parentCategory: true
          }
        },
        variants: {
          include: {
            inventoryItem: true,
            prices: true
          }
        },
        tags: true,
        images: true,
        collections: {
          include: {
            collection: true
          }
        }
      }
    });

    console.log('✅ Complex query successful - all relationships working');
    console.log(`   Product: ${productWithRelations?.title}`);
    console.log(`   Category: ${productWithRelations?.category.title}`);
    console.log(`   Parent Category: ${productWithRelations?.category.parentCategory?.title}`);
    console.log(`   Variants: ${productWithRelations?.variants.length}`);
    console.log(`   Tags: ${productWithRelations?.tags.length}`);
    console.log(`   Images: ${productWithRelations?.images.length}`);
    console.log(`   Collections: ${productWithRelations?.collections.length}`);

    console.log('\n🎉 All schema tests passed successfully!');
    console.log('\n📋 Schema Summary:');
    console.log('   ✅ Category (with hierarchy)');
    console.log('   ✅ Product (Medusa-compatible)');
    console.log('   ✅ ProductVariant (with pricing)');
    console.log('   ✅ InventoryItem (one-to-one)');
    console.log('   ✅ ProductTag (many-to-many)');
    console.log('   ✅ ProductImage (with metadata)');
    console.log('   ✅ ProductPrice (multi-currency)');
    console.log('   ✅ Collection (with products)');
    console.log('   ✅ ONDC integration fields');
    console.log('   ✅ All relationships working');

  } catch (error) {
    console.error('❌ Schema test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEcommerceSchema()
    .then(() => {
      console.log('\n✨ Schema validation complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Schema validation failed:', error);
      process.exit(1);
    });
}
