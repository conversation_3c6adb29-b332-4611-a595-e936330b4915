// ONDC Seller Platform - Prisma Schema
// E-commerce platform following Medusa Commerce patterns with ONDC integration
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENUMS
// ============================================================================

enum ProductStatus {
  DRAFT
  PUBLISHED
  ARCHIVED

  @@map("product_status")
}

enum InventoryStatus {
  IN_STOCK
  LOW_STOCK
  OUT_OF_STOCK
  DISCONTINUED

  @@map("inventory_status")
}

// ============================================================================
// CORE E-COMMERCE MODELS
// ============================================================================

/// Product categorization system with hierarchical support
model Category {
  // Primary fields
  id          String  @id @default(cuid()) @map("id")
  title       String  @map("title")
  description String? @map("description")
  handle      String  @unique @map("handle")
  isActive    Boolean @default(true) @map("is_active")

  // Hierarchical category support (self-referencing)
  parentCategoryId String?    @map("parent_category_id")
  parentCategory   Category?  @relation("CategoryHierarchy", fields: [parentCategoryId], references: [id], onDelete: Cascade)
  childCategories  Category[] @relation("CategoryHierarchy")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  products Product[]

  // Database optimizations
  @@index([handle], name: "idx_category_handle")
  @@index([isActive], name: "idx_category_active")
  @@index([parentCategoryId], name: "idx_category_parent")
  @@map("categories")
}

/// Main product entity with status management and Medusa compatibility
model Product {
  // Primary fields
  id          String        @id @default(cuid()) @map("id")
  title       String        @map("title")
  description String?       @map("description")
  handle      String        @unique @map("handle")
  status      ProductStatus @default(DRAFT) @map("status")
  thumbnail   String?       @map("thumbnail")

  // Category relationship
  categoryId String   @map("category_id")
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  // Product specifications
  weight Float? @map("weight")
  length Float? @map("length")
  height Float? @map("height")
  width  Float? @map("width")

  // Additional Medusa-compatible fields
  subtitle      String? @map("subtitle")
  isGiftcard    Boolean @default(false) @map("is_giftcard")
  profileId     String? @map("profile_id")
  hsCode        String? @map("hs_code")
  originCountry String? @map("origin_country")
  midCode       String? @map("mid_code")
  material      String? @map("material")
  discountable  Boolean @default(true) @map("discountable")
  externalId    String? @map("external_id")

  // ONDC-specific fields
  ondcCategoryId String? @map("ondc_category_id")
  ondcItemId     String? @map("ondc_item_id")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  variants    ProductVariant[]
  tags        ProductTag[]
  images      ProductImage[]
  options     ProductOption[]
  collections CollectionProduct[]

  // Database optimizations
  @@index([handle], name: "idx_product_handle")
  @@index([status], name: "idx_product_status")
  @@index([categoryId], name: "idx_product_category")
  @@index([createdAt], name: "idx_product_created")
  @@index([ondcItemId], name: "idx_product_ondc_item")
  @@map("products")
}

/// Product variations (size, color, price tiers, etc.) with inventory tracking
model ProductVariant {
  // Primary fields
  id      String  @id @default(cuid()) @map("id")
  title   String  @map("title")
  sku     String  @unique @map("sku")
  barcode String? @map("barcode")

  // Product relationship
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Pricing
  price          Decimal  @map("price") @db.Decimal(10, 2)
  compareAtPrice Decimal? @map("compare_at_price") @db.Decimal(10, 2)
  costPrice      Decimal? @map("cost_price") @db.Decimal(10, 2)

  // Physical properties
  weight Float? @map("weight")
  length Float? @map("length")
  height Float? @map("height")
  width  Float? @map("width")

  // Additional Medusa-compatible fields
  ean           String? @map("ean")
  upc           String? @map("upc")
  hsCode        String? @map("hs_code")
  originCountry String? @map("origin_country")
  midCode       String? @map("mid_code")
  material      String? @map("material")

  // Inventory management flags
  allowBackorder  Boolean @default(false) @map("allow_backorder")
  manageInventory Boolean @default(true) @map("manage_inventory")

  // ONDC-specific fields
  ondcVariantId String? @map("ondc_variant_id")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  inventoryItem InventoryItem?
  prices        ProductPrice[]
  options       ProductVariantOption[]

  // Database optimizations
  @@index([sku], name: "idx_variant_sku")
  @@index([productId], name: "idx_variant_product")
  @@index([barcode], name: "idx_variant_barcode")
  @@index([price], name: "idx_variant_price")
  @@map("product_variants")
}

/// Real-time inventory tracking and stock management
model InventoryItem {
  // Primary fields
  id        String @id @default(cuid()) @map("id")
  variantId String @unique @map("variant_id")

  // Variant relationship (one-to-one)
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  // Inventory quantities
  quantity          Int @default(0) @map("quantity")
  reservedQuantity  Int @default(0) @map("reserved_quantity")
  availableQuantity Int @default(0) @map("available_quantity") // Computed: quantity - reservedQuantity

  // Inventory management settings
  allowBackorder    Boolean @default(false) @map("allow_backorder")
  lowStockThreshold Int?    @map("low_stock_threshold")

  // Inventory status tracking
  status InventoryStatus @default(IN_STOCK) @map("status")

  // Location and warehouse management
  locationId  String? @map("location_id")
  warehouseId String? @map("warehouse_id")

  // ONDC-specific inventory fields
  ondcInventoryId String? @map("ondc_inventory_id")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Database optimizations
  @@index([variantId], name: "idx_inventory_variant")
  @@index([status], name: "idx_inventory_status")
  @@index([quantity], name: "idx_inventory_quantity")
  @@index([locationId], name: "idx_inventory_location")
  @@map("inventory_items")
}

// ============================================================================
// SUPPORTING MODELS
// ============================================================================

/// Product pricing with currency and region support
model ProductPrice {
  // Primary fields
  id           String  @id @default(cuid()) @map("id")
  variantId    String  @map("variant_id")
  currencyCode String  @map("currency_code")
  amount       Decimal @map("amount") @db.Decimal(10, 2)

  // Variant relationship
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  // Regional pricing support
  regionId String? @map("region_id")

  // Price type and validity
  priceType String?   @map("price_type") // 'base', 'sale', 'member', etc.
  validFrom DateTime? @map("valid_from")
  validTo   DateTime? @map("valid_to")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([variantId, currencyCode, regionId], name: "unique_variant_currency_region")
  // Database optimizations
  @@index([variantId], name: "idx_price_variant")
  @@index([currencyCode], name: "idx_price_currency")
  @@index([amount], name: "idx_price_amount")
  @@map("product_prices")
}

/// Product tags for categorization and search
model ProductTag {
  // Primary fields
  id    String @id @default(cuid()) @map("id")
  value String @map("value")

  // Product relationship
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([productId, value], name: "unique_product_tag")
  // Database optimizations
  @@index([productId], name: "idx_tag_product")
  @@index([value], name: "idx_tag_value")
  @@map("product_tags")
}

/// Product images with ordering and metadata
model ProductImage {
  // Primary fields
  id  String @id @default(cuid()) @map("id")
  url String @map("url")

  // Product relationship
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Image properties
  altText   String? @map("alt_text")
  caption   String? @map("caption")
  sortOrder Int     @default(0) @map("sort_order")
  isMain    Boolean @default(false) @map("is_main")

  // Image metadata
  width    Int?    @map("width")
  height   Int?    @map("height")
  fileSize Int?    @map("file_size")
  mimeType String? @map("mime_type")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Database optimizations
  @@index([productId], name: "idx_image_product")
  @@index([sortOrder], name: "idx_image_sort")
  @@index([isMain], name: "idx_image_main")
  @@map("product_images")
}

/// Product variant options (size, color, etc.)
model ProductVariantOption {
  // Primary fields
  id       String @id @default(cuid()) @map("id")
  optionId String @map("option_id")
  value    String @map("value")

  // Variant relationship
  variantId String         @map("variant_id")
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  // Option relationship
  option ProductOption @relation(fields: [optionId], references: [id], onDelete: Cascade)

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([variantId, optionId], name: "unique_variant_option")
  // Database optimizations
  @@index([variantId], name: "idx_variant_option_variant")
  @@index([optionId], name: "idx_variant_option_option")
  @@map("product_variant_options")
}

/// Product options definition (size, color, material, etc.)
model ProductOption {
  // Primary fields
  id    String @id @default(cuid()) @map("id")
  title String @map("title")

  // Product relationship
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Option properties
  position Int     @default(0) @map("position")
  required Boolean @default(false) @map("required")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  variantOptions ProductVariantOption[]
  values         ProductOptionValue[]

  // Database optimizations
  @@index([productId], name: "idx_option_product")
  @@index([position], name: "idx_option_position")
  @@map("product_options")
}

/// Product option values (Small, Medium, Large for size option)
model ProductOptionValue {
  // Primary fields
  id    String @id @default(cuid()) @map("id")
  value String @map("value")

  // Option relationship
  optionId String        @map("option_id")
  option   ProductOption @relation(fields: [optionId], references: [id], onDelete: Cascade)

  // Value properties
  position  Int     @default(0) @map("position")
  isDefault Boolean @default(false) @map("is_default")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([optionId, value], name: "unique_option_value")
  // Database optimizations
  @@index([optionId], name: "idx_option_value_option")
  @@index([position], name: "idx_option_value_position")
  @@map("product_option_values")
}

// ============================================================================
// ADDITIONAL E-COMMERCE MODELS
// ============================================================================

/// Product collections for grouping and marketing
model Collection {
  // Primary fields
  id          String  @id @default(cuid()) @map("id")
  title       String  @map("title")
  description String? @map("description")
  handle      String  @unique @map("handle")

  // Collection properties
  isActive   Boolean @default(true) @map("is_active")
  isFeatured Boolean @default(false) @map("is_featured")
  sortOrder  Int     @default(0) @map("sort_order")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  products CollectionProduct[]

  // Database optimizations
  @@index([handle], name: "idx_collection_handle")
  @@index([isActive], name: "idx_collection_active")
  @@index([isFeatured], name: "idx_collection_featured")
  @@map("collections")
}

/// Many-to-many relationship between collections and products
model CollectionProduct {
  // Primary fields
  id           String @id @default(cuid()) @map("id")
  collectionId String @map("collection_id")
  productId    String @map("product_id")

  // Relationships
  collection Collection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  product    Product    @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Position in collection
  position Int @default(0) @map("position")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([collectionId, productId], name: "unique_collection_product")
  // Database optimizations
  @@index([collectionId], name: "idx_collection_product_collection")
  @@index([productId], name: "idx_collection_product_product")
  @@index([position], name: "idx_collection_product_position")
  @@map("collection_products")
}

// ============================================================================
// ONDC INTEGRATION MODELS
// ============================================================================

/// ONDC catalog synchronization tracking
model OndcCatalogSync {
  // Primary fields
  id        String @id @default(cuid()) @map("id")
  catalogId String @map("catalog_id")

  // Sync status
  status     String    @map("status") // 'pending', 'syncing', 'completed', 'failed'
  lastSyncAt DateTime? @map("last_sync_at")
  nextSyncAt DateTime? @map("next_sync_at")

  // Sync statistics
  totalProducts  Int @default(0) @map("total_products")
  syncedProducts Int @default(0) @map("synced_products")
  failedProducts Int @default(0) @map("failed_products")

  // Error tracking
  errorMessage String? @map("error_message")
  errorDetails Json?   @map("error_details")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Database optimizations
  @@index([catalogId], name: "idx_ondc_sync_catalog")
  @@index([status], name: "idx_ondc_sync_status")
  @@index([lastSyncAt], name: "idx_ondc_sync_last")
  @@map("ondc_catalog_syncs")
}

// ============================================================================
// AUDIT AND SYSTEM MODELS
// ============================================================================

/// System audit log for tracking changes
model AuditLog {
  // Primary fields
  id         String @id @default(cuid()) @map("id")
  entityType String @map("entity_type") // 'product', 'category', 'inventory', etc.
  entityId   String @map("entity_id")
  action     String @map("action") // 'create', 'update', 'delete'

  // Change tracking
  oldValues Json? @map("old_values")
  newValues Json? @map("new_values")
  changes   Json? @map("changes")

  // User and session tracking
  userId    String? @map("user_id")
  sessionId String? @map("session_id")
  ipAddress String? @map("ip_address")
  userAgent String? @map("user_agent")

  // Metadata and timestamps
  metadata  Json?    @map("metadata")
  createdAt DateTime @default(now()) @map("created_at")

  // Database optimizations
  @@index([entityType], name: "idx_audit_entity_type")
  @@index([entityId], name: "idx_audit_entity_id")
  @@index([action], name: "idx_audit_action")
  @@index([userId], name: "idx_audit_user")
  @@index([createdAt], name: "idx_audit_created")
  @@map("audit_logs")
}
