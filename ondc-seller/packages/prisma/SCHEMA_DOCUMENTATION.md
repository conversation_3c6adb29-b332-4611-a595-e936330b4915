# ONDC Seller Platform - Prisma Schema Documentation

## Overview

This comprehensive Prisma schema is designed for the ONDC (Open Network for Digital Commerce) seller platform, following **Medusa Commerce patterns** with full **ONDC protocol integration**. The schema supports a complete e-commerce ecosystem with advanced inventory management, multi-currency pricing, and hierarchical categorization.

## 🏗️ Architecture

### Core Design Principles

1. **Medusa Commerce Compatibility**: Full compatibility with Medusa.js data structures
2. **ONDC Integration**: Native support for ONDC protocol fields and synchronization
3. **Scalable Relationships**: Optimized foreign keys and cascade behaviors
4. **Performance Optimized**: Strategic database indexes on frequently queried fields
5. **Audit Trail**: Complete change tracking and system logging
6. **Multi-tenancy Ready**: Extensible for multiple seller support

### Database Optimizations

- **Indexes**: Strategic indexes on `handle`, `sku`, `status`, and relationship fields
- **Cascade Deletes**: Proper cleanup when parent entities are removed
- **Unique Constraints**: Prevent duplicate handles, SKUs, and relationship combinations
- **JSON Metadata**: Flexible metadata storage for extensibility

## 📊 Core Models

### 1. Category Model
```prisma
model Category {
  id          String   @id @default(cuid())
  title       String
  description String?
  handle      String   @unique
  isActive    Boolean  @default(true)
  
  // Hierarchical support
  parentCategoryId String?
  parentCategory   Category?
  childCategories  Category[]
  
  // Relationships
  products Product[]
}
```

**Features:**
- ✅ Hierarchical categories (parent/child relationships)
- ✅ URL-friendly handles for SEO
- ✅ Active/inactive status management
- ✅ Flexible metadata storage

**Use Cases:**
- Electronics → Smartphones → iPhone
- Fashion → Men's Clothing → Shirts
- Home & Garden → Kitchen → Appliances

### 2. Product Model
```prisma
model Product {
  id          String        @id @default(cuid())
  title       String
  description String?
  handle      String        @unique
  status      ProductStatus @default(DRAFT)
  thumbnail   String?
  categoryId  String
  
  // Medusa-compatible fields
  subtitle      String?
  isGiftcard    Boolean  @default(false)
  discountable  Boolean  @default(true)
  
  // ONDC integration
  ondcCategoryId String?
  ondcItemId     String?
  
  // Relationships
  variants    ProductVariant[]
  tags        ProductTag[]
  images      ProductImage[]
  options     ProductOption[]
  collections CollectionProduct[]
}
```

**Features:**
- ✅ Draft/Published/Archived status workflow
- ✅ SEO-optimized handles
- ✅ Medusa Commerce compatibility
- ✅ ONDC protocol integration
- ✅ Rich metadata support

**Status Workflow:**
1. `DRAFT` → Product being created/edited
2. `PUBLISHED` → Live on storefront
3. `ARCHIVED` → Hidden but preserved

### 3. ProductVariant Model
```prisma
model ProductVariant {
  id      String  @id @default(cuid())
  title   String
  sku     String  @unique
  barcode String?
  productId String
  
  // Pricing
  price         Decimal @db.Decimal(10, 2)
  compareAtPrice Decimal? @db.Decimal(10, 2)
  costPrice     Decimal? @db.Decimal(10, 2)
  
  // Physical properties
  weight Float?
  length Float?
  height Float?
  width  Float?
  
  // Inventory flags
  allowBackorder  Boolean @default(false)
  manageInventory Boolean @default(true)
  
  // ONDC integration
  ondcVariantId String?
  
  // Relationships
  inventoryItem InventoryItem?
  prices        ProductPrice[]
  options       ProductVariantOption[]
}
```

**Features:**
- ✅ Unique SKU management
- ✅ Multi-tier pricing (base, compare, cost)
- ✅ Physical dimension tracking
- ✅ Inventory management controls
- ✅ ONDC variant synchronization

**Pricing Strategy:**
- `price`: Current selling price
- `compareAtPrice`: Original/MSRP price (for discounts)
- `costPrice`: Internal cost (for profit calculations)

### 4. InventoryItem Model
```prisma
model InventoryItem {
  id        String @id @default(cuid())
  variantId String @unique
  
  // Quantities
  quantity         Int @default(0)
  reservedQuantity Int @default(0)
  availableQuantity Int @default(0)
  
  // Management
  allowBackorder      Boolean @default(false)
  lowStockThreshold   Int?
  status             InventoryStatus @default(IN_STOCK)
  
  // Location tracking
  locationId   String?
  warehouseId  String?
  
  // ONDC integration
  ondcInventoryId String?
}
```

**Features:**
- ✅ Real-time stock tracking
- ✅ Reserved quantity management
- ✅ Low stock alerts
- ✅ Multi-location support
- ✅ Backorder handling

**Inventory Status:**
- `IN_STOCK`: Available for purchase
- `LOW_STOCK`: Below threshold
- `OUT_OF_STOCK`: Zero available
- `DISCONTINUED`: No longer sold

## 🔗 Relationship Patterns

### One-to-Many Relationships
```
Category (1) → Products (N)
Product (1) → ProductVariants (N)
Product (1) → ProductTags (N)
Product (1) → ProductImages (N)
ProductVariant (1) → ProductPrices (N)
```

### One-to-One Relationships
```
ProductVariant (1) ↔ InventoryItem (1)
```

### Many-to-Many Relationships
```
Products (N) ↔ Collections (N) via CollectionProduct
ProductVariants (N) ↔ ProductOptions (N) via ProductVariantOption
```

### Hierarchical Relationships
```
Category (1) → ChildCategories (N) [Self-referencing]
```

## 🎯 ONDC Integration

### ONDC-Specific Fields
- `ondcCategoryId`: Maps to ONDC category taxonomy
- `ondcItemId`: Unique ONDC item identifier
- `ondcVariantId`: ONDC variant identifier
- `ondcInventoryId`: ONDC inventory tracking

### Synchronization Model
```prisma
model OndcCatalogSync {
  id        String @id @default(cuid())
  catalogId String
  status    String // 'pending', 'syncing', 'completed', 'failed'
  
  // Statistics
  totalProducts   Int @default(0)
  syncedProducts  Int @default(0)
  failedProducts  Int @default(0)
  
  // Error tracking
  errorMessage String?
  errorDetails Json?
}
```

## 🔍 Performance Optimizations

### Strategic Indexes
```sql
-- Frequently queried fields
CREATE INDEX idx_product_handle ON products(handle);
CREATE INDEX idx_product_status ON products(status);
CREATE INDEX idx_variant_sku ON product_variants(sku);
CREATE INDEX idx_inventory_quantity ON inventory_items(quantity);

-- Relationship indexes
CREATE INDEX idx_product_category ON products(category_id);
CREATE INDEX idx_variant_product ON product_variants(product_id);
CREATE INDEX idx_inventory_variant ON inventory_items(variant_id);

-- ONDC integration indexes
CREATE INDEX idx_product_ondc_item ON products(ondc_item_id);
CREATE INDEX idx_variant_ondc_variant ON product_variants(ondc_variant_id);
```

### Unique Constraints
```sql
-- Prevent duplicates
UNIQUE(handle) ON products
UNIQUE(sku) ON product_variants
UNIQUE(variant_id) ON inventory_items
UNIQUE(product_id, value) ON product_tags
UNIQUE(collection_id, product_id) ON collection_products
```

## 🛡️ Data Integrity

### Cascade Behaviors
- **Category deletion**: Cascades to products (with warning)
- **Product deletion**: Cascades to variants, tags, images, options
- **Variant deletion**: Cascades to inventory, prices, options
- **Collection deletion**: Cascades to collection-product relationships

### Validation Rules
- SKUs must be unique across all variants
- Handles must be URL-safe and unique
- Inventory quantities cannot be negative
- Prices must be positive decimals

## 📈 Scalability Considerations

### Multi-Currency Support
```prisma
model ProductPrice {
  variantId    String
  currencyCode String
  amount       Decimal
  regionId     String?
  
  @@unique([variantId, currencyCode, regionId])
}
```

### Multi-Location Inventory
```prisma
model InventoryItem {
  locationId   String?
  warehouseId  String?
  // ... other fields
}
```

### Audit Trail
```prisma
model AuditLog {
  entityType String // 'product', 'category', 'inventory'
  entityId   String
  action     String // 'create', 'update', 'delete'
  oldValues  Json?
  newValues  Json?
  userId     String?
  createdAt  DateTime @default(now())
}
```

## 🚀 Usage Examples

### Creating a Complete Product
```typescript
const product = await prisma.product.create({
  data: {
    title: "iPhone 15 Pro",
    handle: "iphone-15-pro",
    status: "PUBLISHED",
    categoryId: "smartphone-category-id",
    variants: {
      create: [
        {
          title: "iPhone 15 Pro - 128GB",
          sku: "IPHONE-15-PRO-128GB",
          price: 99900.00,
          inventoryItem: {
            create: {
              quantity: 50,
              lowStockThreshold: 10
            }
          }
        }
      ]
    }
  }
});
```

### Complex Query with Relations
```typescript
const productWithEverything = await prisma.product.findUnique({
  where: { handle: "iphone-15-pro" },
  include: {
    category: { include: { parentCategory: true } },
    variants: { include: { inventoryItem: true, prices: true } },
    tags: true,
    images: true,
    collections: { include: { collection: true } }
  }
});
```

## 🔧 Migration Strategy

1. **Initial Setup**: Run `prisma migrate dev --name init`
2. **Schema Updates**: Use `prisma migrate dev --name <description>`
3. **Production**: Use `prisma migrate deploy`
4. **Data Seeding**: Use custom seed scripts for initial data

## 📝 Best Practices

1. **Always use transactions** for multi-table operations
2. **Validate SKUs** before creating variants
3. **Handle inventory updates** atomically
4. **Use soft deletes** for important entities
5. **Monitor query performance** with database logs
6. **Regular backups** before major migrations

This schema provides a robust foundation for the ONDC seller platform with room for future enhancements and integrations.
